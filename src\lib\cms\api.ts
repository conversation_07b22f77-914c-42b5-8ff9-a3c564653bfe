import type {
  CMSProject,
  CMSSuccessStory,
  CMSResource,
  CMSNews,
  CMSMediaItem,
  CMSInvestmentOpportunity,
  CMSQueryParams,
  CMSListResponse,
  CMSResponse,
} from './types'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

class CMSApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
  ) {
    super(message)
    this.name = 'CMSApiError'
  }
}

async function fetchAPI<T>(
  endpoint: string,
  options: RequestInit = {},
): Promise<T> {
  const url = `${API_BASE_URL}/api${endpoint}`
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new CMSApiError(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData.code,
      )
    }

    return await response.json()
  } catch (error) {
    if (error instanceof CMSApiError) {
      throw error
    }
    throw new CMSApiError(
      error instanceof Error ? error.message : 'Unknown error occurred',
    )
  }
}

function buildQueryString(params: CMSQueryParams): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

// Projects API
export const projectsAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSProject>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ projects: CMSProject[]; totalProjects: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/projects${queryString}`)
    
    return {
      data: response.projects,
      totalDocs: response.totalProjects,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSProject>> {
    const response = await fetchAPI<{ project: CMSProject }>(`/projects/${id}`)
    return { data: response.project }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSProject>> {
    return this.getAll({ featured: true, limit })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSProject>> {
    return this.getAll({ ...params, category })
  },
}

// Success Stories API
export const successStoriesAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSSuccessStory>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ stories: CMSSuccessStory[]; totalStories: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/success-stories${queryString}`)
    
    return {
      data: response.stories,
      totalDocs: response.totalStories,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSSuccessStory>> {
    const response = await fetchAPI<{ story: CMSSuccessStory }>(`/success-stories/${id}`)
    return { data: response.story }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSSuccessStory>> {
    return this.getAll({ featured: true, limit })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSSuccessStory>> {
    return this.getAll({ ...params, category })
  },
}

// Resources API
export const resourcesAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSResource>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ resources: CMSResource[]; totalResources: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/resources${queryString}`)
    
    return {
      data: response.resources,
      totalDocs: response.totalResources,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSResource>> {
    const response = await fetchAPI<{ resource: CMSResource }>(`/resources/${id}`)
    return { data: response.resource }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSResource>> {
    return this.getAll({ featured: true, limit })
  },

  async getByType(type: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSResource>> {
    return this.getAll({ ...params, type })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSResource>> {
    return this.getAll({ ...params, category })
  },

  async download(id: string): Promise<{ downloadUrl: string; filename?: string; filesize?: number; mimeType?: string }> {
    return await fetchAPI<{ downloadUrl: string; filename?: string; filesize?: number; mimeType?: string }>(`/resources/${id}/download`)
  },
}

// News API
export const newsAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSNews>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ articles: CMSNews[]; totalArticles: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/news${queryString}`)
    
    return {
      data: response.articles,
      totalDocs: response.totalArticles,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSNews>> {
    const response = await fetchAPI<{ article: CMSNews }>(`/news/${id}`)
    return { data: response.article }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSNews>> {
    return this.getAll({ featured: true, limit })
  },

  async getUrgent(limit = 3): Promise<CMSListResponse<CMSNews>> {
    return this.getAll({ urgent: true, limit })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSNews>> {
    return this.getAll({ ...params, category })
  },
}

// Media Gallery API
export const mediaGalleryAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSMediaItem>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ mediaItems: CMSMediaItem[]; totalItems: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/media-gallery${queryString}`)
    
    return {
      data: response.mediaItems,
      totalDocs: response.totalItems,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSMediaItem>> {
    const response = await fetchAPI<{ mediaItem: CMSMediaItem }>(`/media-gallery/${id}`)
    return { data: response.mediaItem }
  },

  async getFeatured(limit = 12): Promise<CMSListResponse<CMSMediaItem>> {
    return this.getAll({ featured: true, limit })
  },

  async getByType(type: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSMediaItem>> {
    return this.getAll({ ...params, type })
  },

  async getByCategory(category: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSMediaItem>> {
    return this.getAll({ ...params, category })
  },
}

// Investment Opportunities API
export const investmentOpportunitiesAPI = {
  async getAll(params: CMSQueryParams = {}): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    const queryString = buildQueryString(params)
    const response = await fetchAPI<{ opportunities: CMSInvestmentOpportunity[]; totalOpportunities: number; page: number; limit: number; totalPages: number; hasNextPage: boolean; hasPrevPage: boolean }>(`/investment-opportunities${queryString}`)
    
    return {
      data: response.opportunities,
      totalDocs: response.totalOpportunities,
      page: response.page,
      limit: response.limit,
      totalPages: response.totalPages,
      hasNextPage: response.hasNextPage,
      hasPrevPage: response.hasPrevPage,
    }
  },

  async getById(id: string): Promise<CMSResponse<CMSInvestmentOpportunity>> {
    const response = await fetchAPI<{ opportunity: CMSInvestmentOpportunity }>(`/investment-opportunities/${id}`)
    return { data: response.opportunity }
  },

  async getFeatured(limit = 6): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    return this.getAll({ featured: true, limit })
  },

  async getUrgent(limit = 3): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    return this.getAll({ urgent: true, limit })
  },

  async getBySector(sector: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    return this.getAll({ ...params, sector })
  },

  async getByInvestmentType(investmentType: string, params: CMSQueryParams = {}): Promise<CMSListResponse<CMSInvestmentOpportunity>> {
    return this.getAll({ ...params, investmentType })
  },
}

// Contact Submissions API (for public contact form)
export const contactAPI = {
  async submit(data: {
    name: string
    email: string
    phone?: string
    organization?: string
    role?: string
    subject: string
    category: string
    message: string
    location?: {
      county?: string
      city?: string
      country?: string
    }
    priority?: string
  }): Promise<{ success: boolean; message: string; submissionId: string; status: string }> {
    return await fetchAPI<{ success: boolean; message: string; submissionId: string; status: string }>('/contact-submissions', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },
}

// Export all APIs
export const cmsAPI = {
  projects: projectsAPI,
  successStories: successStoriesAPI,
  resources: resourcesAPI,
  news: newsAPI,
  mediaGallery: mediaGalleryAPI,
  investmentOpportunities: investmentOpportunitiesAPI,
  contact: contactAPI,
}

export { CMSApiError }
