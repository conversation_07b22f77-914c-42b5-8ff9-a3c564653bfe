# County-User Relationship API

## 🔗 **Relationship Overview**

I've added a county relationship to the Users collection, allowing users to be associated with specific counties. This creates a one-to-many relationship where:
- **One County** can have **many Users**
- **One User** belongs to **one County** (optional)

## 📊 **Updated User Collection**

### **New Field Added:**
```typescript
{
  name: 'county',
  type: 'relationship',
  relationTo: 'counties',
  label: 'County',
  admin: {
    description: 'The county this user is associated with',
  },
}
```

### **Admin Panel Updates:**
- County field now appears in user creation/edit forms
- County column added to users list view
- Dropdown selection of available counties

## 🔍 **Query Users by County (Using Filters)**

Instead of creating a separate endpoint, use the existing `/api/users` endpoint with filters to query users by county.

### **1. Get Users in Specific County**
```bash
# Get users in county with ID 1
curl "http://localhost:3000/api/users?where[county][equals]=1"

# Get users in county with ID 1, include county data
curl "http://localhost:3000/api/users?where[county][equals]=1&depth=1"

# Get users in county with pagination
curl "http://localhost:3000/api/users?where[county][equals]=1&limit=10&page=1"
```

### **2. Get Users in Multiple Counties**
```bash
# Get users in counties 1, 2, or 3
curl "http://localhost:3000/api/users?where[county][in]=1,2,3"

# Get users in counties 1, 2, or 3 with county data
curl "http://localhost:3000/api/users?where[county][in]=1,2,3&depth=1"
```

### **3. Get Users Without County**
```bash
# Get users who don't have a county assigned
curl "http://localhost:3000/api/users?where[county][exists]=false"

# Get users who have any county assigned
curl "http://localhost:3000/api/users?where[county][exists]=true"
```

### **4. Complex County Filters**
```bash
# Get users in county 1 with name containing "John"
curl "http://localhost:3000/api/users?where[county][equals]=1&where[name][contains]=John"

# Get users in county 1, sorted by name
curl "http://localhost:3000/api/users?where[county][equals]=1&sort=name"

# Get users in county 1, only return specific fields
curl "http://localhost:3000/api/users?where[county][equals]=1&select=name,email,county"
```

### **5. Search by County Properties**
```bash
# Get users in counties with specific name (requires depth=1)
curl "http://localhost:3000/api/users?where[county.name][equals]=Nairobi&depth=1"

# Get users in counties with specific code
curl "http://localhost:3000/api/users?where[county.code][equals]=KE-047&depth=1"

# Get users in active counties only
curl "http://localhost:3000/api/users?where[county.isActive][equals]=true&depth=1"
```

## 📊 **Response Examples**

### **Users in County Response:**
```json
{
  "docs": [
    {
      "id": "1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "county": {
        "id": "1",
        "name": "Nairobi",
        "code": "KE-047",
        "coordinates": {
          "latitude": -1.2921,
          "longitude": 36.8219
        },
        "description": "Kenya's capital city",
        "isActive": true
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalDocs": 1,
  "limit": 10,
  "totalPages": 1,
  "page": 1,
  "pagingCounter": 1,
  "hasPrevPage": false,
  "hasNextPage": false
}
```

### **Users Without County Data (depth=0):**
```json
{
  "docs": [
    {
      "id": "1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "county": "1",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalDocs": 1
}
```

## 🧪 **CRUD Operations with County Relationship**

### **Create User with County:**
```bash
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "John Doe",
    "county": 1
  }'
```

### **Update User's County:**
```bash
curl -X PUT http://localhost:3000/api/users/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "county": 2
  }'
```

### **Remove User's County:**
```bash
curl -X PUT http://localhost:3000/api/users/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "county": null
  }'
```

### **Get User with County Data:**
```bash
# Get user with populated county information
curl "http://localhost:3000/api/users/1?depth=1"
```

**Response:**
```json
{
  "id": "1",
  "name": "John Doe",
  "email": "<EMAIL>",
  "county": {
    "id": "1",
    "name": "Nairobi",
    "code": "KE-047",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    },
    "description": "Kenya's capital city",
    "isActive": true
  },
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

## 📋 **Complete Examples**

### **Workflow: Create County and Assign Users**

```bash
# Step 1: Login to get token
TOKEN=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  | jq -r '.token')

# Step 2: Create a county
COUNTY_RESPONSE=$(curl -s -X POST http://localhost:3000/api/counties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Nairobi",
    "code": "KE-047",
    "coordinates": {"latitude": -1.2921, "longitude": 36.8219},
    "description": "Kenya'\''s capital city"
  }')

COUNTY_ID=$(echo $COUNTY_RESPONSE | jq -r '.county.id')
echo "Created County ID: $COUNTY_ID"

# Step 3: Create users and assign to county
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Alice Johnson",
    "county": '$COUNTY_ID'
  }'

curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Bob Smith",
    "county": '$COUNTY_ID'
  }'

# Step 4: Get all users in the county using filters
curl "http://localhost:3000/api/users?where[county][equals]=$COUNTY_ID&depth=1"
```

### **Get County Statistics:**
```bash
# Get users in county and extract statistics
curl -s "http://localhost:3000/api/users?where[county][equals]=1&depth=1" | jq '{
  userCount: .totalDocs,
  userNames: [.docs[].name],
  countyName: .docs[0].county.name
}'

# Get user count per county (requires multiple requests)
for county_id in 1 2 3; do
  count=$(curl -s "http://localhost:3000/api/users?where[county][equals]=$county_id" | jq '.totalDocs')
  echo "County $county_id: $count users"
done
```

## 🎯 **Use Cases**

1. **Geographic User Management**: Organize users by their location
2. **Regional Administration**: Assign county-specific administrators
3. **Location-based Services**: Provide county-specific content/services
4. **Analytics**: Generate reports by county
5. **User Segmentation**: Group users for targeted communications

## 🔐 **Security Considerations**

### **Access Control:**
- **County-User endpoint**: Public read access (no sensitive data exposed)
- **User creation**: Public (for registration)
- **User updates**: Authenticated users only
- **Email addresses**: Included in county users endpoint (consider if this should be restricted)

### **Privacy Options:**
If you want to hide email addresses from the county users endpoint, update the transform function:

```typescript
// In countyUsersHandler, remove email from response:
const transformedUsers = usersResult.docs.map((user: any) => ({
  id: user.id,
  name: user.name,
  // email: user.email, // Remove this line
  county: user.county ? {
    id: user.county.id,
    name: user.county.name,
    code: user.county.code,
  } : null,
  createdAt: user.createdAt,
  updatedAt: user.updatedAt,
}))
```

## 📊 **Admin Panel Features**

### **User Management:**
- County dropdown in user creation form
- County column in users list
- Filter users by county
- Bulk assign users to counties

### **County Management:**
- View user count per county
- Access county users directly from county edit page

## 🧪 **Testing the Relationship**

```bash
# Test script
echo "Testing County-User Relationship"

# 1. Create a county
echo "Creating county..."
COUNTY=$(curl -s -X POST http://localhost:3000/api/counties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name":"Test County","code":"TEST-001","coordinates":{"latitude":0,"longitude":0}}')

COUNTY_ID=$(echo $COUNTY | jq -r '.county.id')
echo "County ID: $COUNTY_ID"

# 2. Create users with county
echo "Creating users..."
curl -s -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"<EMAIL>\",\"password\":\"password123\",\"name\":\"Test User 1\",\"county\":$COUNTY_ID}"

curl -s -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"<EMAIL>\",\"password\":\"password123\",\"name\":\"Test User 2\",\"county\":$COUNTY_ID}"

# 3. Get users in county
echo "Getting users in county..."
curl -s "http://localhost:3000/api/counties/$COUNTY_ID/users" | jq '.'
```

The county-user relationship is now fully implemented with CRUD operations and a dedicated endpoint to retrieve users by county! 🎉
