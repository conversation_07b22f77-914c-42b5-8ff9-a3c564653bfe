'use client'

import React from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  MapPin,
  Calendar,
  Users,
  TrendingUp,
  Quote,
  Heart,
  Star,
  CheckCircle,
} from 'lucide-react'

interface NPISuccessStoryDetailsClientProps {
  story: any // Use any for now to handle Payload CMS response structure
}

// Helper function to extract text from Lexical format
const extractTextFromLexical = (lexicalData: any): string => {
  if (!lexicalData || !lexicalData.root || !lexicalData.root.children) {
    return ''
  }

  const extractText = (node: any): string => {
    if (node.type === 'text') {
      return node.text || ''
    }
    if (node.children && Array.isArray(node.children)) {
      return node.children.map(extractText).join('')
    }
    return ''
  }

  return lexicalData.root.children.map(extractText).join('\n')
}

export default function NPISuccessStoryDetailsClient({ story }: NPISuccessStoryDetailsClientProps) {
  // Extract content text from Lexical format
  const contentText = extractTextFromLexical(story.content) || story.summary || 'No content available.'

  // Handle image URL
  const imageUrl = story.image?.url || '/assets/product 1.jpg'

  // Safe access to story properties
  const storyTitle = story.title || 'Untitled Success Story'
  const storyCategory = story.category || 'general'
  const storyLocation = story.location?.specificLocation || story.location?.county?.name || 'Kenya'

  return (
    <div className="min-h-screen bg-[#FFFFFF]">
      {/* Hero Section */}
      <NPISection className="relative min-h-[70vh] bg-[#FFFFFF]" container={false}>
        <div className="relative h-[70vh] w-full">
          <Image
            src={imageUrl}
            alt={story.image?.alt || storyTitle}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

          {/* Back Button */}
          <div className="absolute top-6 left-6 z-10">
            <Link href="/success-stories">
              <NPIButton
                variant="outline"
                className="bg-white/90 hover:bg-white text-black border-white/20 hover:border-white"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Success Stories
              </NPIButton>
            </Link>
          </div>

          {/* Hero Content */}
          <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
            <div className="container mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <div className="flex items-center gap-2 mb-4">
                  <span className="bg-[#8A3E25] px-3 py-1 text-sm font-medium">
                    {storyCategory}
                  </span>
                  <span className="flex items-center gap-1 text-sm">
                    <MapPin className="w-4 h-4" />
                    {storyLocation}
                  </span>
                </div>
                <h1 className="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
                  {storyTitle}
                </h1>
                <p className="text-xl text-white/90 max-w-3xl">
                  {story.summary}
                </p>
              </motion.div>
            </div>
          </div>
        </div>
      </NPISection>

      {/* Main Content */}
      <NPISection className="py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <NPICard className="bg-[#FFFFFF] border-2 border-[#725242]/20">
                <NPICardHeader>
                  <NPICardTitle className="text-3xl text-black mb-6 flex items-center gap-2">
                    <Heart className="w-8 h-8 text-[#8A3E25]" />
                    The Story
                  </NPICardTitle>
                </NPICardHeader>
                <NPICardContent>
                  <div className="prose prose-lg max-w-none">
                    <div className="text-[#725242] leading-relaxed whitespace-pre-line">
                      {contentText}
                    </div>
                  </div>
                </NPICardContent>
              </NPICard>
            </motion.div>

            {/* Testimonials */}
            {story.testimonials && story.testimonials.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="mt-8"
              >
                <NPICard className="bg-[#EFE3BA] border-2 border-[#725242]/20">
                  <NPICardHeader>
                    <NPICardTitle className="text-2xl text-black mb-6 flex items-center gap-2">
                      <Quote className="w-6 h-6 text-[#8A3E25]" />
                      What People Say
                    </NPICardTitle>
                  </NPICardHeader>
                  <NPICardContent>
                    <div className="space-y-6">
                      {story.testimonials.map((testimonial: any, index: number) => (
                        <blockquote key={index} className="border-l-4 border-[#25718A] pl-6">
                          <p className="text-[#725242] italic mb-3 text-lg">
                            &ldquo;{testimonial.quote}&rdquo;
                          </p>
                          <footer className="flex items-center gap-3">
                            {testimonial.photo?.url && (
                              <Image
                                src={testimonial.photo.url}
                                alt={testimonial.author}
                                width={48}
                                height={48}
                                className="rounded-full object-cover"
                              />
                            )}
                            <div>
                              <strong className="text-black font-medium">
                                {testimonial.author}
                              </strong>
                              <p className="text-[#725242] text-sm">
                                {testimonial.role}
                                {testimonial.organization && `, ${testimonial.organization}`}
                              </p>
                            </div>
                          </footer>
                        </blockquote>
                      ))}
                    </div>
                  </NPICardContent>
                </NPICard>
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Story Details */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <NPICard className="bg-[#FFFFFF] border-2 border-[#725242]/20 mb-8">
                <NPICardHeader>
                  <NPICardTitle className="text-xl text-black mb-4">
                    Story Details
                  </NPICardTitle>
                </NPICardHeader>
                <NPICardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <MapPin className="w-5 h-5 text-[#8A3E25]" />
                      <div>
                        <p className="font-medium text-black">Location</p>
                        <p className="text-[#725242] text-sm">{storyLocation}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <Calendar className="w-5 h-5 text-[#8A3E25]" />
                      <div>
                        <p className="font-medium text-black">Timeline</p>
                        <p className="text-[#725242] text-sm">
                          {story.timeline?.startDate ? new Date(story.timeline.startDate).getFullYear() : 'Ongoing'}
                          {story.timeline?.completionDate && ` - ${new Date(story.timeline.completionDate).getFullYear()}`}
                        </p>
                      </div>
                    </div>

                    {story.participants?.beneficiary && (
                      <div className="flex items-center gap-3">
                        <Users className="w-5 h-5 text-[#8A3E25]" />
                        <div>
                          <p className="font-medium text-black">Key Participant</p>
                          <p className="text-[#725242] text-sm">
                            {story.participants.beneficiary.name}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </NPICardContent>
              </NPICard>
            </motion.div>

            {/* Impact Metrics */}
            {story.impact && (
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <NPICard className="bg-[#EFE3BA] border-2 border-[#725242]/20">
                  <NPICardHeader>
                    <NPICardTitle className="text-xl text-black mb-4 flex items-center gap-2">
                      <TrendingUp className="w-5 h-5 text-[#8A3E25]" />
                      Impact
                    </NPICardTitle>
                  </NPICardHeader>
                  <NPICardContent>
                    <div className="space-y-4">
                      {story.impact.beneficiaries && (
                        <div className="text-center p-4 bg-white/50 rounded">
                          <div className="text-2xl font-bold text-[#8A3E25] mb-1">
                            {story.impact.beneficiaries.toLocaleString()}
                          </div>
                          <div className="text-sm text-[#725242]">People Impacted</div>
                        </div>
                      )}
                      
                      {story.impact.economicValue && (
                        <div className="text-center p-4 bg-white/50 rounded">
                          <div className="text-2xl font-bold text-[#8A3E25] mb-1">
                            KES {story.impact.economicValue.toLocaleString()}
                          </div>
                          <div className="text-sm text-[#725242]">Economic Value</div>
                        </div>
                      )}
                    </div>
                  </NPICardContent>
                </NPICard>
              </motion.div>
            )}
          </div>
        </div>
      </NPISection>

      {/* Related Stories CTA */}
      <NPISection className="bg-[#EFE3BA] py-16">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold text-black mb-4">
              Discover More Success Stories
            </h2>
            <p className="text-[#725242] mb-8 max-w-2xl mx-auto">
              Explore how NPI initiatives are transforming lives and communities across Kenya.
            </p>
            <NPIButton
              asChild
              size="lg"
              className="bg-[#8A3E25] hover:bg-[#25718A] text-white font-bold px-8 py-3"
            >
              <Link href="/success-stories">
                View All Success Stories
              </Link>
            </NPIButton>
          </motion.div>
        </div>
      </NPISection>
    </div>
  )
}
