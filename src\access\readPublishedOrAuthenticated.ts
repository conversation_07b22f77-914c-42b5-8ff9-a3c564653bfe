import type { AccessArgs } from 'payload'
import type { User } from '@/payload-types'

type ReadPublishedOrAuthenticated = (args: AccessArgs<User>) => boolean

export const readPublishedOrAuthenticated: ReadPublishedOrAuthenticated = ({ req: { user }, data }) => {
  // If user is authenticated, allow access to all content
  if (user) {
    return true
  }

  // For public users, only allow access to published content
  // Check both 'published' field and 'status' field for different content types
  if (data?.published === false) {
    return false
  }

  if (data?.status && data.status !== 'published') {
    return false
  }

  return true
}
