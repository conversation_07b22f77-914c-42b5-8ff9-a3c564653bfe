// <PERSON>rip<PERSON> to create the first admin user (when no admin exists yet)
// This uses PayloadCMS's ability to create the first admin user without authentication
// Run with: node scripts/create-first-admin.js

const BASE_URL = 'http://localhost:3000/api'

// Super Admin user details
const ADMIN_USER = {
  name: 'Super Administrator',
  email: '<EMAIL>',
  password: 'SuperAdmin123!',
  role: 'super-admin'
}

async function checkIfAdminExists() {
  console.log('🔍 Checking if any admin users exist...')
  
  try {
    // Try to get users without authentication - this might work if no users exist yet
    const response = await fetch(`${BASE_URL}/users?limit=1`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.ok) {
      const data = await response.json()
      return data.totalDocs > 0
    }
    
    return true // Assume users exist if we can't check
  } catch (error) {
    console.log('   Could not check existing users (this might be normal)')
    return false
  }
}

async function createFirstAdmin() {
  console.log(`👤 Creating first admin user: ${ADMIN_USER.email}...`)
  
  try {
    // Try to create user without authentication (works for first admin in some PayloadCMS setups)
    const response = await fetch(`${BASE_URL}/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(ADMIN_USER),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(`Failed to create user: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`)
    }

    const data = await response.json()
    console.log('✅ Admin user created successfully!')
    console.log(`📧 Email: ${data.email}`)
    console.log(`👤 Name: ${data.name}`)
    console.log(`🔑 Role: ${data.role}`)
    console.log(`🆔 ID: ${data.id}`)
    
    return data
  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message)
    return null
  }
}

async function createAdminViaFirstUserEndpoint() {
  console.log(`👤 Trying to create admin via first-user endpoint...`)
  
  try {
    // Some PayloadCMS setups have a special endpoint for creating the first admin
    const response = await fetch(`${BASE_URL}/users/first-user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(ADMIN_USER),
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    console.log('✅ Admin user created via first-user endpoint!')
    console.log(`📧 Email: ${data.email}`)
    console.log(`👤 Name: ${data.name}`)
    console.log(`🔑 Role: ${data.role}`)
    console.log(`🆔 ID: ${data.id}`)
    
    return data
  } catch (error) {
    return null
  }
}

async function main() {
  console.log('🚀 First Admin User Creation Script')
  console.log('====================================')
  console.log(`Creating first admin user: ${ADMIN_USER.name} (${ADMIN_USER.email})`)
  console.log('')

  // Method 1: Try the first-user endpoint
  console.log('📝 Method 1: Trying first-user endpoint...')
  let result = await createAdminViaFirstUserEndpoint()
  
  if (result) {
    console.log('\n🎉 Admin user creation completed successfully!')
    console.log('\n📋 Login Details:')
    console.log(`   Email: ${ADMIN_USER.email}`)
    console.log(`   Password: ${ADMIN_USER.password}`)
    console.log(`   Role: ${ADMIN_USER.role}`)
    console.log('\n🔗 You can now login at: http://localhost:3000/admin')
    return
  }

  // Method 2: Try direct user creation
  console.log('📝 Method 2: Trying direct user creation...')
  result = await createFirstAdmin()
  
  if (result) {
    console.log('\n🎉 Admin user creation completed successfully!')
    console.log('\n📋 Login Details:')
    console.log(`   Email: ${ADMIN_USER.email}`)
    console.log(`   Password: ${ADMIN_USER.password}`)
    console.log(`   Role: ${ADMIN_USER.role}`)
    console.log('\n🔗 You can now login at: http://localhost:3000/admin')
    return
  }

  // If both methods failed
  console.log('\n❌ Could not create admin user with either method')
  console.log('\n🔧 Alternative approaches:')
  console.log('1. Visit http://localhost:3000/admin and create the first admin user manually')
  console.log('2. Run the seed script if available: npm run seed')
  console.log('3. Use the create-admin-user.js script if you have existing admin credentials')
  console.log('4. Check if your PayloadCMS setup allows public user registration')
}

// Run the script
main().catch(error => {
  console.error('💥 Script failed:', error)
  process.exit(1)
})
