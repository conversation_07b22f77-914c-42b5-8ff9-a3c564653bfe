import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

type RouteParams = {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    let result

    // First try to find by ID (if it's a valid ObjectId format)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      try {
        result = await payload.findByID({
          collection: 'projects',
          id,
        })
      } catch (error) {
        // If ID lookup fails, try slug lookup
        result = null
      }
    }

    // If no result from ID lookup or ID is not ObjectId format, try slug lookup
    if (!result) {
      const slugResult = await payload.find({
        collection: 'projects',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length > 0) {
        result = slugResult.docs[0]
      }
    }

    if (!result) {
      return NextResponse.json(
        {
          error: 'Project not found',
          message: `Project with ID or slug '${id}' not found`,
        },
        { status: 404 },
      )
    }

    return NextResponse.json({
      success: true,
      project: result,
    })
  } catch (error) {
    console.error('Projects GET by ID error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to fetch project',
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // First find the project to get its actual ID
    let projectId = id

    // If not a valid ObjectId, try to find by slug
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      const slugResult = await payload.find({
        collection: 'projects',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length === 0) {
        return NextResponse.json(
          {
            error: 'Project not found',
            message: `Project with slug '${id}' not found`,
          },
          { status: 404 },
        )
      }

      projectId = slugResult.docs[0].id
    }

    // Handle both JSON and form data
    let body
    const contentType = request.headers.get('content-type') || ''

    if (contentType.includes('multipart/form-data')) {
      const formData = await request.formData()
      const payloadData = formData.get('_payload')

      if (payloadData && typeof payloadData === 'string') {
        body = JSON.parse(payloadData)
      } else {
        return NextResponse.json(
          {
            error: 'Invalid form data',
            message: 'Missing _payload field in form data',
          },
          { status: 400 },
        )
      }
    } else {
      body = await request.json()
    }

    // Update project by actual ID
    const result = await payload.update({
      collection: 'projects',
      id: projectId,
      data: body,
    })

    return NextResponse.json({
      success: true,
      message: 'Project updated successfully',
      project: result,
    })
  } catch (error) {
    console.error('Projects PUT by ID error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to update project',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // First find the project to get its actual ID
    let projectId = id

    // If not a valid ObjectId, try to find by slug
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      const slugResult = await payload.find({
        collection: 'projects',
        where: {
          slug: {
            equals: id,
          },
        },
        limit: 1,
      })

      if (slugResult.docs.length === 0) {
        return NextResponse.json(
          {
            error: 'Project not found',
            message: `Project with slug '${id}' not found`,
          },
          { status: 404 },
        )
      }

      projectId = slugResult.docs[0].id
    }

    // Delete project by actual ID
    await payload.delete({
      collection: 'projects',
      id: projectId,
    })

    return NextResponse.json({
      success: true,
      message: 'Project deleted successfully',
    })
  } catch (error) {
    console.error('Projects DELETE by ID error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to delete project',
      },
      { status: 500 },
    )
  }
}
