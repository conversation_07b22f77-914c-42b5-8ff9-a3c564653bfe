import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const Partnerships: CollectionConfig = {
  slug: 'partnerships',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'type', 'status', 'partner', 'startDate'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Partnership',
    plural: 'Partnerships',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The partnership title or name',
      },
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      admin: {
        description: 'Detailed partnership description',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief partnership summary for cards and previews (max 300 characters)',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Strategic Partnership', value: 'strategic' },
        { label: 'Implementation Partnership', value: 'implementation' },
        { label: 'Funding Partnership', value: 'funding' },
        { label: 'Research Partnership', value: 'research' },
        { label: 'Technology Partnership', value: 'technology' },
        { label: 'Community Partnership', value: 'community' },
        { label: 'Government Partnership', value: 'government' },
        { label: 'Academic Partnership', value: 'academic' },
        { label: 'Private Sector Partnership', value: 'private-sector' },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'active',
      options: [
        { label: 'Proposed', value: 'proposed' },
        { label: 'Under Negotiation', value: 'negotiation' },
        { label: 'Active', value: 'active' },
        { label: 'Completed', value: 'completed' },
        { label: 'On Hold', value: 'on-hold' },
        { label: 'Terminated', value: 'terminated' },
      ],
    },
    {
      name: 'partner',
      type: 'relationship',
      relationTo: 'partners',
      required: true,
      admin: {
        description: 'The partner organization',
      },
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Partnership image or logo',
      },
    },
    {
      name: 'timeline',
      type: 'group',
      fields: [
        {
          name: 'startDate',
          type: 'date',
          required: true,
        },
        {
          name: 'endDate',
          type: 'date',
        },
        {
          name: 'duration',
          type: 'text',
          admin: {
            description: 'Human-readable duration (e.g., "3 years", "Ongoing")',
          },
        },
        {
          name: 'renewalDate',
          type: 'date',
          admin: {
            description: 'Next renewal or review date',
          },
        },
      ],
    },
    {
      name: 'scope',
      type: 'group',
      fields: [
        {
          name: 'objectives',
          type: 'array',
          fields: [
            {
              name: 'objective',
              type: 'text',
              required: true,
            },
          ],
        },
        {
          name: 'activities',
          type: 'array',
          fields: [
            {
              name: 'activity',
              type: 'text',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
            },
            {
              name: 'status',
              type: 'select',
              options: [
                { label: 'Planned', value: 'planned' },
                { label: 'In Progress', value: 'in-progress' },
                { label: 'Completed', value: 'completed' },
                { label: 'Cancelled', value: 'cancelled' },
              ],
            },
          ],
        },
        {
          name: 'deliverables',
          type: 'array',
          fields: [
            {
              name: 'deliverable',
              type: 'text',
              required: true,
            },
            {
              name: 'dueDate',
              type: 'date',
            },
            {
              name: 'completed',
              type: 'checkbox',
              defaultValue: false,
            },
          ],
        },
      ],
    },
    {
      name: 'resources',
      type: 'group',
      fields: [
        {
          name: 'npiContribution',
          type: 'group',
          dbName: 'npi_contrib',
          fields: [
            {
              name: 'financial',
              type: 'number',
              admin: {
                description: 'Financial contribution in KES',
              },
            },
            {
              name: 'inKind',
              type: 'array',
              fields: [
                {
                  name: 'resource',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'value',
                  type: 'number',
                },
              ],
            },
            {
              name: 'personnel',
              type: 'array',
              fields: [
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'role',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'timeCommitment',
                  type: 'text',
                },
              ],
            },
          ],
        },
        {
          name: 'partnerContribution',
          type: 'group',
          dbName: 'partner_contrib',
          fields: [
            {
              name: 'financial',
              type: 'number',
              admin: {
                description: 'Financial contribution in KES',
              },
            },
            {
              name: 'inKind',
              type: 'array',
              fields: [
                {
                  name: 'resource',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'value',
                  type: 'number',
                },
              ],
            },
            {
              name: 'expertise',
              type: 'array',
              fields: [
                {
                  name: 'area',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'description',
                  type: 'textarea',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'impact',
      type: 'group',
      fields: [
        {
          name: 'beneficiaries',
          type: 'number',
          admin: {
            description: 'Number of direct beneficiaries',
          },
        },
        {
          name: 'communities',
          type: 'number',
          admin: {
            description: 'Number of communities reached',
          },
        },
        {
          name: 'counties',
          type: 'relationship',
          relationTo: 'counties',
          hasMany: true,
          admin: {
            description: 'Counties where partnership operates',
          },
        },
        {
          name: 'metrics',
          type: 'array',
          fields: [
            {
              name: 'metric',
              type: 'text',
              required: true,
            },
            {
              name: 'target',
              type: 'text',
            },
            {
              name: 'achieved',
              type: 'text',
            },
            {
              name: 'unit',
              type: 'text',
            },
          ],
        },
        {
          name: 'outcomes',
          type: 'array',
          fields: [
            {
              name: 'outcome',
              type: 'text',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
            },
            {
              name: 'evidence',
              type: 'upload',
              relationTo: 'media',
            },
          ],
        },
      ],
    },
    {
      name: 'governance',
      type: 'group',
      fields: [
        {
          name: 'agreementType',
          type: 'select',
          options: [
            { label: 'Memorandum of Understanding (MOU)', value: 'mou' },
            { label: 'Service Agreement', value: 'service-agreement' },
            { label: 'Grant Agreement', value: 'grant-agreement' },
            { label: 'Joint Venture Agreement', value: 'joint-venture' },
            { label: 'Letter of Intent', value: 'letter-of-intent' },
            { label: 'Other', value: 'other' },
          ],
        },
        {
          name: 'agreementDocument',
          type: 'upload',
          dbName: 'agreement_doc',
          relationTo: 'media',
          admin: {
            description: 'Partnership agreement document',
          },
        },
        {
          name: 'keyContacts',
          type: 'array',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'organization',
              type: 'select',
              options: [
                { label: 'NPI', value: 'npi' },
                { label: 'Partner', value: 'partner' },
              ],
            },
            {
              name: 'email',
              type: 'email',
            },
            {
              name: 'phone',
              type: 'text',
            },
          ],
        },
        {
          name: 'reportingSchedule',
          type: 'select',
          dbName: 'reporting_sched',
          options: [
            { label: 'Monthly', value: 'monthly' },
            { label: 'Quarterly', value: 'quarterly' },
            { label: 'Bi-annually', value: 'bi-annually' },
            { label: 'Annually', value: 'annually' },
            { label: 'As needed', value: 'as-needed' },
          ],
        },
      ],
    },
    {
      name: 'relatedProjects',
      type: 'relationship',
      dbName: 'related_proj',
      relationTo: 'projects',
      hasMany: true,
      admin: {
        description: 'Projects associated with this partnership',
      },
    },
    {
      name: 'documents',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Agreement', value: 'agreement' },
            { label: 'Report', value: 'report' },
            { label: 'Proposal', value: 'proposal' },
            { label: 'Presentation', value: 'presentation' },
            { label: 'Other', value: 'other' },
          ],
        },
        {
          name: 'confidential',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this partnership on homepage and key sections',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this partnership visible to the public',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    ...slugField(),
  ],
}

export default Partnerships
