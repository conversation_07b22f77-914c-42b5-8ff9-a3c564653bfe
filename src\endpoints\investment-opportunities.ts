import type { PayloadRequest } from 'payload'

interface TransformedInvestmentOpportunity {
  id: string
  title: string
  description: string
  summary: string
  sector: string
  investmentType: string
  status: string
  image?: TransformedMedia
  gallery?: TransformedMedia[]
  financial: {
    fundingRequired: number
    currency: string
    fundingStages?: Array<{
      stage: string
      amount: number
      timeline?: string
      milestones?: string
    }>
    useOfFunds?: Array<{
      category: string
      amount: number
      percentage?: number
      description?: string
    }>
    expectedReturns?: {
      roi?: number
      paybackPeriod?: string
      revenueProjections?: Array<{
        year: number
        revenue: number
        profit?: number
      }>
    }
  }
  businessModel?: {
    valueProposition: string
    targetMarket: string
    competitiveAdvantage?: string
    revenueStreams?: Array<{
      stream: string
      description?: string
      projectedRevenue?: number
    }>
    keyPartners?: Array<{
      partner: string
      role: string
      contribution?: string
    }>
  }
  location: {
    counties: TransformedCounty[]
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  timeline?: {
    applicationDeadline?: string
    expectedStartDate?: string
    projectDuration?: string
    milestones?: Array<{
      milestone: string
      targetDate?: string
      description?: string
    }>
  }
  requirements?: {
    investorCriteria?: Array<{
      criterion: string
      description?: string
      mandatory: boolean
    }>
    minimumInvestment?: number
    maximumInvestment?: number
    investorType?: string[]
    documentation?: Array<{
      document: string
      required: boolean
      description?: string
    }>
  }
  impact?: {
    socialImpact?: string
    environmentalImpact?: string
    economicImpact?: string
    beneficiaries?: number
    jobsCreated?: number
    sdgAlignment?: Array<{
      sdg: string
      description?: string
    }>
  }
  team?: {
    projectLead?: {
      name: string
      role: string
      bio?: string
      photo?: TransformedMedia
      email?: string
    }
    keyPersonnel?: Array<{
      name: string
      role: string
      expertise?: string
      bio?: string
    }>
  }
  documents?: Array<{
    title: string
    file: TransformedMedia
    type?: string
    confidential: boolean
  }>
  applicationProcess?: {
    steps?: Array<{
      step: string
      description?: string
      duration?: string
    }>
    contactPerson?: {
      name: string
      role: string
      email: string
      phone?: string
    }
    applicationForm?: TransformedMedia
  }
  featured: boolean
  urgent: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
}

interface TransformedCounty {
  id: string
  name: string
  code?: string
}

interface InvestmentOpportunitiesResponse {
  opportunities: TransformedInvestmentOpportunity[]
  totalOpportunities: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility functions
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
  }
}

const transformCounty = (county: any): TransformedCounty | undefined => {
  if (!county || typeof county === 'string') return undefined

  return {
    id: county.id,
    name: county.name,
    code: county.code,
  }
}

const transformInvestmentOpportunity = (opportunity: any): TransformedInvestmentOpportunity => {
  return {
    id: opportunity.id,
    title: opportunity.title,
    description: extractTextFromLexical(opportunity.description),
    summary: opportunity.summary,
    sector: opportunity.sector,
    investmentType: opportunity.investmentType,
    status: opportunity.status,
    image: transformMedia(opportunity.image),
    gallery: Array.isArray(opportunity.gallery) 
      ? opportunity.gallery.map((item: any) => transformMedia(item.image)).filter(Boolean)
      : [],
    financial: {
      fundingRequired: opportunity.financial?.fundingRequired || 0,
      currency: opportunity.financial?.currency || 'KES',
      fundingStages: Array.isArray(opportunity.financial?.fundingStages) 
        ? opportunity.financial.fundingStages.map((stage: any) => ({
            stage: stage.stage,
            amount: stage.amount,
            timeline: stage.timeline,
            milestones: stage.milestones,
          }))
        : [],
      useOfFunds: Array.isArray(opportunity.financial?.useOfFunds) 
        ? opportunity.financial.useOfFunds.map((use: any) => ({
            category: use.category,
            amount: use.amount,
            percentage: use.percentage,
            description: use.description,
          }))
        : [],
      expectedReturns: opportunity.financial?.expectedReturns ? {
        roi: opportunity.financial.expectedReturns.roi,
        paybackPeriod: opportunity.financial.expectedReturns.paybackPeriod,
        revenueProjections: Array.isArray(opportunity.financial.expectedReturns.revenueProjections) 
          ? opportunity.financial.expectedReturns.revenueProjections.map((proj: any) => ({
              year: proj.year,
              revenue: proj.revenue,
              profit: proj.profit,
            }))
          : [],
      } : undefined,
    },
    businessModel: opportunity.businessModel ? {
      valueProposition: extractTextFromLexical(opportunity.businessModel.valueProposition),
      targetMarket: extractTextFromLexical(opportunity.businessModel.targetMarket),
      competitiveAdvantage: extractTextFromLexical(opportunity.businessModel.competitiveAdvantage),
      revenueStreams: Array.isArray(opportunity.businessModel.revenueStreams) 
        ? opportunity.businessModel.revenueStreams.map((stream: any) => ({
            stream: stream.stream,
            description: stream.description,
            projectedRevenue: stream.projectedRevenue,
          }))
        : [],
      keyPartners: Array.isArray(opportunity.businessModel.keyPartners) 
        ? opportunity.businessModel.keyPartners.map((partner: any) => ({
            partner: partner.partner,
            role: partner.role,
            contribution: partner.contribution,
          }))
        : [],
    } : undefined,
    location: {
      counties: Array.isArray(opportunity.location?.counties) 
        ? opportunity.location.counties.map(transformCounty).filter(Boolean)
        : [],
      specificLocation: opportunity.location?.specificLocation,
      coordinates: opportunity.location?.coordinates,
    },
    timeline: opportunity.timeline ? {
      applicationDeadline: opportunity.timeline.applicationDeadline,
      expectedStartDate: opportunity.timeline.expectedStartDate,
      projectDuration: opportunity.timeline.projectDuration,
      milestones: Array.isArray(opportunity.timeline.milestones) 
        ? opportunity.timeline.milestones.map((milestone: any) => ({
            milestone: milestone.milestone,
            targetDate: milestone.targetDate,
            description: milestone.description,
          }))
        : [],
    } : undefined,
    requirements: opportunity.requirements ? {
      investorCriteria: Array.isArray(opportunity.requirements.investorCriteria) 
        ? opportunity.requirements.investorCriteria.map((criterion: any) => ({
            criterion: criterion.criterion,
            description: criterion.description,
            mandatory: criterion.mandatory || false,
          }))
        : [],
      minimumInvestment: opportunity.requirements.minimumInvestment,
      maximumInvestment: opportunity.requirements.maximumInvestment,
      investorType: Array.isArray(opportunity.requirements.investorType) 
        ? opportunity.requirements.investorType
        : [],
      documentation: Array.isArray(opportunity.requirements.documentation) 
        ? opportunity.requirements.documentation.map((doc: any) => ({
            document: doc.document,
            required: doc.required !== false,
            description: doc.description,
          }))
        : [],
    } : undefined,
    impact: opportunity.impact ? {
      socialImpact: extractTextFromLexical(opportunity.impact.socialImpact),
      environmentalImpact: extractTextFromLexical(opportunity.impact.environmentalImpact),
      economicImpact: extractTextFromLexical(opportunity.impact.economicImpact),
      beneficiaries: opportunity.impact.beneficiaries,
      jobsCreated: opportunity.impact.jobsCreated,
      sdgAlignment: Array.isArray(opportunity.impact.sdgAlignment) 
        ? opportunity.impact.sdgAlignment.map((sdg: any) => ({
            sdg: sdg.sdg,
            description: sdg.description,
          }))
        : [],
    } : undefined,
    team: opportunity.team ? {
      projectLead: opportunity.team.projectLead ? {
        name: opportunity.team.projectLead.name,
        role: opportunity.team.projectLead.role,
        bio: opportunity.team.projectLead.bio,
        photo: transformMedia(opportunity.team.projectLead.photo),
        email: opportunity.team.projectLead.email,
      } : undefined,
      keyPersonnel: Array.isArray(opportunity.team.keyPersonnel) 
        ? opportunity.team.keyPersonnel.map((person: any) => ({
            name: person.name,
            role: person.role,
            expertise: person.expertise,
            bio: person.bio,
          }))
        : [],
    } : undefined,
    documents: Array.isArray(opportunity.documents) 
      ? opportunity.documents.map((doc: any) => ({
          title: doc.title,
          file: transformMedia(doc.file),
          type: doc.type,
          confidential: doc.confidential || false,
        })).filter((doc: any) => doc.file)
      : [],
    applicationProcess: opportunity.applicationProcess ? {
      steps: Array.isArray(opportunity.applicationProcess.steps) 
        ? opportunity.applicationProcess.steps.map((step: any) => ({
            step: step.step,
            description: step.description,
            duration: step.duration,
          }))
        : [],
      contactPerson: opportunity.applicationProcess.contactPerson ? {
        name: opportunity.applicationProcess.contactPerson.name,
        role: opportunity.applicationProcess.contactPerson.role,
        email: opportunity.applicationProcess.contactPerson.email,
        phone: opportunity.applicationProcess.contactPerson.phone,
      } : undefined,
      applicationForm: transformMedia(opportunity.applicationProcess.applicationForm),
    } : undefined,
    featured: opportunity.featured || false,
    urgent: opportunity.urgent || false,
    tags: Array.isArray(opportunity.tags) 
      ? opportunity.tags.map((tag: any) => tag.tag).filter(Boolean)
      : [],
    slug: opportunity.slug,
    createdAt: opportunity.createdAt,
    updatedAt: opportunity.updatedAt,
  }
}

// Main Investment Opportunities Handler
export const investmentOpportunitiesHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      sector,
      investmentType,
      status,
      featured,
      urgent,
      county,
      minAmount,
      maxAmount,
      limit = '20',
      page = '1',
      sort = '-updatedAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {}

    if (sector) where.sector = { equals: sector }
    if (investmentType) where.investmentType = { equals: investmentType }
    if (status) where.status = { equals: status }
    if (featured === 'true') where.featured = { equals: true }
    if (urgent === 'true') where.urgent = { equals: true }
    if (county) where['location.counties'] = { in: [county] }
    if (minAmount) where['financial.fundingRequired'] = { greater_than_equal: parseInt(minAmount) }
    if (maxAmount) {
      where['financial.fundingRequired'] = {
        ...where['financial.fundingRequired'],
        less_than_equal: parseInt(maxAmount),
      }
    }
    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } },
        { 'tags.tag': { contains: search } },
      ]
    }

    // Fetch investment opportunities with populated relationships
    const opportunitiesResult = await payload.find({
      collection: 'investment-opportunities',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate counties, etc.
    })

    // Transform investment opportunities
    const transformedOpportunities: TransformedInvestmentOpportunity[] = opportunitiesResult.docs.map(transformInvestmentOpportunity)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(opportunitiesResult.totalDocs / currentLimit)

    const response: InvestmentOpportunitiesResponse = {
      opportunities: transformedOpportunities,
      totalOpportunities: opportunitiesResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in investment opportunities endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single investment opportunity by ID or slug
export const investmentOpportunityByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let opportunity
    try {
      opportunity = await payload.findByID({
        collection: 'investment-opportunities',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'investment-opportunities',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      opportunity = result.docs[0]
    }

    if (!opportunity) {
      return res.status(404).json({
        error: 'Investment opportunity not found',
        message: `No investment opportunity found with ID or slug: ${id}`,
      })
    }

    const transformedOpportunity = transformInvestmentOpportunity(opportunity)

    res.status(200).json({
      opportunity: transformedOpportunity,
    })
  } catch (error) {
    console.error('Error in investment opportunity by ID endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
