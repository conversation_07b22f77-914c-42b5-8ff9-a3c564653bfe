import type { CollectionBeforeChangeHook } from 'payload'

/**
 * Universal ID generation hook that can be applied to any collection
 * Generates unique IDs based on collection slug and timestamp
 */
export const generateUniversalId: CollectionBeforeChangeHook = ({ data, operation, req, collection }) => {
  // Only generate ID for new items (create operation)
  if (operation === 'create') {
    try {
      // Get collection slug for prefix
      const collectionSlug = collection?.config?.slug || 'ITEM'
      
      // Create a readable prefix based on collection slug
      const prefix = collectionSlug
        .toUpperCase()
        .replace(/-/g, '_') // Replace hyphens with underscores
        .substring(0, 10) // Limit prefix length
      
      // Generate timestamp-based ID components
      const timestamp = Date.now().toString(36) // Base36 timestamp for shorter string
      const randomPart = Math.random().toString(36).substring(2, 8) // 6 random characters
      
      // Create the unique ID
      const uniqueId = `${prefix}-${timestamp}-${randomPart}`.toUpperCase()
      
      // Determine the field name to use for the ID
      let idFieldName = 'uniqueId'
      
      // Use collection-specific ID field names if they exist
      const collectionSpecificFields = {
        'projects': 'projectId',
        'media': 'mediaId',
        'news': 'newsId',
        'success-stories': 'storyId',
        'resources': 'resourceId',
        'events': 'eventId',
        'speakers': 'speakerId',
        'counties': 'countyId',
        'users': 'userId',
        'posts': 'postId',
        'pages': 'pageId',
        'categories': 'categoryId',
        'media-gallery': 'galleryId',
        'partnerships': 'partnershipId',
        'investment-opportunities': 'opportunityId',
        'partners': 'partnerId',
        'contact-submissions': 'submissionId',
        'partnership-applications': 'applicationId',
      }
      
      if (collectionSlug && collectionSpecificFields[collectionSlug]) {
        idFieldName = collectionSpecificFields[collectionSlug]
      }
      
      // Add the generated ID to the data
      data[idFieldName] = uniqueId
      
      req.payload.logger.info(`Generated ${idFieldName} for ${collectionSlug}: ${uniqueId}`)
      
    } catch (error) {
      req.payload.logger.error(`Error generating ID for ${collection?.config?.slug}:`, error)
      throw new Error(`Failed to generate unique ID for ${collection?.config?.slug}`)
    }
  }
  
  return data
}

/**
 * Helper function to get the appropriate ID field configuration for a collection
 * This can be used when defining collection fields
 */
export const getIdFieldConfig = (collectionSlug: string) => {
  const collectionSpecificFields = {
    'projects': 'projectId',
    'media': 'mediaId',
    'news': 'newsId',
    'success-stories': 'storyId',
    'resources': 'resourceId',
    'events': 'eventId',
    'speakers': 'speakerId',
    'counties': 'countyId',
    'users': 'userId',
    'posts': 'postId',
    'pages': 'pageId',
    'categories': 'categoryId',
    'media-gallery': 'galleryId',
    'partnerships': 'partnershipId',
    'investment-opportunities': 'opportunityId',
    'partners': 'partnerId',
    'contact-submissions': 'submissionId',
    'partnership-applications': 'applicationId',
  }
  
  const fieldName = collectionSpecificFields[collectionSlug] || 'uniqueId'
  const displayName = fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
  
  return {
    name: fieldName,
    type: 'text' as const,
    admin: {
      readOnly: true,
      description: `Auto-generated unique ${displayName.toLowerCase()}`,
      position: 'sidebar' as const,
    },
    access: {
      create: () => false, // Prevent manual creation
      update: () => false, // Prevent manual updates
    },
  }
}
