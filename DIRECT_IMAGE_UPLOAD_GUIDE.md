# Direct Image Upload Guide

## Overview

The NPI CMS now supports direct image upload functionality that allows users to upload images without having to first create them in the media collection. This provides a more streamlined user experience while still storing images in the database.

## Available Image Field Types

### 1. `directImageUploadField`

Simple image upload field that directly uploads to the media collection but provides a seamless experience.

```typescript
import { directImageUploadField } from '../../fields/imageUpload'

// Usage in collection
directImageUploadField({
  name: 'profileImage',
  label: 'Profile Image',
  required: true,
  admin: {
    description: 'Upload your profile image',
    position: 'sidebar',
  },
})
```

### 2. `enhancedImageField`

Image field with additional metadata including alt text and caption.

```typescript
import { enhancedImageField } from '../../fields/imageUpload'

// Usage in collection
enhancedImageField({
  name: 'featuredImage',
  label: 'Featured Image',
  required: true,
  admin: {
    description: 'Main article image with alt text and caption',
  },
})
```

**Generated Structure:**
```json
{
  "featuredImage": {
    "image": "media_id_reference",
    "alt": "Alternative text for accessibility",
    "caption": "Optional image caption"
  }
}
```

### 3. `heroImage<PERSON>ield`

Hero image field with focal point for responsive cropping.

```typescript
import { heroImageField } from '../../fields/imageUpload'

// Usage in collection
heroImageField({
  name: 'heroImage',
  label: 'Hero Image',
  required: true,
  admin: {
    description: 'Main hero image with focal point for responsive display',
  },
})
```

**Generated Structure:**
```json
{
  "heroImage": {
    "image": "media_id_reference",
    "alt": "Alternative text for accessibility",
    "focalPoint": {
      "x": 50,
      "y": 30
    }
  }
}
```

### 4. `multipleImagesField`

Array field for uploading multiple images with metadata.

```typescript
import { multipleImagesField } from '../../fields/imageUpload'

// Usage in collection
multipleImagesField({
  name: 'gallery',
  label: 'Image Gallery',
  required: false,
  admin: {
    description: 'Upload multiple images for the gallery',
  },
})
```

**Generated Structure:**
```json
{
  "gallery": [
    {
      "image": "media_id_reference_1",
      "alt": "First image alt text",
      "caption": "First image caption"
    },
    {
      "image": "media_id_reference_2", 
      "alt": "Second image alt text",
      "caption": "Second image caption"
    }
  ]
}
```

## Implementation Examples

### Projects Collection

```typescript
// src/collections/Projects/index.ts
import { enhancedImageField, multipleImagesField } from '../../fields/imageUpload'

export const Projects: CollectionConfig = {
  // ... other config
  fields: [
    // Main project image with metadata
    enhancedImageField({
      name: 'image',
      label: 'Main Project Image',
      required: false,
      admin: {
        description: 'Upload the main project image with alt text and caption',
      },
    }),
    
    // Project gallery
    {
      name: 'gallery',
      type: 'array',
      fields: [
        directImageUploadField({
          name: 'image',
          label: 'Gallery Image',
          required: true,
          admin: {
            description: 'Upload a gallery image',
          },
        }),
        {
          name: 'caption',
          type: 'text',
          admin: {
            description: 'Image caption',
          },
        },
      ],
    },
  ],
}
```

### News Collection

```typescript
// src/collections/News/index.ts
import { enhancedImageField, directImageUploadField } from '../../fields/imageUpload'

export const News: CollectionConfig = {
  // ... other config
  fields: [
    // Featured image with metadata
    enhancedImageField({
      name: 'featuredImage',
      label: 'Featured Image',
      required: true,
      admin: {
        description: 'Main article image with alt text and caption',
      },
    }),
    
    // Article gallery
    {
      name: 'gallery',
      type: 'array',
      fields: [
        directImageUploadField({
          name: 'image',
          label: 'Gallery Image',
          required: true,
          admin: {
            description: 'Upload a gallery image',
          },
        }),
        {
          name: 'caption',
          type: 'text',
        },
      ],
    },
  ],
}
```

### Success Stories Collection

```typescript
// src/collections/SuccessStories/index.ts
import { heroImageField } from '../../fields/imageUpload'

export const SuccessStories: CollectionConfig = {
  // ... other config
  fields: [
    // Hero image with focal point
    heroImageField({
      name: 'image',
      label: 'Main Story Image',
      required: true,
      admin: {
        description: 'Main story image with focal point for responsive display',
      },
    }),
  ],
}
```

## User Experience

### For Content Creators

1. **Direct Upload**: Click the upload button and select an image file
2. **Automatic Processing**: Image is automatically optimized and stored in database
3. **Metadata Entry**: Add alt text, captions, and focal points as needed
4. **No Media Collection Management**: No need to pre-create media items

### For Developers

1. **Consistent API**: All image fields return consistent data structures
2. **Database Storage**: Images are stored as base64 in MongoDB
3. **Automatic IDs**: Each uploaded image gets a unique auto-generated ID
4. **Validation**: Built-in validation for image formats and accessibility

## Benefits

### ✅ Streamlined Workflow
- Users can upload images directly without navigating to media collection
- Reduced steps in content creation process
- Intuitive upload experience

### ✅ Enhanced Metadata
- Built-in alt text fields for accessibility
- Optional captions for better content description
- Focal point selection for responsive images

### ✅ Database Storage
- Images stored directly in MongoDB as base64
- No external file storage dependencies
- Consistent with CMS database storage strategy

### ✅ Accessibility First
- Required alt text fields where appropriate
- Proper semantic structure for screen readers
- WCAG compliance support

## Migration from Standard Upload Fields

### Before (Standard Upload)
```typescript
{
  name: 'image',
  type: 'upload',
  relationTo: 'media',
  required: true,
}
```

### After (Enhanced Direct Upload)
```typescript
enhancedImageField({
  name: 'image',
  label: 'Image',
  required: true,
  admin: {
    description: 'Upload an image with metadata',
  },
})
```

## API Response Changes

### Standard Upload Response
```json
{
  "image": "media_id_reference"
}
```

### Enhanced Upload Response
```json
{
  "image": {
    "image": "media_id_reference",
    "alt": "Descriptive alt text",
    "caption": "Optional caption"
  }
}
```

## Frontend Usage

### Accessing Enhanced Image Data

```typescript
// For enhanced image fields
const { image, alt, caption } = item.featuredImage
const imageUrl = image?.url || image?.base64Data

// For hero image fields
const { image, alt, focalPoint } = item.heroImage
const imageUrl = image?.url || image?.base64Data
const focusX = focalPoint?.x || 50
const focusY = focalPoint?.y || 50
```

### Rendering Images

```jsx
// React component example
const ImageComponent = ({ imageData }) => {
  if (!imageData?.image) return null
  
  return (
    <figure>
      <img 
        src={imageData.image.url || `data:${imageData.image.mimeType};base64,${imageData.image.base64Data}`}
        alt={imageData.alt}
        style={{
          objectPosition: imageData.focalPoint 
            ? `${imageData.focalPoint.x}% ${imageData.focalPoint.y}%`
            : 'center'
        }}
      />
      {imageData.caption && (
        <figcaption>{imageData.caption}</figcaption>
      )}
    </figure>
  )
}
```

## Summary

The direct image upload functionality provides:

- ✅ **Seamless Upload Experience**: No need to manage media collection separately
- ✅ **Enhanced Metadata**: Built-in alt text, captions, and focal points
- ✅ **Database Storage**: Consistent with CMS storage strategy
- ✅ **Accessibility Support**: Required alt text and proper semantic structure
- ✅ **Developer Friendly**: Consistent API and easy integration
- ✅ **User Friendly**: Intuitive upload process for content creators

All collections that need images now have the option to upload directly without importing from the media collection, while still maintaining the benefits of centralized media management when needed.
