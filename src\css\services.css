/* Styles specific to the Services page, using the NPI color palette */

/* The main heading for the services page */
.services-header {
    text-align: center;
    padding: 2rem 1rem;
}

.services-header h1 {
    color: var(--npi-brown-dark);
}

.services-header p {
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Grid for displaying individual services */
.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 0 2rem;
}

/* Using the .npi-card component style from the palette file */
.service-item {
    /* This class would be added to an element that also has .npi-card */
    text-align: center;
    padding: 1.5rem;
}

.service-item h3 {
    color: var(--npi-blue); /* Using accent color for service titles */
}
