import fetch from 'node-fetch'

async function testCleanResponse() {
  const baseUrl = 'http://localhost:3001'
  
  console.log('🧹 Testing Clean Events Response...\n')
  
  try {
    const response = await fetch(`${baseUrl}/api/events`)
    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Success! Clean response received:\n')
      
      // Display the first event in a clean format
      if (data.events.length > 0) {
        const event = data.events[0]
        
        console.log('📅 EVENT DETAILS:')
        console.log('================')
        console.log(`Title: ${event.title}`)
        console.log(`Type: ${event.type}`)
        console.log(`Date: ${event.formattedDate}`)
        console.log(`Time: ${event.formattedTime || 'TBD'}`)
        console.log(`Duration: ${event.duration || 'Unknown'}`)
        console.log(`Location: ${event.location || 'TBD'}`)
        console.log(`Day: ${event.dayLabel}`)
        console.log(`Upcoming: ${event.isUpcoming ? 'Yes' : 'No'}`)
        console.log('\n📝 DESCRIPTION:')
        console.log('===============')
        console.log(event.description)
        
        console.log('\n👥 SPEAKERS:')
        console.log('============')
        if (event.speakers.length > 0) {
          event.speakers.forEach((speaker, index) => {
            console.log(`${index + 1}. ${speaker.name}`)
            if (speaker.title) console.log(`   Title: ${speaker.title}`)
            if (speaker.company) console.log(`   Company: ${speaker.company}`)
            if (speaker.bio) console.log(`   Bio: ${speaker.bio}`)
            if (speaker.photo) console.log(`   Photo: ${speaker.photo.url}`)
            console.log('')
          })
        } else {
          console.log('No speakers assigned')
        }
        
        console.log('\n📊 SUMMARY:')
        console.log('===========')
        console.log(`Total Events: ${data.totalEvents}`)
        console.log(`Events by Day: ${Object.keys(data.eventsByDay).length} days`)
        console.log(`Event Types: ${Object.keys(data.eventsByType).join(', ')}`)
        console.log(`Upcoming Events: ${data.upcomingEvents.length}`)
        
      } else {
        console.log('No events found in the database.')
      }
      
    } else {
      console.log('❌ Failed!')
      console.log('Response:', data)
    }
    
  } catch (error) {
    console.error('❌ Error testing endpoint:', error.message)
  }
}

// Run the test
testCleanResponse()
