// Script to update all existing users to have county ID 2
// Run with: node scripts/update-users-county.js

const BASE_URL = 'http://localhost:3000/api'

// Admin credentials - update these with your actual admin credentials
const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = 'password123'

let authToken = null

async function login() {
  console.log('🔐 Logging in as admin...')

  try {
    const response = await fetch(`${BASE_URL}/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
      }),
    })

    const data = await response.json()

    if (response.ok) {
      console.log('✅ Login successful!')
      authToken = data.token
      return true
    } else {
      console.log('❌ Login failed:', data.message)
      console.log('Please update ADMIN_EMAIL and ADMIN_PASSWORD in the script')
      return false
    }
  } catch (error) {
    console.error('❌ Login error:', error.message)
    return false
  }
}

async function verifyCountyExists(countyId) {
  console.log(`🔍 Verifying county ${countyId} exists...`)

  try {
    const response = await fetch(`${BASE_URL}/counties/${countyId}`)
    const data = await response.json()

    if (response.ok) {
      console.log(`✅ County found: ${data.county.name} (${data.county.code})`)
      return true
    } else {
      console.log(`❌ County ${countyId} not found:`, data.error)
      return false
    }
  } catch (error) {
    console.error(`❌ Error verifying county ${countyId}:`, error.message)
    return false
  }
}

async function getAllUsers() {
  console.log('📋 Fetching all users...')

  try {
    // Get all users with a large limit to fetch them all
    const response = await fetch(`${BASE_URL}/users?limit=1000`)
    const data = await response.json()

    if (response.ok) {
      console.log(`✅ Found ${data.totalDocs} users`)
      return data.docs
    } else {
      console.log('❌ Failed to fetch users:', data.error)
      return []
    }
  } catch (error) {
    console.error('❌ Error fetching users:', error.message)
    return []
  }
}

async function updateUserCounty(userId, countyId) {
  try {
    const response = await fetch(`${BASE_URL}/users/${userId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        county: countyId,
      }),
    })

    const data = await response.json()

    if (response.ok && data.doc) {
      return { success: true, user: data.doc }
    } else {
      return { success: false, error: data.error || data.message }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

async function updateAllUsersToCounty(targetCountyId) {
  console.log(`\n🔄 Starting bulk update to county ${targetCountyId}...`)

  // Verify county exists
  const countyExists = await verifyCountyExists(targetCountyId)
  if (!countyExists) {
    console.log('❌ Cannot proceed - target county does not exist')
    return false
  }

  // Get all users
  const users = await getAllUsers()
  if (users.length === 0) {
    console.log('❌ No users found to update')
    return false
  }

  // Filter users that need updating (don't already have the target county)
  const usersToUpdate = users.filter((user) => user.county !== targetCountyId)

  console.log(`📊 Users analysis:`)
  console.log(`  - Total users: ${users.length}`)
  console.log(
    `  - Users already in county ${targetCountyId}: ${users.length - usersToUpdate.length}`,
  )
  console.log(`  - Users to update: ${usersToUpdate.length}`)

  if (usersToUpdate.length === 0) {
    console.log('✅ All users already have the target county!')
    return true
  }

  // Ask for confirmation
  console.log(`\n⚠️  About to update ${usersToUpdate.length} users to county ${targetCountyId}`)
  console.log('Users to be updated:')
  usersToUpdate.slice(0, 10).forEach((user) => {
    const currentCounty = user.county ? `county ${user.county}` : 'no county'
    console.log(`  - ${user.name} (${user.email}) - currently in ${currentCounty}`)
  })

  if (usersToUpdate.length > 10) {
    console.log(`  ... and ${usersToUpdate.length - 10} more users`)
  }

  // In a real script, you might want to add a confirmation prompt here
  // For now, we'll proceed automatically
  console.log('\n🚀 Proceeding with updates...')

  // Update users
  let successCount = 0
  let errorCount = 0
  const errors = []

  for (let i = 0; i < usersToUpdate.length; i++) {
    const user = usersToUpdate[i]
    console.log(`[${i + 1}/${usersToUpdate.length}] Updating ${user.name}...`)

    const result = await updateUserCounty(user.id, targetCountyId)

    if (result.success) {
      successCount++
      console.log(`  ✅ Success`)
    } else {
      errorCount++
      errors.push({ user: user.name, error: result.error })
      console.log(`  ❌ Failed: ${result.error}`)
    }

    // Add a small delay to avoid overwhelming the server
    await new Promise((resolve) => setTimeout(resolve, 100))
  }

  // Summary
  console.log('\n📊 Update Summary:')
  console.log(`  ✅ Successful updates: ${successCount}`)
  console.log(`  ❌ Failed updates: ${errorCount}`)

  if (errors.length > 0) {
    console.log('\n❌ Errors encountered:')
    errors.forEach((error) => {
      console.log(`  - ${error.user}: ${error.error}`)
    })
  }

  return errorCount === 0
}

async function main() {
  console.log('🚀 Bulk User County Update Script')
  console.log('='.repeat(50))

  // Login
  const loginSuccess = await login()
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication')
    process.exit(1)
  }

  // Update all users to county 2
  const targetCountyId = 2
  const success = await updateAllUsersToCounty(targetCountyId)

  console.log('\n' + '='.repeat(50))
  if (success) {
    console.log('🎉 Bulk update completed successfully!')
  } else {
    console.log('⚠️  Bulk update completed with some errors')
  }
}

// Run the script
main().catch(console.error)
