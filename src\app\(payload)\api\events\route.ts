import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { eventsHand<PERSON> } from '@/endpoints/events'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    
    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      query: Object.fromEntries(request.nextUrl.searchParams.entries()),
      headers: {
        get: (name: string) => request.headers.get(name),
      },
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the events handler
    await eventsHand<PERSON>(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Error in events API route:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
