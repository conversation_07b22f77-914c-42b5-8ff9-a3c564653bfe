import type { PayloadRequest } from 'payload'

/**
 * Universal CRUD handlers that can be used for any collection
 * These provide complete Create, Read, Update, Delete operations
 */

// Generic GET all items handler
export const createGetAllHandler = (collectionSlug: string) => {
  return async (req: PayloadRequest, res: any): Promise<any> => {
    try {
      const { payload } = req

      // Parse query parameters
      const {
        limit = '20',
        page = '1',
        sort = '-updatedAt',
        search,
        ...filters
      } = req.query as Record<string, string>

      // Safely parse integers with fallbacks
      const parsedPage = parseInt(page) || 1
      const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

      // Build where clause from filters
      const where: any = {}
      
      // Add search functionality if search term provided
      if (search) {
        // Try to search in common text fields
        const searchFields = ['title', 'name', 'subject', 'description']
        const searchConditions = searchFields.map(field => ({
          [field]: { contains: search }
        }))
        where.or = searchConditions
      }

      // Add other filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value && key !== 'limit' && key !== 'page' && key !== 'sort' && key !== 'search') {
          where[key] = { equals: value }
        }
      })

      // Fetch items from collection
      const result = await payload.find({
        collection: collectionSlug as any,
        where: Object.keys(where).length > 0 ? where : undefined,
        page: parsedPage,
        limit: parsedLimit,
        sort,
      })

      return res.json({
        success: true,
        data: result.docs,
        totalItems: result.totalDocs,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      })
    } catch (error) {
      console.error(`Error fetching ${collectionSlug}:`, error)
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }
}

// Generic GET by ID handler
export const createGetByIdHandler = (collectionSlug: string) => {
  return async (req: PayloadRequest, res: any): Promise<any> => {
    try {
      const { payload } = req
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'ID parameter is required',
        })
      }

      const item = await payload.findByID({
        collection: collectionSlug as any,
        id,
      })

      if (!item) {
        return res.status(404).json({
          success: false,
          error: `${collectionSlug} item not found`,
        })
      }

      return res.json({
        success: true,
        data: item,
      })
    } catch (error) {
      console.error(`Error fetching ${collectionSlug} by ID:`, error)
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }
}

// Generic POST (create) handler
export const createPostHandler = (collectionSlug: string) => {
  return async (req: PayloadRequest, res: any): Promise<any> => {
    try {
      const { payload } = req
      const data = req.body

      if (!data) {
        return res.status(400).json({
          success: false,
          error: 'Request body is required',
        })
      }

      const newItem = await payload.create({
        collection: collectionSlug as any,
        data,
      })

      return res.status(201).json({
        success: true,
        message: `${collectionSlug} item created successfully`,
        data: newItem,
      })
    } catch (error) {
      console.error(`Error creating ${collectionSlug}:`, error)
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }
}

// Generic PUT (update) handler
export const createPutHandler = (collectionSlug: string) => {
  return async (req: PayloadRequest, res: any): Promise<any> => {
    try {
      const { payload } = req
      const { id } = req.params
      const data = req.body

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'ID parameter is required',
        })
      }

      if (!data) {
        return res.status(400).json({
          success: false,
          error: 'Request body is required',
        })
      }

      const updatedItem = await payload.update({
        collection: collectionSlug as any,
        id,
        data,
      })

      return res.json({
        success: true,
        message: `${collectionSlug} item updated successfully`,
        data: updatedItem,
      })
    } catch (error) {
      console.error(`Error updating ${collectionSlug}:`, error)
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }
}

// Generic DELETE handler
export const createDeleteHandler = (collectionSlug: string) => {
  return async (req: PayloadRequest, res: any): Promise<any> => {
    try {
      const { payload } = req
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'ID parameter is required',
        })
      }

      await payload.delete({
        collection: collectionSlug as any,
        id,
      })

      return res.json({
        success: true,
        message: `${collectionSlug} item deleted successfully`,
      })
    } catch (error) {
      console.error(`Error deleting ${collectionSlug}:`, error)
      return res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      })
    }
  }
}

/**
 * Helper function to create all CRUD handlers for a collection
 */
export const createCRUDHandlers = (collectionSlug: string) => {
  return {
    getAll: createGetAllHandler(collectionSlug),
    getById: createGetByIdHandler(collectionSlug),
    create: createPostHandler(collectionSlug),
    update: createPutHandler(collectionSlug),
    delete: createDeleteHandler(collectionSlug),
  }
}
