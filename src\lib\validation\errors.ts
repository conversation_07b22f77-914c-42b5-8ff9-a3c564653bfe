import { ZodError, ZodIssue } from 'zod'
import type { PayloadRequest } from 'payload'

// Custom error classes
export class ValidationError extends Error {
  public readonly code = 'VALIDATION_ERROR'
  public readonly statusCode = 400
  public readonly issues: ValidationIssue[]

  constructor(message: string, issues: ValidationIssue[] = []) {
    super(message)
    this.name = 'ValidationError'
    this.issues = issues
  }

  static fromZodError(error: ZodError): ValidationError {
    const issues: ValidationIssue[] = error.issues.map(issue => ({
      field: issue.path.join('.'),
      message: issue.message,
      code: issue.code,
      received: 'received' in issue ? issue.received : undefined,
      expected: 'expected' in issue ? issue.expected : undefined,
    }))

    return new ValidationError('Validation failed', issues)
  }
}

export class AuthenticationError extends Error {
  public readonly code = 'AUTHENTICATION_ERROR'
  public readonly statusCode = 401

  constructor(message: string = 'Authentication required') {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends Error {
  public readonly code = 'AUTHORIZATION_ERROR'
  public readonly statusCode = 403

  constructor(message: string = 'Insufficient permissions') {
    super(message)
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends Error {
  public readonly code = 'NOT_FOUND_ERROR'
  public readonly statusCode = 404

  constructor(message: string = 'Resource not found') {
    super(message)
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends Error {
  public readonly code = 'CONFLICT_ERROR'
  public readonly statusCode = 409

  constructor(message: string = 'Resource conflict') {
    super(message)
    this.name = 'ConflictError'
  }
}

export class RateLimitError extends Error {
  public readonly code = 'RATE_LIMIT_ERROR'
  public readonly statusCode = 429

  constructor(message: string = 'Rate limit exceeded') {
    super(message)
    this.name = 'RateLimitError'
  }
}

export class InternalServerError extends Error {
  public readonly code = 'INTERNAL_SERVER_ERROR'
  public readonly statusCode = 500

  constructor(message: string = 'Internal server error') {
    super(message)
    this.name = 'InternalServerError'
  }
}

export class ServiceUnavailableError extends Error {
  public readonly code = 'SERVICE_UNAVAILABLE_ERROR'
  public readonly statusCode = 503

  constructor(message: string = 'Service temporarily unavailable') {
    super(message)
    this.name = 'ServiceUnavailableError'
  }
}

// Validation issue interface
export interface ValidationIssue {
  field: string
  message: string
  code: string
  received?: any
  expected?: any
}

// Error response interface
export interface ErrorResponse {
  error: string
  message: string
  code: string
  statusCode: number
  issues?: ValidationIssue[]
  timestamp: string
  path?: string
  requestId?: string
}

// Error handler utility
export function createErrorResponse(
  error: Error,
  req?: PayloadRequest,
  path?: string
): ErrorResponse {
  const timestamp = new Date().toISOString()
  const requestId = req?.id || generateRequestId()

  // Handle known error types
  if (error instanceof ValidationError) {
    return {
      error: 'Validation Error',
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      issues: error.issues,
      timestamp,
      path,
      requestId,
    }
  }

  if (error instanceof AuthenticationError) {
    return {
      error: 'Authentication Error',
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      timestamp,
      path,
      requestId,
    }
  }

  if (error instanceof AuthorizationError) {
    return {
      error: 'Authorization Error',
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      timestamp,
      path,
      requestId,
    }
  }

  if (error instanceof NotFoundError) {
    return {
      error: 'Not Found',
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      timestamp,
      path,
      requestId,
    }
  }

  if (error instanceof ConflictError) {
    return {
      error: 'Conflict',
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      timestamp,
      path,
      requestId,
    }
  }

  if (error instanceof RateLimitError) {
    return {
      error: 'Rate Limit Exceeded',
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      timestamp,
      path,
      requestId,
    }
  }

  if (error instanceof ServiceUnavailableError) {
    return {
      error: 'Service Unavailable',
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      timestamp,
      path,
      requestId,
    }
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const validationError = ValidationError.fromZodError(error)
    return createErrorResponse(validationError, req, path)
  }

  // Handle Payload CMS errors
  if (error.name === 'ValidationError' && 'data' in error) {
    const issues: ValidationIssue[] = []
    if (Array.isArray((error as any).data)) {
      (error as any).data.forEach((item: any) => {
        if (item.field && item.message) {
          issues.push({
            field: item.field,
            message: item.message,
            code: 'invalid_value',
          })
        }
      })
    }

    return {
      error: 'Validation Error',
      message: error.message || 'Validation failed',
      code: 'VALIDATION_ERROR',
      statusCode: 400,
      issues,
      timestamp,
      path,
      requestId,
    }
  }

  // Handle database errors
  if (error.name === 'MongoError' || error.name === 'MongoServerError') {
    if ((error as any).code === 11000) {
      return {
        error: 'Conflict',
        message: 'Resource already exists',
        code: 'DUPLICATE_KEY_ERROR',
        statusCode: 409,
        timestamp,
        path,
        requestId,
      }
    }
  }

  // Handle network errors
  if (error.name === 'NetworkError' || error.message.includes('ECONNREFUSED')) {
    return {
      error: 'Service Unavailable',
      message: 'Unable to connect to external service',
      code: 'NETWORK_ERROR',
      statusCode: 503,
      timestamp,
      path,
      requestId,
    }
  }

  // Handle timeout errors
  if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
    return {
      error: 'Request Timeout',
      message: 'Request timed out',
      code: 'TIMEOUT_ERROR',
      statusCode: 408,
      timestamp,
      path,
      requestId,
    }
  }

  // Default to internal server error
  return {
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' 
      ? 'An unexpected error occurred' 
      : error.message,
    code: 'INTERNAL_SERVER_ERROR',
    statusCode: 500,
    timestamp,
    path,
    requestId,
  }
}

// Error handler middleware for endpoints
export function withErrorHandler<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args)
    } catch (error) {
      // Extract request and response from args (assuming they're the first two)
      const [req, res] = args as [PayloadRequest, any]
      
      const errorResponse = createErrorResponse(
        error instanceof Error ? error : new Error(String(error)),
        req,
        req?.url
      )

      // Log error for monitoring
      console.error('API Error:', {
        ...errorResponse,
        stack: error instanceof Error ? error.stack : undefined,
      })

      // Send error response
      if (res && typeof res.status === 'function') {
        return res.status(errorResponse.statusCode).json(errorResponse) as R
      }

      // Re-throw if we can't send response
      throw error
    }
  }
}

// Validation middleware
export function validateInput<T>(schema: any, data: unknown): T {
  try {
    return schema.parse(data)
  } catch (error) {
    if (error instanceof ZodError) {
      throw ValidationError.fromZodError(error)
    }
    throw new ValidationError('Invalid input data')
  }
}

// Sanitization utilities
export function sanitizeString(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
}

export function sanitizeObject(obj: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {}
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value)
    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      sanitized[key] = sanitizeObject(value)
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizeString(item) :
        typeof item === 'object' && item !== null ? sanitizeObject(item) :
        item
      )
    } else {
      sanitized[key] = value
    }
  }
  
  return sanitized
}

// Rate limiting utilities
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  identifier: string,
  limit: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): boolean {
  const now = Date.now()
  const record = rateLimitStore.get(identifier)
  
  if (!record || now > record.resetTime) {
    rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (record.count >= limit) {
    return false
  }
  
  record.count++
  return true
}

export function getRateLimitIdentifier(req: PayloadRequest): string {
  // Use user ID if authenticated, otherwise use IP address
  if (req.user?.id) {
    return `user:${req.user.id}`
  }
  
  const ip = req.ip || 
    req.headers['x-forwarded-for'] || 
    req.headers['x-real-ip'] || 
    req.connection?.remoteAddress ||
    'unknown'
  
  return `ip:${ip}`
}

// Request ID generator
function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15)
}

// Async error wrapper for better error handling
export function asyncHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return (...args: T): Promise<R> => {
    return Promise.resolve(fn(...args)).catch((error) => {
      // Log the error
      console.error('Async handler error:', error)
      throw error
    })
  }
}

// Error logging utility
export function logError(error: Error, context?: Record<string, any>): void {
  const logData = {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    context,
    timestamp: new Date().toISOString(),
  }
  
  // In production, you might want to send this to a logging service
  console.error('Error logged:', JSON.stringify(logData, null, 2))
}
