# NPI CMS Deployment Guide

## Overview

This guide covers deploying the Natural Products Institute CMS to production environments. The system is designed to work with modern cloud platforms and supports various deployment strategies.

## Prerequisites

- Node.js 18+ 
- MongoDB database
- Vercel Blob Storage account (or alternative file storage)
- Domain name and SSL certificate
- SMTP server for email notifications (optional)

## Environment Configuration

### Required Environment Variables

Create a `.env.production` file with the following variables:

```env
# Application
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://your-domain.com

# Database
DATABASE_URI=mongodb+srv://username:<EMAIL>/npi-cms?retryWrites=true&w=majority

# Authentication & Security
PAYLOAD_SECRET=your-super-secure-secret-key-min-32-chars
JWT_SECRET=your-jwt-secret-key-min-32-chars
COOKIE_SECRET=your-cookie-secret-key

# File Storage (Vercel Blob)
BLOB_READ_WRITE_TOKEN=vercel_blob_rw_token_here

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Natural Products Institute

# External Services
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
SENTRY_DSN=your-sentry-dsn

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
```

### Security Considerations

1. **Secrets Management**: Use a secure secrets manager (AWS Secrets Manager, Azure Key Vault, etc.)
2. **Environment Isolation**: Keep production secrets separate from development
3. **Regular Rotation**: Rotate secrets regularly
4. **Access Control**: Limit access to production environment variables

## Deployment Options

### Option 1: Vercel Deployment (Recommended)

#### Prerequisites
- Vercel account
- GitHub repository

#### Steps

1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Login to Vercel
   vercel login
   
   # Link project
   vercel link
   ```

2. **Configure Environment Variables**
   ```bash
   # Set environment variables via CLI
   vercel env add PAYLOAD_SECRET production
   vercel env add DATABASE_URI production
   vercel env add BLOB_READ_WRITE_TOKEN production
   # ... add all required variables
   ```

3. **Deploy**
   ```bash
   # Deploy to production
   vercel --prod
   ```

4. **Configure Domain**
   - Add custom domain in Vercel dashboard
   - Configure DNS records
   - SSL certificate is automatically provisioned

#### Vercel Configuration

Create `vercel.json`:

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ]
}
```

### Option 2: Docker Deployment

#### Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URI=${DATABASE_URI}
      - PAYLOAD_SECRET=${PAYLOAD_SECRET}
      - JWT_SECRET=${JWT_SECRET}
      - BLOB_READ_WRITE_TOKEN=${BLOB_READ_WRITE_TOKEN}
    depends_on:
      - mongodb
    restart: unless-stopped

  mongodb:
    image: mongo:6
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongodb_data:
```

### Option 3: AWS Deployment

#### Using AWS App Runner

1. **Create App Runner Service**
   ```bash
   # Create apprunner.yaml
   version: 1.0
   runtime: nodejs18
   build:
     commands:
       build:
         - npm install
         - npm run build
   run:
     runtime-version: 18
     command: npm start
     network:
       port: 3000
       env: PORT
   ```

2. **Configure Environment Variables** in AWS App Runner console

3. **Set up Database** using AWS DocumentDB or MongoDB Atlas

#### Using AWS ECS with Fargate

1. **Build and Push Docker Image**
   ```bash
   # Build image
   docker build -t npi-cms .
   
   # Tag for ECR
   docker tag npi-cms:latest 123456789012.dkr.ecr.region.amazonaws.com/npi-cms:latest
   
   # Push to ECR
   docker push 123456789012.dkr.ecr.region.amazonaws.com/npi-cms:latest
   ```

2. **Create ECS Task Definition**
3. **Configure Load Balancer**
4. **Set up Auto Scaling**

## Database Setup

### MongoDB Atlas (Recommended)

1. **Create Cluster**
   - Choose appropriate tier (M10+ for production)
   - Select region closest to your application
   - Configure security settings

2. **Database Configuration**
   ```javascript
   // Create indexes for better performance
   db.projects.createIndex({ "slug": 1 }, { unique: true })
   db.projects.createIndex({ "featured": 1, "published": 1 })
   db.projects.createIndex({ "category": 1, "status": 1 })
   db.projects.createIndex({ "createdAt": -1 })
   
   db.news.createIndex({ "slug": 1 }, { unique: true })
   db.news.createIndex({ "status": 1, "publishDate": -1 })
   db.news.createIndex({ "featured": 1, "urgent": 1 })
   
   db.resources.createIndex({ "slug": 1 }, { unique: true })
   db.resources.createIndex({ "type": 1, "category": 1 })
   db.resources.createIndex({ "featured": 1, "published": 1 })
   ```

3. **Backup Configuration**
   - Enable automated backups
   - Configure backup retention policy
   - Test restore procedures

### Self-Hosted MongoDB

1. **Installation**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install mongodb
   
   # Configure replica set for production
   sudo nano /etc/mongod.conf
   ```

2. **Security Configuration**
   ```yaml
   # /etc/mongod.conf
   security:
     authorization: enabled
   
   net:
     bindIp: 127.0.0.1,your-app-server-ip
   
   replication:
     replSetName: "rs0"
   ```

## File Storage Setup

### Vercel Blob Storage

1. **Create Blob Store**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Create blob store
   vercel blob create npi-cms-files
   ```

2. **Configure Access Token**
   - Generate read/write token
   - Add to environment variables

### Alternative: AWS S3

```javascript
// Configure S3 adapter in payload.config.ts
import { s3Adapter } from '@payloadcms/plugin-cloud-storage/s3'

export default buildConfig({
  plugins: [
    cloudStorage({
      collections: {
        media: {
          adapter: s3Adapter({
            config: {
              endpoint: process.env.S3_ENDPOINT,
              region: process.env.S3_REGION,
              credentials: {
                accessKeyId: process.env.S3_ACCESS_KEY_ID,
                secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
              },
            },
            bucket: process.env.S3_BUCKET,
          }),
        },
      },
    }),
  ],
})
```

## SSL/TLS Configuration

### Using Cloudflare (Recommended)

1. **Add Domain to Cloudflare**
2. **Configure DNS Records**
3. **Enable SSL/TLS**
   - Set to "Full (strict)" mode
   - Enable "Always Use HTTPS"
   - Configure HSTS

### Using Let's Encrypt

```bash
# Install Certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### Application Monitoring

1. **Sentry Integration**
   ```javascript
   // sentry.client.config.js
   import * as Sentry from "@sentry/nextjs"
   
   Sentry.init({
     dsn: process.env.SENTRY_DSN,
     environment: process.env.NODE_ENV,
   })
   ```

2. **Health Check Endpoint**
   ```javascript
   // pages/api/health.ts
   export default function handler(req, res) {
     res.status(200).json({
       status: 'healthy',
       timestamp: new Date().toISOString(),
       version: process.env.npm_package_version,
     })
   }
   ```

### Log Management

```javascript
// lib/logger.ts
import winston from 'winston'

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
})

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }))
}

export default logger
```

## Performance Optimization

### Caching Strategy

1. **CDN Configuration**
   - Configure Cloudflare or AWS CloudFront
   - Set appropriate cache headers
   - Enable compression

2. **Database Optimization**
   - Create appropriate indexes
   - Use connection pooling
   - Implement query optimization

3. **Image Optimization**
   - Use Next.js Image component
   - Configure image sizes
   - Enable WebP format

### Load Testing

```bash
# Install Artillery
npm install -g artillery

# Create load test
# artillery.yml
config:
  target: 'https://your-domain.com'
  phases:
    - duration: 60
      arrivalRate: 10

scenarios:
  - name: "API Load Test"
    requests:
      - get:
          url: "/api/projects"
      - get:
          url: "/api/news"

# Run test
artillery run artillery.yml
```

## Backup and Recovery

### Database Backup

```bash
# MongoDB backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mongodb"
DB_NAME="npi-cms"

mkdir -p $BACKUP_DIR

mongodump --uri="$DATABASE_URI" --out="$BACKUP_DIR/backup_$DATE"

# Compress backup
tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" -C "$BACKUP_DIR" "backup_$DATE"
rm -rf "$BACKUP_DIR/backup_$DATE"

# Upload to S3 (optional)
aws s3 cp "$BACKUP_DIR/backup_$DATE.tar.gz" s3://your-backup-bucket/mongodb/
```

### File Storage Backup

```bash
# Sync Vercel Blob to S3 for backup
vercel blob list | while read file; do
  vercel blob download "$file" --output "./backup/$file"
  aws s3 cp "./backup/$file" "s3://your-backup-bucket/files/$file"
done
```

## Security Checklist

- [ ] Environment variables secured
- [ ] Database access restricted
- [ ] SSL/TLS enabled
- [ ] CORS properly configured
- [ ] Rate limiting implemented
- [ ] Input validation enabled
- [ ] File upload restrictions in place
- [ ] Security headers configured
- [ ] Regular security updates
- [ ] Backup and recovery tested

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all environment variables are set
   - Review build logs for specific errors

2. **Database Connection Issues**
   - Verify connection string format
   - Check network connectivity
   - Ensure database user has proper permissions

3. **File Upload Problems**
   - Verify blob storage configuration
   - Check file size limits
   - Ensure proper MIME type handling

4. **Performance Issues**
   - Review database queries and indexes
   - Check for memory leaks
   - Monitor resource usage

### Support Resources

- [PayloadCMS Documentation](https://payloadcms.com/docs)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [Vercel Documentation](https://vercel.com/docs)
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)

---

For additional support, contact the development team or refer to the project documentation.
