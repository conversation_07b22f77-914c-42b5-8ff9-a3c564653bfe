import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { adminOnly } from '../../access/adminOnly'

export const Users: CollectionConfig = {
  slug: 'users',
  access: {
    admin: authenticated,
    create: () => true, // Temporarily allow anyone to create users for initial admin setup
    delete: adminOnly, // Only admins can delete users
    read: authenticated,
    update: ({ req: { user } }) => {
      // Users can update their own profile, admins can update any profile
      if (user?.role === 'admin' || user?.role === 'super-admin') {
        return true
      }
      // Users can only update their own profile
      return {
        id: {
          equals: user?.id,
        },
      }
    },
  },
  admin: {
    defaultColumns: ['name', 'email', 'role', 'county', 'createdAt'],
    useAsTitle: 'name',
    group: 'Administration',
  },
  auth: {
    tokenExpiration: 7200, // 2 hours
    verify: false, // Set to true if you want email verification
    maxLoginAttempts: 5,
    lockTime: 600000, // 10 minutes
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      admin: {
        description: 'Full name of the user',
      },
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      defaultValue: 'user',
      options: [
        { label: 'Super Admin', value: 'super-admin' },
        { label: 'Admin', value: 'admin' },
        { label: 'Content Manager', value: 'content-manager' },
        { label: 'Editor', value: 'editor' },
        { label: 'User', value: 'user' },
      ],
      access: {
        create: () => true, // Temporarily allow anyone to set role for initial admin setup
        update: adminOnly,
      },
      admin: {
        description: 'User role determines access permissions',
      },
    },
    {
      name: 'county',
      type: 'relationship',
      relationTo: 'counties',
      label: 'County',
      admin: {
        description: 'The county this user is associated with',
      },
    },
    {
      name: 'organization',
      type: 'text',
      admin: {
        description: 'Organization or company the user represents',
      },
    },
    {
      name: 'position',
      type: 'text',
      admin: {
        description: 'Job title or position',
      },
    },
    {
      name: 'phone',
      type: 'text',
      admin: {
        description: 'Phone number',
      },
    },
    {
      name: 'bio',
      type: 'textarea',
      admin: {
        description: 'Brief biography or description',
      },
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Profile picture',
      },
    },
    {
      name: 'preferences',
      type: 'group',
      fields: [
        {
          name: 'newsletter',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Subscribe to newsletter',
          },
        },
        {
          name: 'notifications',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description: 'Receive email notifications',
          },
        },
        {
          name: 'language',
          type: 'select',
          defaultValue: 'en',
          options: [
            { label: 'English', value: 'en' },
            { label: 'Swahili', value: 'sw' },
          ],
        },
      ],
    },
    {
      name: 'lastLogin',
      type: 'date',
      admin: {
        readOnly: true,
        description: 'Last login timestamp',
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      access: {
        create: adminOnly,
        update: adminOnly,
      },
      admin: {
        description: 'Whether the user account is active',
      },
    },
  ],
  timestamps: true,
}
