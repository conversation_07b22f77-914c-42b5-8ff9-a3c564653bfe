// Export all validation utilities

// Schemas
export {
  validationSchemas,
  projectSchema,
  successStorySchema,
  resourceSchema,
  newsSchema,
  contactSubmissionSchema,
  investmentOpportunitySchema,
  emailSchema,
  phoneSchema,
  urlSchema,
  slugSchema,
  richTextSchema,
  coordinatesSchema,
  mediaSchema,
} from './schemas'

export type {
  ProjectInput,
  SuccessStoryInput,
  ResourceInput,
  NewsInput,
  ContactSubmissionInput,
  InvestmentOpportunityInput,
} from './schemas'

// Error handling
export {
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  InternalServerError,
  ServiceUnavailableError,
  createErrorResponse,
  withErrorHandler,
  validateInput,
  sanitizeString,
  sanitizeObject,
  checkRateLimit,
  getRateLimitIdentifier,
  asyncHandler,
  logError,
} from './errors'

export type {
  ValidationIssue,
  ErrorResponse,
} from './errors'

// Sanitization
export {
  sanitizers,
  sanitizeHtml,
  sanitizeText,
  sanitizeEmail,
  sanitizePhone,
  sanitizeUrl,
  sanitizeSlug,
  sanitizeFilename,
  sanitizeRichText,
  sanitizeObject as sanitizeObjectDeep,
  sanitizeFileUpload,
  generateCSPNonce,
  createCSPHeader,
} from './sanitization'
