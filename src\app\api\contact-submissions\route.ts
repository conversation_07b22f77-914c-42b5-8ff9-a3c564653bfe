import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters for filtering and pagination
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const status = searchParams.get('status')
    const priority = searchParams.get('priority')

    // Build where clause for filtering (simplified to avoid type issues)
    const where = {} as any
    if (category) where.category = { equals: category }
    if (status) where.status = { equals: status }
    if (priority) where.priority = { equals: priority }

    // Fetch contact submissions directly from PayloadCMS
    const result = await payload.find({
      collection: 'contact-submissions',
      where: Object.keys(where).length > 0 ? where : undefined,
      page,
      limit,
      sort: '-createdAt',
    })

    return NextResponse.json({
      submissions: result.docs,
      totalSubmissions: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPrevPage: result.hasPrevPage,
    })
  } catch (error) {
    console.error('Contact submissions GET error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()

    // Extract and validate required fields
    const {
      name,
      email,
      phone,
      organization,
      role,
      subject,
      category,
      message,
      location,
      priority = 'medium',
    } = body

    // Basic validation
    if (!name || !email || !subject || !message || !category) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Name, email, subject, message, and category are required',
        },
        { status: 400 },
      )
    }

    // Capture metadata
    const metadata = {
      source: 'website-form' as const,
      ipAddress:
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '127.0.0.1',
      userAgent: request.headers.get('user-agent') || '',
      referrer: request.headers.get('referer') || request.headers.get('referrer') || '',
    }

    // Create the submission directly using PayloadCMS
    const submission = await payload.create({
      collection: 'contact-submissions',
      data: {
        name,
        email,
        phone,
        organization,
        role,
        subject,
        category,
        priority,
        message,
        location,
        status: 'new',
        metadata,
        archived: false,
      },
    })

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: 'Contact submission received successfully',
        submissionId: submission.id,
        status: 'new',
      },
      { status: 201 },
    )
  } catch (error) {
    console.error('Contact submissions POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to submit contact form',
      },
      { status: 500 },
    )
  }
}
