import type { PayloadRequest } from 'payload'

interface TransformedResource {
  id: string
  title: string
  description: string
  summary: string
  type: string
  category: string
  file?: TransformedMedia
  coverImage?: TransformedMedia
  additionalFiles?: Array<{
    title: string
    file: TransformedMedia
    description?: string
  }>
  metadata: {
    authors?: Array<{
      name: string
      organization?: string
      role?: string
    }>
    publishDate: string
    lastUpdated?: string
    version: string
    language: string
    pageCount?: number
    fileSize?: string
    isbn?: string
    doi?: string
  }
  access: {
    level: string
    requiresRegistration: boolean
    downloadLimit?: number
  }
  analytics: {
    downloadCount: number
    viewCount: number
    lastDownloaded?: string
  }
  relatedResources?: any[]
  relatedProjects?: any[]
  keywords?: string[]
  featured: boolean
  published: boolean
  externalUrl?: string
  slug: string
  createdAt: string
  updatedAt: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
  mimeType?: string
  filesize?: number
}

interface ResourcesResponse {
  resources: TransformedResource[]
  totalResources: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility functions
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
    mimeType: media.mimeType,
    filesize: media.filesize,
  }
}

const transformResource = (resource: any): TransformedResource => {
  return {
    id: resource.id,
    title: resource.title,
    description: extractTextFromLexical(resource.description),
    summary: resource.summary,
    type: resource.type,
    category: resource.category,
    file: transformMedia(resource.file),
    coverImage: transformMedia(resource.coverImage),
    additionalFiles: Array.isArray(resource.additionalFiles) 
      ? resource.additionalFiles.map((item: any) => ({
          title: item.title,
          file: transformMedia(item.file),
          description: item.description,
        })).filter((item: any) => item.file)
      : [],
    metadata: {
      authors: Array.isArray(resource.metadata?.authors) 
        ? resource.metadata.authors.map((author: any) => ({
            name: author.name,
            organization: author.organization,
            role: author.role,
          }))
        : [],
      publishDate: resource.metadata?.publishDate,
      lastUpdated: resource.metadata?.lastUpdated,
      version: resource.metadata?.version || '1.0',
      language: resource.metadata?.language || 'en',
      pageCount: resource.metadata?.pageCount,
      fileSize: resource.metadata?.fileSize,
      isbn: resource.metadata?.isbn,
      doi: resource.metadata?.doi,
    },
    access: {
      level: resource.access?.level || 'public',
      requiresRegistration: resource.access?.requiresRegistration || false,
      downloadLimit: resource.access?.downloadLimit,
    },
    analytics: {
      downloadCount: resource.analytics?.downloadCount || 0,
      viewCount: resource.analytics?.viewCount || 0,
      lastDownloaded: resource.analytics?.lastDownloaded,
    },
    relatedResources: resource.relatedResources || [],
    relatedProjects: resource.relatedProjects || [],
    keywords: Array.isArray(resource.keywords) 
      ? resource.keywords.map((keyword: any) => keyword.keyword).filter(Boolean)
      : [],
    featured: resource.featured || false,
    published: resource.published !== false,
    externalUrl: resource.externalUrl,
    slug: resource.slug,
    createdAt: resource.createdAt,
    updatedAt: resource.updatedAt,
  }
}

// Main Resources Handler
export const resourcesHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      type,
      category,
      language,
      featured,
      access,
      limit = '20',
      page = '1',
      sort = '-updatedAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      published: { equals: true },
    }

    if (type) where.type = { equals: type }
    if (category) where.category = { equals: category }
    if (language) where['metadata.language'] = { equals: language }
    if (featured === 'true') where.featured = { equals: true }
    if (access) where['access.level'] = { equals: access }
    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } },
        { 'keywords.keyword': { contains: search } },
        { 'metadata.authors.name': { contains: search } },
      ]
    }

    // Fetch resources with populated relationships
    const resourcesResult = await payload.find({
      collection: 'resources',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate related resources and projects
    })

    // Transform resources
    const transformedResources: TransformedResource[] = resourcesResult.docs.map(transformResource)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(resourcesResult.totalDocs / currentLimit)

    const response: ResourcesResponse = {
      resources: transformedResources,
      totalResources: resourcesResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in resources endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single resource by ID or slug
export const resourceByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let resource
    try {
      resource = await payload.findByID({
        collection: 'resources',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'resources',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      resource = result.docs[0]
    }

    if (!resource) {
      return res.status(404).json({
        error: 'Resource not found',
        message: `No resource found with ID or slug: ${id}`,
      })
    }

    // Check if published (unless user is authenticated)
    if (!resource.published && !req.user) {
      return res.status(404).json({
        error: 'Resource not found',
        message: 'Resource is not published',
      })
    }

    // Check access level
    const accessLevel = resource.access?.level || 'public'
    if (accessLevel !== 'public' && !req.user) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'This resource requires authentication',
      })
    }

    const transformedResource = transformResource(resource)

    // Increment view count (in a real implementation, you might want to do this asynchronously)
    try {
      await payload.update({
        collection: 'resources',
        id: resource.id,
        data: {
          analytics: {
            ...resource.analytics,
            viewCount: (resource.analytics?.viewCount || 0) + 1,
          },
        },
      })
    } catch (updateError) {
      console.warn('Failed to update view count:', updateError)
    }

    res.status(200).json({
      resource: transformedResource,
    })
  } catch (error) {
    console.error('Error in resource by ID endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Download resource handler
export const downloadResourceHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Find the resource
    const resource = await payload.findByID({
      collection: 'resources',
      id,
      depth: 1,
    })

    if (!resource) {
      return res.status(404).json({
        error: 'Resource not found',
        message: `No resource found with ID: ${id}`,
      })
    }

    // Check if published
    if (!resource.published && !req.user) {
      return res.status(404).json({
        error: 'Resource not found',
        message: 'Resource is not published',
      })
    }

    // Check access level
    const accessLevel = resource.access?.level || 'public'
    if (accessLevel !== 'public' && !req.user) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'This resource requires authentication to download',
      })
    }

    // Check if registration is required
    if (resource.access?.requiresRegistration && !req.user) {
      return res.status(403).json({
        error: 'Registration required',
        message: 'You must be registered to download this resource',
      })
    }

    // Increment download count
    try {
      await payload.update({
        collection: 'resources',
        id: resource.id,
        data: {
          analytics: {
            ...resource.analytics,
            downloadCount: (resource.analytics?.downloadCount || 0) + 1,
            lastDownloaded: new Date().toISOString(),
          },
        },
      })
    } catch (updateError) {
      console.warn('Failed to update download count:', updateError)
    }

    // Return download information
    res.status(200).json({
      downloadUrl: resource.file?.url || resource.externalUrl,
      filename: resource.file?.filename,
      filesize: resource.file?.filesize,
      mimeType: resource.file?.mimeType,
    })
  } catch (error) {
    console.error('Error in download resource endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
