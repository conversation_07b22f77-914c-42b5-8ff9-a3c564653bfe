# Natural Products Institute CMS Documentation

## Overview

The Natural Products Institute (NPI) Content Management System is a comprehensive solution built on PayloadCMS that manages all content for the organization's digital presence. This system provides robust content management capabilities for projects, success stories, resources, news, media, partnerships, and investment opportunities.

## Table of Contents

1. [Architecture](#architecture)
2. [Collections](#collections)
3. [API Endpoints](#api-endpoints)
4. [Frontend Integration](#frontend-integration)
5. [Authentication & Authorization](#authentication--authorization)
6. [Data Validation](#data-validation)
7. [File Management](#file-management)
8. [Deployment](#deployment)
9. [Development Guide](#development-guide)
10. [Troubleshooting](#troubleshooting)

## Architecture

### Technology Stack

- **Backend**: PayloadCMS (Node.js/Express)
- **Database**: MongoDB
- **File Storage**: Vercel Blob Storage
- **Frontend**: Next.js 14 with TypeScript
- **Validation**: Zod schemas
- **Authentication**: JWT-based with role-based access control

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   PayloadCMS    │    │   Database      │
│   (Next.js)     │◄──►│   (API Layer)   │◄──►│   (MongoDB)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  File Storage   │
                       │ (Vercel Blob)   │
                       └─────────────────┘
```

## Collections

### Core Collections

#### 1. Projects
Manages development projects and initiatives.

**Key Fields:**
- `title`: Project name
- `description`: Rich text project description
- `summary`: Brief project overview (max 300 chars)
- `category`: Project category (knowledge-preservation, community-empowerment, etc.)
- `pillar`: Strategic pillar alignment
- `status`: Current project status
- `timeline`: Start/end dates and milestones
- `budget`: Financial information
- `impact`: Beneficiaries and metrics
- `location`: Geographic information
- `team`: Project personnel

**API Endpoints:**
- `GET /api/projects` - List projects with filtering
- `GET /api/projects/:id` - Get single project
- `POST /api/projects` - Create project (admin only)
- `PUT /api/projects/:id` - Update project (admin only)
- `DELETE /api/projects/:id` - Delete project (admin only)

#### 2. Success Stories
Showcases community impact and achievements.

**Key Fields:**
- `title`: Story title
- `summary`: Brief story overview
- `content`: Full story content (rich text)
- `category`: Story category
- `participants`: Beneficiaries and knowledge holders
- `impact`: Quantified outcomes
- `testimonials`: Quotes and endorsements
- `timeline`: Story timeline
- `investment`: Financial details

#### 3. Resources & Publications
Manages downloadable resources and publications.

**Key Fields:**
- `title`: Resource title
- `type`: Resource type (guide, report, video, etc.)
- `category`: Subject category
- `file`: Main resource file
- `metadata`: Authors, publish date, version info
- `access`: Access level and restrictions
- `analytics`: Download and view counts

#### 4. News & Updates
Manages news articles and announcements.

**Key Fields:**
- `title`: Article title
- `content`: Article content (rich text)
- `category`: News category
- `status`: Publication status
- `author`: Author information
- `publishDate`: Publication date
- `featured`: Featured article flag
- `urgent`: Breaking news flag

#### 5. Media Gallery
Manages multimedia content.

**Key Fields:**
- `title`: Media title
- `type`: Media type (image, video, audio, document)
- `media`: Media file
- `category`: Content category
- `credits`: Attribution information
- `usage`: Usage permissions
- `accessibility`: Alt text and transcripts

#### 6. Partnerships
Manages organizational partnerships.

**Key Fields:**
- `title`: Partnership name
- `type`: Partnership type
- `status`: Current status
- `partner`: Partner organization
- `timeline`: Partnership duration
- `scope`: Objectives and activities
- `resources`: Contributions from each party
- `impact`: Partnership outcomes

#### 7. Investment Opportunities
Manages investment and funding opportunities.

**Key Fields:**
- `title`: Opportunity title
- `sector`: Industry sector
- `investmentType`: Type of investment
- `financial`: Funding requirements and projections
- `businessModel`: Value proposition and market
- `requirements`: Investor criteria
- `impact`: Expected social/economic impact

#### 8. Contact Submissions
Manages contact form submissions and inquiries.

**Key Fields:**
- `name`: Submitter name
- `email`: Contact email
- `subject`: Inquiry subject
- `category`: Inquiry type
- `message`: Detailed message
- `status`: Processing status
- `responses`: Staff responses

### Supporting Collections

#### Counties
Geographic reference data for Kenya's counties.

#### Partners
Partner organization profiles and information.

#### Users
System users with role-based access control.

## API Endpoints

### Public Endpoints

All public endpoints support query parameters for filtering, pagination, and sorting:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `sort`: Sort field and direction (e.g., `-createdAt`)
- `search`: Text search across relevant fields
- `featured`: Filter featured content (`true`/`false`)
- `category`: Filter by category
- Collection-specific filters (e.g., `status`, `type`, `county`)

### Response Format

```json
{
  "data": [...],
  "totalDocs": 150,
  "page": 1,
  "limit": 20,
  "totalPages": 8,
  "hasNextPage": true,
  "hasPrevPage": false
}
```

### Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "Validation Error",
  "message": "Invalid input data",
  "code": "VALIDATION_ERROR",
  "statusCode": 400,
  "issues": [
    {
      "field": "email",
      "message": "Please enter a valid email address",
      "code": "invalid_email"
    }
  ],
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestId": "req_123456"
}
```

## Frontend Integration

### React Hooks

The system provides custom React hooks for easy data fetching:

```typescript
import { useProjects, useProject, useFeaturedProjects } from '@/lib/cms'

// List projects with filtering
const { data: projects, loading, error, refetch } = useProjects({
  category: 'community-empowerment',
  featured: true,
  limit: 10
})

// Single project
const { data: project, loading } = useProject('project-id-or-slug')

// Featured projects
const { data: featuredProjects } = useFeaturedProjects(6)
```

### API Client

Direct API access for custom implementations:

```typescript
import { cmsAPI } from '@/lib/cms'

// Fetch projects
const projectsResponse = await cmsAPI.projects.getAll({
  category: 'research-development',
  page: 1,
  limit: 20
})

// Submit contact form
await cmsAPI.contact.submit({
  name: 'John Doe',
  email: '<EMAIL>',
  subject: 'Partnership Inquiry',
  category: 'partnership',
  message: 'I am interested in partnering...'
})
```

### Utility Functions

Helper functions for data formatting and manipulation:

```typescript
import { 
  formatCurrency, 
  formatDate, 
  getOptimizedImageUrl,
  truncateText 
} from '@/lib/cms'

// Format currency
const formattedAmount = formatCurrency(1500000, 'KES') // "KES 1,500,000"

// Format dates
const formattedDate = formatDate('2024-01-15') // "January 15, 2024"

// Optimize images
const imageUrl = getOptimizedImageUrl(media, { width: 800, quality: 85 })

// Truncate text
const excerpt = truncateText(longText, 200) // "Text truncated to 200 chars..."
```

## Authentication & Authorization

### User Roles

1. **Super Admin**: Full system access
2. **Admin**: Content management and user administration
3. **Content Manager**: Content creation and editing
4. **Editor**: Content editing only
5. **User**: Read-only access to internal content

### Access Control

Collections implement role-based access control:

```typescript
// Example access configuration
access: {
  create: adminOrEditor,
  read: readPublishedOrAuthenticated,
  update: adminOrEditor,
  delete: adminOnly,
}
```

### API Authentication

Protected endpoints require JWT authentication:

```typescript
// Include JWT token in requests
const response = await fetch('/api/admin/projects', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
```

## Data Validation

### Input Validation

All data inputs are validated using Zod schemas:

```typescript
import { projectSchema } from '@/lib/validation'

// Validate project data
const validatedData = projectSchema.parse(inputData)
```

### Sanitization

Data is automatically sanitized to prevent XSS and injection attacks:

```typescript
import { sanitizeHtml, sanitizeText } from '@/lib/validation'

// Sanitize HTML content
const cleanHtml = sanitizeHtml(userInput)

// Sanitize plain text
const cleanText = sanitizeText(userInput, { maxLength: 500 })
```

### Error Handling

Comprehensive error handling with detailed error messages:

```typescript
try {
  const result = await cmsAPI.projects.create(projectData)
} catch (error) {
  if (error instanceof ValidationError) {
    // Handle validation errors
    error.issues.forEach(issue => {
      console.log(`${issue.field}: ${issue.message}`)
    })
  }
}
```

## File Management

### Supported File Types

- **Images**: JPEG, PNG, GIF, WebP, SVG
- **Documents**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, CSV
- **Media**: MP4, MP3, WAV, OGG

### File Processing

Images are automatically processed with multiple sizes:

- `thumbnail`: 300x300px
- `small`: 600x400px
- `medium`: 900x600px
- `large`: 1400x900px
- `hero`: 1920x800px
- `og`: 1200x630px (for social media)

### Storage Configuration

Files are stored using Vercel Blob Storage with automatic CDN distribution.

## Deployment

### Environment Variables

Required environment variables:

```env
# Database
DATABASE_URI=mongodb://localhost:27017/npi-cms

# Authentication
PAYLOAD_SECRET=your-secret-key
JWT_SECRET=your-jwt-secret

# File Storage
BLOB_READ_WRITE_TOKEN=your-vercel-blob-token

# Email (optional)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-password

# External APIs
NEXT_PUBLIC_API_URL=https://your-domain.com
```

### Build Process

```bash
# Install dependencies
npm install

# Build the application
npm run build

# Start production server
npm start
```

### Database Migration

Initial database seeding:

```bash
# Seed database with sample data
npm run seed
```

## Development Guide

### Local Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Start development server: `npm run dev`
5. Access admin panel: `http://localhost:3000/admin`

### Adding New Collections

1. Create collection configuration in `src/collections/`
2. Add to `payload.config.ts`
3. Create API endpoints in `src/endpoints/`
4. Add TypeScript types to `src/lib/cms/types.ts`
5. Create React hooks in `src/lib/cms/hooks.ts`
6. Update validation schemas if needed

### Custom Fields

Example custom field implementation:

```typescript
{
  name: 'customField',
  type: 'text',
  validate: (value) => {
    if (!value) return 'Field is required'
    if (value.length < 5) return 'Minimum 5 characters'
    return true
  },
  hooks: {
    beforeChange: [
      ({ value }) => {
        return value?.toLowerCase()
      }
    ]
  }
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check MongoDB connection string
   - Ensure database server is running
   - Verify network connectivity

2. **File Upload Issues**
   - Check Vercel Blob configuration
   - Verify file size limits
   - Ensure proper MIME type handling

3. **Authentication Problems**
   - Verify JWT secret configuration
   - Check token expiration
   - Ensure proper role assignments

4. **Performance Issues**
   - Implement proper indexing
   - Use pagination for large datasets
   - Optimize image sizes
   - Enable caching where appropriate

### Debugging

Enable debug logging:

```env
DEBUG=payload:*
NODE_ENV=development
```

### Support

For technical support:
- Check the PayloadCMS documentation
- Review error logs in the admin panel
- Contact the development team

---

This documentation provides a comprehensive overview of the NPI CMS system. For specific implementation details, refer to the source code and inline comments.
