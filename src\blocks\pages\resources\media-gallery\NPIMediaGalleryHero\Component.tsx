import React from 'react'
import Image from 'next/image'

interface NPIMediaGalleryHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPIMediaGalleryHeroBlock: React.FC<NPIMediaGalleryHeroProps> = ({
  title = 'Media Gallery',
  backgroundImage = '/assets/Gemini_Generated_Image_afyc4tafyc4tafyc.png',
}) => {
  return (
    <section className="relative min-h-[85vh] max-h-[95vh] overflow-hidden -mt-16 pt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Hero background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with balanced colors - no gradients */}
      <div className="absolute inset-0 bg-[#725242]/60" />

      {/* Top Center Title */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-30 text-center max-w-5xl px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-white">
          {title}
        </h1>
      </div>

      {/* Bottom Left Text with 6-color palette */}
      <div className="absolute bottom-8 left-8 z-30 max-w-sm">
        <div className="bg-[#EFE3BA]/90 backdrop-blur-md p-6 border-l-4 border-[#725242]">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed text-black">
            Explore our comprehensive collection of videos, photos, and multimedia content
            showcasing NPI&apos;s impact.
          </p>
        </div>
      </div>

      {/* Bottom Right Feature Card with 6-color palette */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#FFFFFF]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[#725242]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-black">Visual Stories</h3>
          <p className="text-sm sm:text-base mb-4 text-[#725242]">
            Discover the visual narrative of Kenya&apos;s natural products transformation through
            our media collection.
          </p>
          <a
            href="#media-content"
            className="text-[#25718A] hover:text-[#8A3E25] text-sm font-medium transition-colors"
          >
            Explore Gallery &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
