import type { PayloadRequest } from 'payload'

export const serveDatabaseMedia = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        error: 'Media ID is required',
      })
    }

    // Find the media item by ID
    const media = await payload.findByID({
      collection: 'media',
      id,
    })

    if (!media) {
      return res.status(404).json({
        error: 'Media not found',
      })
    }

    // Check if media is stored in database
    if (media.storageType !== 'database' || !media.base64Data) {
      return res.status(404).json({
        error: 'Media not stored in database',
      })
    }

    // Convert base64 back to buffer
    const buffer = Buffer.from(media.base64Data, 'base64')

    // Set appropriate headers
    res.setHeader('Content-Type', media.mimeType || 'application/octet-stream')
    res.setHeader('Content-Length', buffer.length)
    res.setHeader('Cache-Control', 'public, max-age=31536000') // Cache for 1 year
    res.setHeader('ETag', `"${media.id}-${media.updatedAt}"`)

    // Check if client has cached version
    const clientETag = req.headers['if-none-match']
    if (clientETag === `"${media.id}-${media.updatedAt}"`) {
      return res.status(304).end()
    }

    // Send the file
    return res.send(buffer)

  } catch (error) {
    console.error('Error serving database media:', error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

export const getDatabaseMediaInfo = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        error: 'Media ID is required',
      })
    }

    // Find the media item by ID
    const media = await payload.findByID({
      collection: 'media',
      id,
    })

    if (!media) {
      return res.status(404).json({
        error: 'Media not found',
      })
    }

    // Return media info without base64 data (for performance)
    const { base64Data, ...mediaInfo } = media
    
    return res.json({
      ...mediaInfo,
      hasBase64Data: !!base64Data,
      storageType: media.storageType || 'unknown',
    })

  } catch (error) {
    console.error('Error getting database media info:', error)
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
