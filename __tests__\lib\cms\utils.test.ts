import { describe, it, expect } from '@jest/globals'
import {
  getMediaUrl,
  getOptimizedImageUrl,
  formatFileSize,
  formatDate,
  formatRelativeDate,
  formatCurrency,
  formatNumber,
  truncateText,
  stripHtml,
  extractExcerpt,
  slugify,
  getReadingTime,
  getStatusColor,
  getPriorityColor,
  getCategoryIcon,
  isValidEmail,
  isValidPhone,
  isValidUrl,
  filterBySearchTerm,
  sortByDate,
  groupByCategory,
} from '../../../src/lib/cms/utils'

describe('CMS Utilities', () => {
  describe('Media utilities', () => {
    const mockMedia = {
      id: 'test-id',
      filename: 'test.jpg',
      url: 'https://example.com/test.jpg',
      alt: 'Test image',
      width: 1920,
      height: 1080,
    }

    describe('getMediaUrl', () => {
      it('should return media URL', () => {
        const result = getMediaUrl(mockMedia)
        expect(result).toBe('https://example.com/test.jpg')
      })

      it('should return empty string for undefined media', () => {
        const result = getMediaUrl(undefined)
        expect(result).toBe('')
      })

      it('should add size parameter when specified', () => {
        const result = getMediaUrl(mockMedia, 'medium')
        expect(result).toContain('size=medium')
      })
    })

    describe('getOptimizedImageUrl', () => {
      it('should add optimization parameters', () => {
        const result = getOptimizedImageUrl(mockMedia, {
          width: 800,
          height: 600,
          quality: 85,
          format: 'webp',
        })

        expect(result).toContain('w=800')
        expect(result).toContain('h=600')
        expect(result).toContain('q=85')
        expect(result).toContain('f=webp')
      })

      it('should return empty string for undefined media', () => {
        const result = getOptimizedImageUrl(undefined)
        expect(result).toBe('')
      })
    })

    describe('formatFileSize', () => {
      it('should format bytes correctly', () => {
        expect(formatFileSize(1024)).toBe('1.0 KB')
        expect(formatFileSize(1048576)).toBe('1.0 MB')
        expect(formatFileSize(1073741824)).toBe('1.0 GB')
      })

      it('should return empty string for undefined', () => {
        expect(formatFileSize(undefined)).toBe('')
      })

      it('should handle zero bytes', () => {
        expect(formatFileSize(0)).toBe('0.0 B')
      })
    })
  })

  describe('Date utilities', () => {
    describe('formatDate', () => {
      it('should format date with default options', () => {
        const result = formatDate('2024-01-15T10:30:00.000Z')
        expect(result).toBe('January 15, 2024')
      })

      it('should format date with custom options', () => {
        const result = formatDate('2024-01-15T10:30:00.000Z', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        })
        expect(result).toBe('Jan 15, 2024')
      })
    })

    describe('formatRelativeDate', () => {
      const now = new Date()
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

      it('should return "Today" for today', () => {
        const result = formatRelativeDate(now.toISOString())
        expect(result).toBe('Today')
      })

      it('should return "Yesterday" for yesterday', () => {
        const result = formatRelativeDate(yesterday.toISOString())
        expect(result).toBe('Yesterday')
      })

      it('should return days ago for recent dates', () => {
        const result = formatRelativeDate(lastWeek.toISOString())
        expect(result).toContain('days ago')
      })

      it('should return weeks ago for older dates', () => {
        const result = formatRelativeDate(lastMonth.toISOString())
        expect(result).toContain('ago')
      })
    })
  })

  describe('Currency utilities', () => {
    describe('formatCurrency', () => {
      it('should format KES currency', () => {
        const result = formatCurrency(1500000, 'KES')
        expect(result).toContain('1,500,000')
      })

      it('should format USD currency', () => {
        const result = formatCurrency(1500, 'USD')
        expect(result).toContain('1,500')
      })

      it('should return empty string for undefined amount', () => {
        const result = formatCurrency(undefined)
        expect(result).toBe('')
      })
    })

    describe('formatNumber', () => {
      it('should format numbers with commas', () => {
        const result = formatNumber(1234567)
        expect(result).toBe('1,234,567')
      })

      it('should return empty string for undefined', () => {
        const result = formatNumber(undefined)
        expect(result).toBe('')
      })
    })
  })

  describe('Text utilities', () => {
    describe('truncateText', () => {
      it('should truncate long text', () => {
        const text = 'This is a very long text that should be truncated'
        const result = truncateText(text, 20)
        expect(result).toBe('This is a very lo...')
      })

      it('should not truncate short text', () => {
        const text = 'Short text'
        const result = truncateText(text, 20)
        expect(result).toBe('Short text')
      })

      it('should use custom suffix', () => {
        const text = 'This is a long text'
        const result = truncateText(text, 10, ' [more]')
        expect(result).toBe('This [more]')
      })
    })

    describe('stripHtml', () => {
      it('should remove HTML tags', () => {
        const html = '<p>This is <strong>bold</strong> text.</p>'
        const result = stripHtml(html)
        expect(result).toBe('This is bold text.')
      })

      it('should handle nested tags', () => {
        const html = '<div><p>Nested <em>content</em></p></div>'
        const result = stripHtml(html)
        expect(result).toBe('Nested content')
      })
    })

    describe('extractExcerpt', () => {
      it('should extract plain text excerpt', () => {
        const html = '<p>This is a <strong>test</strong> content with HTML tags.</p>'
        const result = extractExcerpt(html, 20)
        expect(result).toBe('This is a test co...')
      })
    })

    describe('slugify', () => {
      it('should create valid slug', () => {
        const text = 'This is a Test Title!'
        const result = slugify(text)
        expect(result).toBe('this-is-a-test-title')
      })

      it('should handle special characters', () => {
        const text = 'Test@Title#With$Special%Characters'
        const result = slugify(text)
        expect(result).toBe('testtitlewithspecialcharacters')
      })

      it('should handle multiple spaces', () => {
        const text = 'Test    Title    With    Spaces'
        const result = slugify(text)
        expect(result).toBe('test-title-with-spaces')
      })
    })

    describe('getReadingTime', () => {
      it('should calculate reading time', () => {
        const content = 'word '.repeat(200) // 200 words
        const result = getReadingTime(content)
        expect(result).toBe(1) // 200 words / 200 wpm = 1 minute
      })

      it('should round up reading time', () => {
        const content = 'word '.repeat(250) // 250 words
        const result = getReadingTime(content)
        expect(result).toBe(2) // 250 words / 200 wpm = 1.25, rounded up to 2
      })
    })
  })

  describe('Status utilities', () => {
    describe('getStatusColor', () => {
      it('should return correct color for active status', () => {
        const result = getStatusColor('active')
        expect(result).toContain('green')
      })

      it('should return correct color for completed status', () => {
        const result = getStatusColor('completed')
        expect(result).toContain('blue')
      })

      it('should return default color for unknown status', () => {
        const result = getStatusColor('unknown-status')
        expect(result).toContain('gray')
      })
    })

    describe('getPriorityColor', () => {
      it('should return correct color for high priority', () => {
        const result = getPriorityColor('high')
        expect(result).toContain('orange')
      })

      it('should return correct color for urgent priority', () => {
        const result = getPriorityColor('urgent')
        expect(result).toContain('red')
      })
    })

    describe('getCategoryIcon', () => {
      it('should return icon for known category', () => {
        const result = getCategoryIcon('community-empowerment')
        expect(result).toBe('🤝')
      })

      it('should return default icon for unknown category', () => {
        const result = getCategoryIcon('unknown-category')
        expect(result).toBe('📄')
      })
    })
  })

  describe('Validation utilities', () => {
    describe('isValidEmail', () => {
      it('should validate correct email', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true)
        expect(isValidEmail('<EMAIL>')).toBe(true)
      })

      it('should reject invalid email', () => {
        expect(isValidEmail('invalid-email')).toBe(false)
        expect(isValidEmail('test@')).toBe(false)
        expect(isValidEmail('@example.com')).toBe(false)
      })
    })

    describe('isValidPhone', () => {
      it('should validate correct phone numbers', () => {
        expect(isValidPhone('+254700000000')).toBe(true)
        expect(isValidPhone('254700000000')).toBe(true)
        expect(isValidPhone('0700000000')).toBe(true)
      })

      it('should reject invalid phone numbers', () => {
        expect(isValidPhone('invalid-phone')).toBe(false)
        expect(isValidPhone('123')).toBe(false)
      })
    })

    describe('isValidUrl', () => {
      it('should validate correct URLs', () => {
        expect(isValidUrl('https://example.com')).toBe(true)
        expect(isValidUrl('http://example.com/path?query=value')).toBe(true)
      })

      it('should reject invalid URLs', () => {
        expect(isValidUrl('invalid-url')).toBe(false)
        expect(isValidUrl('not a url')).toBe(false)
      })
    })
  })

  describe('Content filtering utilities', () => {
    const testItems = [
      {
        id: '1',
        title: 'Community Empowerment Project',
        summary: 'Empowering local communities',
        tags: ['community', 'empowerment'],
        category: 'community-empowerment',
        createdAt: '2024-01-01T00:00:00.000Z',
      },
      {
        id: '2',
        title: 'Research Initiative',
        summary: 'Research on traditional medicine',
        tags: ['research', 'medicine'],
        category: 'research-development',
        createdAt: '2024-01-02T00:00:00.000Z',
      },
      {
        id: '3',
        title: 'Community Health Program',
        summary: 'Health program for communities',
        tags: ['health', 'community'],
        category: 'community-empowerment',
        createdAt: '2024-01-03T00:00:00.000Z',
      },
    ]

    describe('filterBySearchTerm', () => {
      it('should filter by title', () => {
        const result = filterBySearchTerm(testItems, 'Community')
        expect(result).toHaveLength(2)
        expect(result[0].title).toContain('Community')
      })

      it('should filter by summary', () => {
        const result = filterBySearchTerm(testItems, 'traditional')
        expect(result).toHaveLength(1)
        expect(result[0].summary).toContain('traditional')
      })

      it('should filter by tags', () => {
        const result = filterBySearchTerm(testItems, 'research')
        expect(result).toHaveLength(1)
        expect(result[0].tags).toContain('research')
      })

      it('should return all items for empty search term', () => {
        const result = filterBySearchTerm(testItems, '')
        expect(result).toHaveLength(3)
      })
    })

    describe('sortByDate', () => {
      it('should sort by date descending by default', () => {
        const result = sortByDate(testItems)
        expect(result[0].createdAt).toBe('2024-01-03T00:00:00.000Z')
        expect(result[2].createdAt).toBe('2024-01-01T00:00:00.000Z')
      })

      it('should sort by date ascending when specified', () => {
        const result = sortByDate(testItems, 'asc')
        expect(result[0].createdAt).toBe('2024-01-01T00:00:00.000Z')
        expect(result[2].createdAt).toBe('2024-01-03T00:00:00.000Z')
      })
    })

    describe('groupByCategory', () => {
      it('should group items by category', () => {
        const result = groupByCategory(testItems)
        expect(result['community-empowerment']).toHaveLength(2)
        expect(result['research-development']).toHaveLength(1)
      })

      it('should handle empty array', () => {
        const result = groupByCategory([])
        expect(result).toEqual({})
      })
    })
  })
})
