# PowerShell script to verify moved components and remove old directories
# This script checks if components exist in their new locations before removing old ones

Write-Host "🔍 Verifying moved components and cleaning up old directories..." -ForegroundColor Green

# Define component mappings: old_path -> new_path
$componentMappings = @{
    # Home page components
    "src\blocks\NPIIntroduction" = "src\blocks\pages\home\NPIIntroduction"
    "src\blocks\NPIMissionVision" = "src\blocks\pages\home\NPIMissionVision"
    "src\blocks\NPIFeaturedPrograms" = "src\blocks\pages\home\NPIFeaturedPrograms"
    "src\blocks\NPISuccessStories" = "src\blocks\pages\home\NPISuccessStories"
    "src\blocks\NPIPartners" = "src\blocks\pages\home\NPIPartners"
    "src\blocks\NPILatestUpdates" = "src\blocks\pages\home\NPILatestUpdates"
    
    # About page components
    "src\blocks\NPIAboutHero" = "src\blocks\pages\about\main\NPIAboutHero"
    "src\blocks\NPIHistoryTimeline" = "src\blocks\pages\about\main\NPIHistoryTimeline"
    "src\blocks\NPIOperationsHero" = "src\blocks\pages\about\operations-structure\NPIOperationsHero"
    "src\blocks\NPIOperationsStructure" = "src\blocks\pages\about\operations-structure\NPIOperationsStructure"
    "src\blocks\NPIStrategicAlignmentHero" = "src\blocks\pages\about\strategic-alignment\NPIStrategicAlignmentHero"
    "src\blocks\NPIStrategicAlignment" = "src\blocks\pages\about\strategic-alignment\NPIStrategicAlignment"
    
    # Partnerships page components
    "src\blocks\NPIPartnershipsHero" = "src\blocks\pages\partnerships\main\NPIPartnershipsHero"
    "src\blocks\NPIPartnershipModels" = "src\blocks\pages\partnerships\main\NPIPartnershipModels"
    "src\blocks\NPIInvestmentOpportunitiesHero" = "src\blocks\pages\partnerships\investment-opportunities\NPIInvestmentOpportunitiesHero"
    "src\blocks\NPIInvestmentOpportunities" = "src\blocks\pages\partnerships\investment-opportunities\NPIInvestmentOpportunities"
    "src\blocks\NPIPartnersHero" = "src\blocks\pages\partnerships\partners\NPIPartnersHero"
    "src\blocks\NPIPartnersShowcase" = "src\blocks\pages\partnerships\partners\NPIPartnersShowcase"
    
    # Resources page components
    "src\blocks\NPIResourcesLibrary" = "src\blocks\pages\resources\main\NPIResourcesLibrary"
    "src\blocks\NPIMediaGalleryHero" = "src\blocks\pages\resources\media-gallery\NPIMediaGalleryHero"
    "src\blocks\NPIMediaGallery" = "src\blocks\pages\resources\media-gallery\NPIMediaGallery"
    "src\blocks\NPIMediaGalleryContent" = "src\blocks\pages\resources\media-gallery\NPIMediaGalleryContent"
    
    # Projects page components
    "src\blocks\NPIProjectsHero" = "src\blocks\pages\projects\NPIProjectsHero"
    "src\blocks\NPIProjectsListing" = "src\blocks\pages\projects\NPIProjectsListing"
    "src\blocks\NPIProgramsHero" = "src\blocks\pages\projects\NPIProgramsHero"
    "src\blocks\NPIProgramsListing" = "src\blocks\pages\projects\NPIProgramsListing"
    
    # Strategic pillars page components
    "src\blocks\NPIPillarsHero" = "src\blocks\pages\strategic-pillars\NPIPillarsHero"
    "src\blocks\NPIStrategicPillars" = "src\blocks\pages\strategic-pillars\NPIStrategicPillars"
    
    # Success stories page components
    "src\blocks\NPISuccessStoriesHero" = "src\blocks\pages\success-stories\NPISuccessStoriesHero"
    "src\blocks\NPISuccessStoriesGrid" = "src\blocks\pages\success-stories\NPISuccessStoriesGrid"
    
    # Contact page components
    "src\blocks\NPIContactForm" = "src\blocks\pages\contact\NPIContactForm"
    
    # Events page components
    "src\blocks\NPIEventsHero" = "src\blocks\pages\events\NPIEventsHero"
    "src\blocks\NPIEventsCalendar" = "src\blocks\pages\events\NPIEventsCalendar"
    
    # News page components
    "src\blocks\NPINewsHero" = "src\blocks\pages\news\NPINewsHero"
    "src\blocks\NPINewsListing" = "src\blocks\pages\news\NPINewsListing"
    
    # Get involved page components
    "src\blocks\NPIGetInvolvedHero" = "src\blocks\pages\get-involved\NPIGetInvolvedHero"
    "src\blocks\NPIEngagementOpportunities" = "src\blocks\pages\get-involved\NPIEngagementOpportunities"
    
    # Shared components
    "src\blocks\NPIStatistics" = "src\blocks\shared\NPIStatistics"
}

$verifiedComponents = @()
$failedComponents = @()

# Verify each component exists in new location
foreach ($mapping in $componentMappings.GetEnumerator()) {
    $oldPath = $mapping.Key
    $newPath = $mapping.Value
    $componentName = Split-Path $oldPath -Leaf
    
    Write-Host "Checking $componentName..." -ForegroundColor Yellow
    
    # Check if old directory exists
    if (-not (Test-Path $oldPath)) {
        Write-Host "  ⚠️  Old directory not found: $oldPath" -ForegroundColor Orange
        continue
    }
    
    # Check if new directory exists
    if (-not (Test-Path $newPath)) {
        Write-Host "  ❌ New directory not found: $newPath" -ForegroundColor Red
        $failedComponents += $componentName
        continue
    }
    
    # Check if Component.tsx exists in new location
    $newComponentFile = Join-Path $newPath "Component.tsx"
    if (-not (Test-Path $newComponentFile)) {
        Write-Host "  ❌ Component.tsx not found in new location: $newComponentFile" -ForegroundColor Red
        $failedComponents += $componentName
        continue
    }
    
    # Check if Component.tsx exists in old location
    $oldComponentFile = Join-Path $oldPath "Component.tsx"
    if (-not (Test-Path $oldComponentFile)) {
        Write-Host "  ⚠️  Component.tsx not found in old location: $oldComponentFile" -ForegroundColor Orange
        continue
    }
    
    # Compare file sizes to ensure they're the same
    $oldSize = (Get-Item $oldComponentFile).Length
    $newSize = (Get-Item $newComponentFile).Length
    
    if ($oldSize -eq $newSize) {
        Write-Host "  ✅ Verified: $componentName (files match)" -ForegroundColor Green
        $verifiedComponents += @{
            Name = $componentName
            OldPath = $oldPath
            NewPath = $newPath
        }
    } else {
        Write-Host "  ❌ File size mismatch for $componentName" -ForegroundColor Red
        $failedComponents += $componentName
    }
}

Write-Host "`n📊 Verification Summary:" -ForegroundColor Cyan
Write-Host "✅ Verified components: $($verifiedComponents.Count)" -ForegroundColor Green
Write-Host "❌ Failed components: $($failedComponents.Count)" -ForegroundColor Red

if ($failedComponents.Count -gt 0) {
    Write-Host "`nFailed components:" -ForegroundColor Red
    $failedComponents | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
    Write-Host "`n⚠️  Not proceeding with cleanup due to verification failures." -ForegroundColor Yellow
    exit 1
}

if ($verifiedComponents.Count -eq 0) {
    Write-Host "`n⚠️  No components to clean up." -ForegroundColor Yellow
    exit 0
}

# Ask for confirmation before removing
Write-Host "`n🗑️  Ready to remove $($verifiedComponents.Count) old component directories." -ForegroundColor Yellow
$confirmation = Read-Host "Do you want to proceed with removal? (y/N)"

if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
    Write-Host "Cleanup cancelled." -ForegroundColor Yellow
    exit 0
}

# Remove verified old directories
Write-Host "`n🧹 Removing old component directories..." -ForegroundColor Green
$removedCount = 0

foreach ($component in $verifiedComponents) {
    try {
        Remove-Item -Path $component.OldPath -Recurse -Force
        Write-Host "  ✅ Removed: $($component.Name)" -ForegroundColor Green
        $removedCount++
    } catch {
        Write-Host "  ❌ Failed to remove: $($component.Name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 Cleanup complete!" -ForegroundColor Green
Write-Host "Removed $removedCount out of $($verifiedComponents.Count) old component directories." -ForegroundColor Cyan
