import type { AccessArgs } from 'payload'
import type { User } from '@/payload-types'

type PublishedOnly = (args: AccessArgs<User>) => boolean

export const publishedOnly: PublishedOnly = ({ req: { user }, data }) => {
  // If user is authenticated (admin/editor), allow access to all content
  if (user && (user.role === 'admin' || user.role === 'super-admin' || user.role === 'editor' || user.role === 'content-manager')) {
    return true
  }

  // For public users, only allow access to published content
  return Boolean(data?.published !== false)
}
