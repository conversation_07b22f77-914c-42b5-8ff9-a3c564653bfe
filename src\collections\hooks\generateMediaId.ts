import type { CollectionBeforeChangeHook } from 'payload'
import { v4 as uuidv4 } from 'uuid'

export const generateMediaId: CollectionBeforeChangeHook = ({ data, operation, req }) => {
  // Only generate ID for new media items (create operation)
  if (operation === 'create') {
    try {
      // Generate a unique media ID
      const timestamp = Date.now().toString(36) // Base36 timestamp for shorter string
      const randomPart = Math.random().toString(36).substring(2, 8) // 6 random characters
      const mediaId = `MEDIA-${timestamp}-${randomPart}`.toUpperCase()
      
      // Add the generated ID to the media data
      data.mediaId = mediaId
      
      req.payload.logger.info(`Generated media ID: ${mediaId}`)
      
    } catch (error) {
      req.payload.logger.error('Error generating media ID:', error)
      throw new Error('Failed to generate media ID')
    }
  }
  
  return data
}
