import { describe, it, expect } from '@jest/globals'
import { ZodError } from 'zod'
import {
  projectSchema,
  successStorySchema,
  resourceSchema,
  newsSchema,
  contactSubmissionSchema,
  ValidationError,
  validateInput,
  sanitizeHtml,
  sanitizeText,
  sanitizeEmail,
  sanitizeUrl,
  sanitizeSlug,
} from '../../src/lib/validation'

describe('Validation Schemas', () => {
  describe('projectSchema', () => {
    const validProjectData = {
      title: 'Test Project',
      description: {
        root: {
          children: [],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      summary: 'A test project summary',
      category: 'community-empowerment',
      pillar: 'community-innovation',
      status: 'active',
      timeline: {
        startDate: '2024-01-01T00:00:00.000Z',
      },
      slug: 'test-project',
    }

    it('should validate valid project data', () => {
      const result = projectSchema.safeParse(validProjectData)
      expect(result.success).toBe(true)
    })

    it('should reject project without required fields', () => {
      const invalidData = {
        summary: 'Missing title',
      }

      const result = projectSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues).toContainEqual(
          expect.objectContaining({
            path: ['title'],
            message: 'Title is required',
          })
        )
      }
    })

    it('should reject invalid category', () => {
      const invalidData = {
        ...validProjectData,
        category: 'invalid-category',
      }

      const result = projectSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should reject invalid status', () => {
      const invalidData = {
        ...validProjectData,
        status: 'invalid-status',
      }

      const result = projectSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should validate project with optional fields', () => {
      const dataWithOptionals = {
        ...validProjectData,
        budget: {
          totalBudget: 100000,
          currency: 'KES',
          fundingSources: [
            {
              source: 'Grant',
              amount: 50000,
              percentage: 50,
            },
          ],
        },
        impact: {
          beneficiaries: 1000,
          communities: 5,
          jobsCreated: 20,
          metrics: [
            {
              metric: 'Training Sessions',
              value: '10',
              unit: 'sessions',
            },
          ],
        },
        featured: true,
        tags: [
          { tag: 'community' },
          { tag: 'empowerment' },
        ],
      }

      const result = projectSchema.safeParse(dataWithOptionals)
      expect(result.success).toBe(true)
    })
  })

  describe('successStorySchema', () => {
    const validStoryData = {
      title: 'Test Success Story',
      summary: 'A test success story',
      content: {
        root: {
          children: [],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      category: 'community-innovation',
      participants: {
        beneficiary: {
          name: 'John Doe',
          role: 'Farmer',
        },
      },
      timeline: {
        startDate: '2023-01-01T00:00:00.000Z',
      },
      investment: {
        totalAmount: 50000,
        currency: 'KES',
      },
      slug: 'test-success-story',
    }

    it('should validate valid success story data', () => {
      const result = successStorySchema.safeParse(validStoryData)
      expect(result.success).toBe(true)
    })

    it('should reject story without required investment', () => {
      const invalidData = {
        ...validStoryData,
        investment: undefined,
      }

      const result = successStorySchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should validate story with testimonials', () => {
      const dataWithTestimonials = {
        ...validStoryData,
        testimonials: [
          {
            quote: 'This project changed my life!',
            author: 'Jane Smith',
            role: 'Beneficiary',
            organization: 'Local Community',
          },
        ],
      }

      const result = successStorySchema.safeParse(dataWithTestimonials)
      expect(result.success).toBe(true)
    })
  })

  describe('contactSubmissionSchema', () => {
    const validSubmissionData = {
      name: 'John Doe',
      email: '<EMAIL>',
      subject: 'Partnership Inquiry',
      category: 'partnership',
      message: 'I am interested in partnering with your organization.',
    }

    it('should validate valid contact submission', () => {
      const result = contactSubmissionSchema.safeParse(validSubmissionData)
      expect(result.success).toBe(true)
    })

    it('should reject invalid email', () => {
      const invalidData = {
        ...validSubmissionData,
        email: 'invalid-email',
      }

      const result = contactSubmissionSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues).toContainEqual(
          expect.objectContaining({
            path: ['email'],
            message: 'Please enter a valid email address',
          })
        )
      }
    })

    it('should reject message that is too long', () => {
      const invalidData = {
        ...validSubmissionData,
        message: 'a'.repeat(2001), // Exceeds 2000 character limit
      }

      const result = contactSubmissionSchema.safeParse(invalidData)
      expect(result.success).toBe(false)
    })

    it('should validate with optional fields', () => {
      const dataWithOptionals = {
        ...validSubmissionData,
        phone: '+254700000000',
        organization: 'Test Organization',
        role: 'Manager',
        priority: 'high',
        location: {
          county: 'nairobi',
          city: 'Nairobi',
          country: 'Kenya',
        },
      }

      const result = contactSubmissionSchema.safeParse(dataWithOptionals)
      expect(result.success).toBe(true)
    })
  })
})

describe('Validation Functions', () => {
  describe('validateInput', () => {
    it('should return validated data for valid input', () => {
      const validData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test',
        category: 'general',
        message: 'Test message',
      }

      const result = validateInput(contactSubmissionSchema, validData)
      expect(result).toEqual(expect.objectContaining(validData))
    })

    it('should throw ValidationError for invalid input', () => {
      const invalidData = {
        name: 'John Doe',
        email: 'invalid-email',
      }

      expect(() => {
        validateInput(contactSubmissionSchema, invalidData)
      }).toThrow(ValidationError)
    })

    it('should include validation issues in error', () => {
      const invalidData = {
        email: 'invalid-email',
      }

      try {
        validateInput(contactSubmissionSchema, invalidData)
      } catch (error) {
        expect(error).toBeInstanceOf(ValidationError)
        if (error instanceof ValidationError) {
          expect(error.issues).toHaveLength(4) // name, subject, category, message are required
          expect(error.issues).toContainEqual(
            expect.objectContaining({
              field: 'email',
              message: 'Please enter a valid email address',
            })
          )
        }
      }
    })
  })
})

describe('Sanitization Functions', () => {
  describe('sanitizeHtml', () => {
    it('should remove dangerous HTML tags', () => {
      const dangerousHtml = '<script>alert("xss")</script><p>Safe content</p>'
      const result = sanitizeHtml(dangerousHtml)
      expect(result).toBe('<p>Safe content</p>')
    })

    it('should preserve allowed HTML tags', () => {
      const safeHtml = '<p>This is <strong>bold</strong> and <em>italic</em> text.</p>'
      const result = sanitizeHtml(safeHtml)
      expect(result).toBe(safeHtml)
    })

    it('should remove dangerous attributes', () => {
      const htmlWithDangerousAttrs = '<p onclick="alert(\'xss\')">Click me</p>'
      const result = sanitizeHtml(htmlWithDangerousAttrs)
      expect(result).toBe('<p>Click me</p>')
    })

    it('should strip all tags when stripTags is true', () => {
      const html = '<p>This is <strong>bold</strong> text.</p>'
      const result = sanitizeHtml(html, { stripTags: true })
      expect(result).toBe('This is bold text.')
    })
  })

  describe('sanitizeText', () => {
    it('should trim whitespace', () => {
      const text = '  Hello World  '
      const result = sanitizeText(text)
      expect(result).toBe('Hello World')
    })

    it('should remove control characters', () => {
      const text = 'Hello\x00World\x1F'
      const result = sanitizeText(text)
      expect(result).toBe('HelloWorld')
    })

    it('should normalize whitespace', () => {
      const text = 'Hello    World\n\n\nTest'
      const result = sanitizeText(text)
      expect(result).toBe('Hello World Test')
    })

    it('should truncate text when maxLength is specified', () => {
      const text = 'This is a very long text that should be truncated'
      const result = sanitizeText(text, { maxLength: 20 })
      expect(result).toBe('This is a very long')
    })

    it('should remove newlines when allowNewlines is false', () => {
      const text = 'Line 1\nLine 2\rLine 3'
      const result = sanitizeText(text, { allowNewlines: false })
      expect(result).toBe('Line 1 Line 2 Line 3')
    })
  })

  describe('sanitizeEmail', () => {
    it('should convert email to lowercase', () => {
      const email = '<EMAIL>'
      const result = sanitizeEmail(email)
      expect(result).toBe('<EMAIL>')
    })

    it('should remove invalid characters', () => {
      const email = 'test<script>@example.com'
      const result = sanitizeEmail(email)
      expect(result).toBe('<EMAIL>')
    })

    it('should handle empty input', () => {
      const result = sanitizeEmail('')
      expect(result).toBe('')
    })
  })

  describe('sanitizeUrl', () => {
    it('should add https protocol when missing', () => {
      const url = 'example.com'
      const result = sanitizeUrl(url)
      expect(result).toBe('https://example.com/')
    })

    it('should preserve valid URLs', () => {
      const url = 'https://example.com/path?query=value'
      const result = sanitizeUrl(url)
      expect(result).toBe(url)
    })

    it('should remove dangerous protocols', () => {
      const url = 'javascript:alert("xss")'
      const result = sanitizeUrl(url)
      expect(result).toBe('')
    })

    it('should reject URLs with disallowed protocols', () => {
      const url = 'ftp://example.com'
      const result = sanitizeUrl(url, { allowedProtocols: ['http', 'https'] })
      expect(result).toBe('')
    })
  })

  describe('sanitizeSlug', () => {
    it('should convert to lowercase', () => {
      const slug = 'Test-Slug'
      const result = sanitizeSlug(slug)
      expect(result).toBe('test-slug')
    })

    it('should replace spaces with hyphens', () => {
      const slug = 'test slug with spaces'
      const result = sanitizeSlug(slug)
      expect(result).toBe('test-slug-with-spaces')
    })

    it('should remove special characters', () => {
      const slug = 'test@slug#with$special%chars'
      const result = sanitizeSlug(slug)
      expect(result).toBe('testslugwithspecialchars')
    })

    it('should remove leading and trailing hyphens', () => {
      const slug = '-test-slug-'
      const result = sanitizeSlug(slug)
      expect(result).toBe('test-slug')
    })

    it('should collapse multiple hyphens', () => {
      const slug = 'test---slug'
      const result = sanitizeSlug(slug)
      expect(result).toBe('test-slug')
    })
  })
})
