import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'

// Mock the Header component since we're testing navigation behavior
const MockHeader = () => (
  <header role="banner">
    <nav role="navigation" aria-label="Main navigation">
      <a href="/" aria-label="NPI Home">
        <img src="/logo.png" alt="NPI Logo" />
      </a>
      <ul>
        <li>
          <a href="/about">About NPI</a>
        </li>
        <li>
          <a href="/strategic-pillars">Strategic Pillars</a>
        </li>
        <li>
          <a href="/ikia">IKIA</a>
        </li>
        <li>
          <a href="/programs">Programs</a>
        </li>
        <li>
          <a href="/partnerships">Partnerships</a>
        </li>
        <li>
          <a href="/success-stories">Success Stories</a>
        </li>
        <li>
          <a href="/resources">Resources</a>
        </li>
        <li>
          <a href="/news">News</a>
        </li>
        <li>
          <a href="/events">Events</a>
        </li>

        <li>
          <a href="/contact">Contact</a>
        </li>
      </ul>
    </nav>
  </header>
)

describe('Navigation Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders main navigation with all required links', () => {
    render(<MockHeader />)

    // Check for navigation landmark
    expect(screen.getByRole('navigation')).toBeInTheDocument()
    expect(screen.getByLabelText('Main navigation')).toBeInTheDocument()

    // Check for all main navigation links
    expect(screen.getByText('About NPI')).toBeInTheDocument()
    expect(screen.getByText('Strategic Pillars')).toBeInTheDocument()
    expect(screen.getByText('IKIA')).toBeInTheDocument()
    expect(screen.getByText('Projects')).toBeInTheDocument()
    expect(screen.getByText('Partnerships')).toBeInTheDocument()
    expect(screen.getByText('Success Stories')).toBeInTheDocument()
    expect(screen.getByText('Resources')).toBeInTheDocument()
    expect(screen.getByText('News')).toBeInTheDocument()
    expect(screen.getByText('Events')).toBeInTheDocument()
    expect(screen.getByText('Get Involved')).toBeInTheDocument()
    expect(screen.getByText('Contact')).toBeInTheDocument()
  })

  it('has proper logo with home link', () => {
    render(<MockHeader />)

    const logoLink = screen.getByLabelText('NPI Home')
    expect(logoLink).toBeInTheDocument()
    expect(logoLink).toHaveAttribute('href', '/')

    const logo = screen.getByAltText('NPI Logo')
    expect(logo).toBeInTheDocument()
  })

  it('navigation links have correct href attributes', () => {
    render(<MockHeader />)

    expect(screen.getByText('About NPI').closest('a')).toHaveAttribute('href', '/about')
    expect(screen.getByText('Strategic Pillars').closest('a')).toHaveAttribute(
      'href',
      '/strategic-pillars',
    )
    expect(screen.getByText('IKIA').closest('a')).toHaveAttribute('href', '/ikia')
    expect(screen.getByText('Projects').closest('a')).toHaveAttribute('href', '/projects')
    expect(screen.getByText('Partnerships').closest('a')).toHaveAttribute('href', '/partnerships')
    expect(screen.getByText('Success Stories').closest('a')).toHaveAttribute(
      'href',
      '/success-stories',
    )
    expect(screen.getByText('Resources').closest('a')).toHaveAttribute('href', '/resources')
    expect(screen.getByText('News').closest('a')).toHaveAttribute('href', '/news')
    expect(screen.getByText('Events').closest('a')).toHaveAttribute('href', '/events')
    expect(screen.getByText('Get Involved').closest('a')).toHaveAttribute('href', '/get-involved')
    expect(screen.getByText('Contact').closest('a')).toHaveAttribute('href', '/contact')
  })

  it('supports keyboard navigation', () => {
    render(<MockHeader />)

    const firstLink = screen.getByText('About NPI')
    const secondLink = screen.getByText('Strategic Pillars')

    // Test tab navigation
    firstLink.focus()
    expect(document.activeElement).toBe(firstLink)

    // Simulate tab key press
    fireEvent.keyDown(firstLink, { key: 'Tab' })
    // In a real implementation, focus would move to the next element
  })

  it('has proper ARIA attributes for accessibility', () => {
    render(<MockHeader />)

    const nav = screen.getByRole('navigation')
    expect(nav).toHaveAttribute('aria-label', 'Main navigation')

    const banner = screen.getByRole('banner')
    expect(banner).toBeInTheDocument()
  })
})

describe('Mobile Navigation', () => {
  const MockMobileHeader = () => (
    <header role="banner">
      <nav role="navigation" aria-label="Main navigation">
        <button
          aria-label="Toggle navigation menu"
          aria-expanded="false"
          className="mobile-menu-toggle"
        >
          Menu
        </button>
        <div className="mobile-menu hidden">
          <a href="/about">About NPI</a>
          <a href="/programs">Programs</a>
          <a href="/contact">Contact</a>
        </div>
      </nav>
    </header>
  )

  it('has mobile menu toggle button', () => {
    render(<MockMobileHeader />)

    const toggleButton = screen.getByLabelText('Toggle navigation menu')
    expect(toggleButton).toBeInTheDocument()
    expect(toggleButton).toHaveAttribute('aria-expanded', 'false')
  })

  it('mobile menu toggle works correctly', () => {
    render(<MockMobileHeader />)

    const toggleButton = screen.getByLabelText('Toggle navigation menu')

    // Initially closed
    expect(toggleButton).toHaveAttribute('aria-expanded', 'false')

    // Click to open (in real implementation, this would update aria-expanded)
    fireEvent.click(toggleButton)

    // Test that the button is interactive
    expect(toggleButton).toBeInTheDocument()
  })
})

describe('Navigation Accessibility', () => {
  it('meets WCAG guidelines for navigation', () => {
    render(<MockHeader />)

    // Check for proper landmark roles
    expect(screen.getByRole('banner')).toBeInTheDocument()
    expect(screen.getByRole('navigation')).toBeInTheDocument()

    // Check for proper labeling
    expect(screen.getByLabelText('Main navigation')).toBeInTheDocument()
    expect(screen.getByLabelText('NPI Home')).toBeInTheDocument()
  })

  it('has sufficient color contrast', () => {
    render(<MockHeader />)

    // This would typically be tested with automated accessibility tools
    // For now, we ensure the navigation renders correctly
    expect(screen.getByRole('navigation')).toBeInTheDocument()
  })

  it('supports screen readers', () => {
    render(<MockHeader />)

    // Check for proper semantic structure
    const nav = screen.getByRole('navigation')
    expect(nav).toHaveAttribute('aria-label')

    // Check that links are properly labeled
    const homeLink = screen.getByLabelText('NPI Home')
    expect(homeLink).toBeInTheDocument()
  })
})

describe('Navigation Performance', () => {
  it('renders quickly', () => {
    const startTime = performance.now()
    render(<MockHeader />)
    const endTime = performance.now()

    expect(endTime - startTime).toBeLessThan(50) // Should render in under 50ms
  })

  it('does not cause layout shifts', () => {
    const { rerender } = render(<MockHeader />)

    // Re-render should not cause issues
    rerender(<MockHeader />)

    expect(screen.getByRole('navigation')).toBeInTheDocument()
  })
})
