#!/usr/bin/env node

/**
 * Contact Form Diagnostic Script
 * 
 * This script diagnoses issues with the contact form submission
 */

import fetch from 'node-fetch'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

dotenv.config({ path: join(__dirname, '..', '.env.local') })

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

async function diagnoseContactForm() {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'
  
  log('🔍 Diagnosing Contact Form Issues...', colors.cyan)
  log(`Base URL: ${baseUrl}`, colors.blue)
  
  try {
    // Test 1: Check if server is running
    log('\n1. Testing server connectivity...', colors.cyan)
    try {
      const response = await fetch(baseUrl, { timeout: 5000 })
      log(`✅ Server is running (Status: ${response.status})`, colors.green)
    } catch (error) {
      log(`❌ Server is not accessible: ${error.message}`, colors.red)
      log('   💡 Make sure to run: npm run dev', colors.yellow)
      return
    }
    
    // Test 2: Check health endpoint
    log('\n2. Testing health endpoint...', colors.cyan)
    try {
      const healthResponse = await fetch(`${baseUrl}/api/health`)
      const healthData = await healthResponse.json()
      
      if (healthData.success) {
        log('✅ Health endpoint working', colors.green)
        log(`   Database connected: ${healthData.database.connected}`, colors.blue)
      } else {
        log('❌ Health endpoint failed', colors.red)
        log(`   Error: ${healthData.message}`, colors.red)
      }
    } catch (error) {
      log(`❌ Health endpoint error: ${error.message}`, colors.red)
    }
    
    // Test 3: Test contact form endpoint with valid data
    log('\n3. Testing contact form endpoint...', colors.cyan)
    const testData = {
      name: 'Test User',
      email: '<EMAIL>',
      subject: 'Test Subject',
      category: 'general',
      message: 'This is a test message to diagnose the contact form.',
      priority: 'medium'
    }
    
    try {
      const contactResponse = await fetch(`${baseUrl}/api/contact-submissions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData),
      })
      
      const contactResult = await contactResponse.json()
      
      if (contactResponse.ok) {
        log('✅ Contact form endpoint working', colors.green)
        log(`   Submission ID: ${contactResult.submissionId}`, colors.blue)
        log(`   Status: ${contactResult.status}`, colors.blue)
      } else {
        log('❌ Contact form endpoint failed', colors.red)
        log(`   Status: ${contactResponse.status}`, colors.red)
        log(`   Error: ${contactResult.error || contactResult.message}`, colors.red)
        
        // Additional debugging info
        if (contactResponse.status === 404) {
          log('   💡 The endpoint might not be properly configured', colors.yellow)
        } else if (contactResponse.status === 500) {
          log('   💡 This is likely a database or server configuration issue', colors.yellow)
        } else if (contactResponse.status === 400) {
          log('   💡 This might be a validation error', colors.yellow)
        }
      }
    } catch (error) {
      log(`❌ Contact form request failed: ${error.message}`, colors.red)
      
      if (error.message.includes('fetch')) {
        log('   💡 This might be a network connectivity issue', colors.yellow)
      }
    }
    
    // Test 4: Test with invalid data to check validation
    log('\n4. Testing validation with invalid data...', colors.cyan)
    const invalidData = {
      name: 'Test User',
      // Missing required fields
    }
    
    try {
      const validationResponse = await fetch(`${baseUrl}/api/contact-submissions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidData),
      })
      
      const validationResult = await validationResponse.json()
      
      if (validationResponse.status === 400) {
        log('✅ Validation working correctly', colors.green)
        log(`   Error message: ${validationResult.message}`, colors.blue)
      } else {
        log('⚠️ Validation might not be working as expected', colors.yellow)
        log(`   Status: ${validationResponse.status}`, colors.yellow)
      }
    } catch (error) {
      log(`❌ Validation test failed: ${error.message}`, colors.red)
    }
    
    // Test 5: Check environment variables
    log('\n5. Checking environment variables...', colors.cyan)
    const requiredVars = ['DATABASE_URI', 'PAYLOAD_SECRET', 'NEXT_PUBLIC_API_URL']
    
    for (const varName of requiredVars) {
      if (process.env[varName]) {
        log(`✅ ${varName} is set`, colors.green)
      } else {
        log(`❌ ${varName} is missing`, colors.red)
      }
    }
    
    // Summary and recommendations
    log('\n📋 Diagnosis Summary:', colors.cyan)
    log('If the contact form is still not working, check:', colors.blue)
    log('1. Browser console for JavaScript errors', colors.blue)
    log('2. Network tab in browser dev tools', colors.blue)
    log('3. Server logs when running npm run dev', colors.blue)
    log('4. Database connection in MongoDB Atlas', colors.blue)
    
  } catch (error) {
    log(`❌ Diagnosis failed: ${error.message}`, colors.red)
  }
}

// Run the diagnosis
diagnoseContactForm()
