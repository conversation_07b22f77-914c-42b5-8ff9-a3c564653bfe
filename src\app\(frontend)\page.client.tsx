'use client'

import React from 'react'
import { NPIHeroComponent } from '@/heros/NPIHero'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import { RenderHero } from '@/heros/RenderHero'
import type { RequiredDataFromCollectionSlug } from 'payload'

interface PageClientProps {
  data?: RequiredDataFromCollectionSlug<'pages'>
}

export default function PageClient({ data }: PageClientProps) {
  if (!data) return null

  const { hero, layout } = data

  return (
    <article className="min-h-screen">
      <RenderHero {...hero} />
      {layout && (
        <main className="relative">
          <RenderBlocks blocks={layout} />
        </main>
      )}
    </article>
  )
}
