'use client'

import * as React from 'react'
import { cn } from '@/utilities/ui'
import { motion } from 'framer-motion'

interface BentoGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  columns?: 2 | 3 | 4 | 6 | 12
  gap?: 'sm' | 'md' | 'lg' | 'xl'
  autoRows?: boolean
}

interface BentoCardProps
  extends Omit<
    React.HTMLAttributes<HTMLDivElement>,
    | 'onDrag'
    | 'onDragStart'
    | 'onDragEnd'
    | 'onAnimationStart'
    | 'onAnimationEnd'
    | 'onAnimationIteration'
  > {
  children: React.ReactNode
  colSpan?: 1 | 2 | 3 | 4 | 6 | 12
  rowSpan?: 1 | 2 | 3 | 4
  variant?: 'default' | 'featured' | 'accent' | 'minimal' | 'glass'
  gradient?: boolean
  hover?: boolean
  delay?: number
}

const BentoGrid = React.forwardRef<HTMLDivElement, BentoGridProps>(
  ({ className, children, columns = 12, gap = 'md', autoRows = true, ...props }, ref) => {
    const gapClasses = {
      sm: 'gap-3',
      md: 'gap-6',
      lg: 'gap-8',
      xl: 'gap-12',
    }

    const columnClasses = {
      2: 'grid-cols-1 sm:grid-cols-2',
      3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
      6: 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6',
      12: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-12',
    }

    return (
      <div
        ref={ref}
        className={cn(
          'grid w-full',
          columnClasses[columns],
          gapClasses[gap],
          autoRows && 'auto-rows-fr',
          className,
        )}
        {...props}
      >
        {children}
      </div>
    )
  },
)
BentoGrid.displayName = 'BentoGrid'

const BentoCard = React.forwardRef<HTMLDivElement, BentoCardProps>(
  (
    {
      className,
      children,
      colSpan = 1,
      rowSpan = 1,
      variant = 'default',
      gradient = false,
      hover = true,
      delay = 0,
      ...props
    },
    ref,
  ) => {
    const colSpanClasses = {
      1: 'col-span-1',
      2: 'col-span-1 sm:col-span-2',
      3: 'col-span-1 sm:col-span-2 lg:col-span-3',
      4: 'col-span-1 sm:col-span-2 lg:col-span-4',
      6: 'col-span-1 sm:col-span-2 md:col-span-3 lg:col-span-6',
      12: 'col-span-1 sm:col-span-2 md:col-span-4 lg:col-span-6 xl:col-span-12',
    }

    const rowSpanClasses = {
      1: 'row-span-1',
      2: 'row-span-2',
      3: 'row-span-3',
      4: 'row-span-4',
    }

    const variantClasses = {
      default: 'bg-card border border-border npi-shadow-soft',
      featured:
        'bg-gradient-to-br from-npi-burgundy-500 to-npi-burgundy-700 text-white border-0 npi-shadow-medium',
      accent:
        'bg-gradient-to-br from-npi-green-500 to-npi-green-700 text-white border-0 npi-shadow-medium',
      minimal: 'bg-transparent border-0',
      glass: 'bg-white/10 backdrop-blur-md border border-white/20 npi-shadow-soft',
    }

    const gradientOverlay = gradient && (
      <div className="absolute inset-0 bg-gradient-to-br from-npi-burgundy-500/10 via-transparent to-npi-green-500/10 rounded-lg pointer-events-none" />
    )

    return (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: '-100px' }}
        transition={{
          duration: 0.6,
          delay: delay * 0.1,
          ease: [0.21, 1.11, 0.81, 0.99],
        }}
        whileHover={
          hover
            ? {
                y: -4,
                transition: { duration: 0.2 },
              }
            : undefined
        }
        className={cn(
          'relative rounded-lg p-6 transition-all duration-300',
          colSpanClasses[colSpan],
          rowSpanClasses[rowSpan],
          variantClasses[variant],
          hover && 'cursor-pointer',
          className,
        )}
        {...props}
      >
        {gradientOverlay}
        <div className="relative z-10">{children}</div>
      </motion.div>
    )
  },
)
BentoCard.displayName = 'BentoCard'

const BentoCardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('flex flex-col space-y-1.5 mb-4', className)} {...props} />
  ),
)
BentoCardHeader.displayName = 'BentoCardHeader'

const BentoCardTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn('text-xl font-semibold leading-none tracking-tight font-npi', className)}
    {...props}
  />
))
BentoCardTitle.displayName = 'BentoCardTitle'

const BentoCardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn('text-sm text-muted-foreground font-npi', className)} {...props} />
))
BentoCardDescription.displayName = 'BentoCardDescription'

const BentoCardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => <div ref={ref} className={cn('', className)} {...props} />,
)
BentoCardContent.displayName = 'BentoCardContent'

const BentoCardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('flex items-center pt-4 mt-auto', className)} {...props} />
  ),
)
BentoCardFooter.displayName = 'BentoCardFooter'

export {
  BentoGrid,
  BentoCard,
  BentoCardHeader,
  BentoCardTitle,
  BentoCardDescription,
  BentoCardContent,
  BentoCardFooter,
}
