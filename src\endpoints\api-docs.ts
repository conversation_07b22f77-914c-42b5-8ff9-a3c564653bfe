import type { PayloadRequest } from 'payload'

export const apiDocsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'
  
  const documentation = {
    title: 'NPI CMS API Documentation',
    version: '1.0.0',
    description: 'Comprehensive API documentation for the Natural Products Industry Initiative CMS',
    baseUrl,
    
    collections: {
      projects: {
        description: 'Manage NPI projects and initiatives',
        endpoints: [
          {
            method: 'GET',
            path: '/api/projects',
            description: 'Get all projects with filtering and pagination',
            parameters: [
              { name: 'limit', type: 'number', description: 'Number of items per page (max 100)' },
              { name: 'page', type: 'number', description: 'Page number' },
              { name: 'sort', type: 'string', description: 'Sort field (e.g., -updatedAt)' },
              { name: 'search', type: 'string', description: 'Search term' },
              { name: 'category', type: 'string', description: 'Filter by category' },
              { name: 'status', type: 'string', description: 'Filter by status' },
            ],
            response: {
              success: true,
              data: '[]',
              totalItems: 'number',
              page: 'number',
              totalPages: 'number',
            }
          },
          {
            method: 'POST',
            path: '/api/projects',
            description: 'Create a new project',
            body: {
              title: 'string (required)',
              description: 'richText (required)',
              category: 'string (required)',
              status: 'string (required)',
              summary: 'string (required, max 300 chars)',
            }
          },
          {
            method: 'GET',
            path: '/api/projects/:id',
            description: 'Get a specific project by ID',
          },
          {
            method: 'PUT',
            path: '/api/projects/:id',
            description: 'Update a specific project',
          },
          {
            method: 'DELETE',
            path: '/api/projects/:id',
            description: 'Delete a specific project',
          },
        ]
      },
      
      'success-stories': {
        description: 'Manage success stories and case studies',
        endpoints: [
          {
            method: 'GET',
            path: '/api/success-stories',
            description: 'Get all success stories',
          },
          {
            method: 'POST',
            path: '/api/success-stories',
            description: 'Create a new success story',
            body: {
              title: 'string (required)',
              content: 'richText (required)',
              summary: 'string (required, max 300 chars)',
              image: 'upload (required)',
            }
          },
        ]
      },
      
      news: {
        description: 'Manage news articles and updates',
        endpoints: [
          {
            method: 'GET',
            path: '/api/news',
            description: 'Get all news articles',
          },
          {
            method: 'POST',
            path: '/api/news',
            description: 'Create a new news article',
            body: {
              title: 'string (required)',
              content: 'richText (required)',
              category: 'string (required)',
              summary: 'string (required, max 300 chars)',
            }
          },
        ]
      },
      
      resources: {
        description: 'Manage resources and publications',
        endpoints: [
          {
            method: 'GET',
            path: '/api/resources',
            description: 'Get all resources',
          },
          {
            method: 'POST',
            path: '/api/resources',
            description: 'Create a new resource',
            body: {
              title: 'string (required)',
              type: 'string (required)',
              description: 'richText (required)',
            }
          },
        ]
      },
      
      events: {
        description: 'Manage events and activities',
        endpoints: [
          {
            method: 'GET',
            path: '/api/events',
            description: 'Get all events',
          },
          {
            method: 'POST',
            path: '/api/events',
            description: 'Create a new event',
            body: {
              title: 'string (required)',
              description: 'richText (required)',
              date: 'date (required)',
              type: 'string (required)',
            }
          },
        ]
      },
      
      'contact-submissions': {
        description: 'Manage contact form submissions',
        endpoints: [
          {
            method: 'GET',
            path: '/api/contact-submissions',
            description: 'Get all contact submissions',
          },
          {
            method: 'POST',
            path: '/api/contact-submissions',
            description: 'Create a new contact submission',
            body: {
              name: 'string (required)',
              email: 'email (required)',
              subject: 'string (required)',
              message: 'textarea (required)',
              category: 'string (required)',
            }
          },
        ]
      },
    },
    
    authentication: {
      description: 'API authentication is handled through Payload CMS session management',
      loginEndpoint: '/admin/login',
      logoutEndpoint: '/admin/logout',
    },
    
    errorHandling: {
      description: 'All endpoints return consistent error responses',
      format: {
        success: false,
        error: 'Error type',
        message: 'Detailed error message',
      },
      statusCodes: {
        200: 'Success',
        201: 'Created',
        400: 'Bad Request',
        401: 'Unauthorized',
        403: 'Forbidden',
        404: 'Not Found',
        500: 'Internal Server Error',
      }
    },
    
    features: {
      autoGeneratedIds: 'All collections automatically generate unique IDs',
      databaseStorage: 'Images are stored directly in the database as base64',
      validation: 'Enhanced validation and error handling for all operations',
      search: 'Full-text search capabilities across collections',
      pagination: 'Consistent pagination with configurable limits',
      sorting: 'Flexible sorting options for all collections',
    }
  }

  // Return HTML documentation page
  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>NPI CMS API Documentation</title>
      <style>
        body { 
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          max-width: 1200px;
          margin: 0 auto;
          padding: 2rem;
          background: #f9fafb;
        }
        .container {
          background: white;
          padding: 2rem;
          border-radius: 0.5rem;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        h1 { color: #1f2937; border-bottom: 2px solid #3b82f6; padding-bottom: 0.5rem; }
        h2 { color: #374151; margin-top: 2rem; }
        h3 { color: #4b5563; }
        .endpoint {
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 0.375rem;
          padding: 1rem;
          margin: 1rem 0;
        }
        .method {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-weight: 600;
          font-size: 0.75rem;
          text-transform: uppercase;
          margin-right: 0.5rem;
        }
        .method-get { background: #10b981; color: white; }
        .method-post { background: #3b82f6; color: white; }
        .method-put { background: #f59e0b; color: white; }
        .method-delete { background: #ef4444; color: white; }
        .path { font-family: monospace; font-weight: 600; }
        pre { 
          background: #1f2937; 
          color: #f9fafb; 
          padding: 1rem; 
          border-radius: 0.375rem; 
          overflow-x: auto; 
        }
        .feature-list {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1rem;
          margin: 1rem 0;
        }
        .feature-card {
          background: #f0f9ff;
          border: 1px solid #0ea5e9;
          border-radius: 0.375rem;
          padding: 1rem;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>${documentation.title}</h1>
        <p><strong>Version:</strong> ${documentation.version}</p>
        <p><strong>Base URL:</strong> <code>${documentation.baseUrl}</code></p>
        <p>${documentation.description}</p>
        
        <h2>🚀 Key Features</h2>
        <div class="feature-list">
          ${Object.entries(documentation.features).map(([key, value]) => `
            <div class="feature-card">
              <h4>${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</h4>
              <p>${value}</p>
            </div>
          `).join('')}
        </div>
        
        <h2>📚 Collections</h2>
        ${Object.entries(documentation.collections).map(([collection, info]) => `
          <h3>${collection.replace('-', ' ').toUpperCase()}</h3>
          <p>${info.description}</p>
          ${info.endpoints.map(endpoint => `
            <div class="endpoint">
              <div>
                <span class="method method-${endpoint.method.toLowerCase()}">${endpoint.method}</span>
                <span class="path">${endpoint.path}</span>
              </div>
              <p>${endpoint.description}</p>
              ${endpoint.parameters ? `
                <h4>Parameters:</h4>
                <ul>
                  ${endpoint.parameters.map(param => `
                    <li><code>${param.name}</code> (${param.type}): ${param.description}</li>
                  `).join('')}
                </ul>
              ` : ''}
              ${endpoint.body ? `
                <h4>Request Body:</h4>
                <pre>${JSON.stringify(endpoint.body, null, 2)}</pre>
              ` : ''}
            </div>
          `).join('')}
        `).join('')}
        
        <h2>🔐 Authentication</h2>
        <p>${documentation.authentication.description}</p>
        <ul>
          <li><strong>Login:</strong> <code>${documentation.authentication.loginEndpoint}</code></li>
          <li><strong>Logout:</strong> <code>${documentation.authentication.logoutEndpoint}</code></li>
        </ul>
        
        <h2>⚠️ Error Handling</h2>
        <p>${documentation.errorHandling.description}</p>
        <pre>${JSON.stringify(documentation.errorHandling.format, null, 2)}</pre>
        
        <h3>Status Codes</h3>
        <ul>
          ${Object.entries(documentation.errorHandling.statusCodes).map(([code, desc]) => `
            <li><code>${code}</code>: ${desc}</li>
          `).join('')}
        </ul>
      </div>
    </body>
    </html>
  `

  res.setHeader('Content-Type', 'text/html')
  return res.send(html)
}
