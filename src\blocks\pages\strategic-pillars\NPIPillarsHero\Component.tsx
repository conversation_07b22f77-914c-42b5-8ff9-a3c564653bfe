import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Database, Lightbulb, Users, Shield, ArrowDown } from 'lucide-react'

interface NPIPillarsHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPIPillarsHeroBlock: React.FC<NPIPillarsHeroProps> = ({
  title = 'Strategic Pillars',
  subtitle = "Four interconnected pillars that form the foundation of NPI's comprehensive approach to transforming Kenya's natural heritage into sustainable economic opportunities.",
  backgroundImage = '/assets/hero image.jpg',
}) => {
  const pillars = [
    {
      icon: <Database className="w-6 h-6" />,
      title: 'Knowledge Documentation',
      description: 'Preserving traditional wisdom',
    },
    {
      icon: <Lightbulb className="w-6 h-6" />,
      title: 'Product Development',
      description: 'Innovation and commercialization',
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: 'Capacity Building',
      description: 'Empowering communities',
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: 'IP Protection',
      description: 'Safeguarding rights',
    },
  ]

  return (
    <section className="relative min-h-[95vh] max-h-[95vh] overflow-hidden -mt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Strategic Pillars background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Solid overlay - no gradients */}
      <div className="absolute inset-0 bg-[#725242]/60 z-10"></div>

      {/* Content */}
      <div className="relative z-20 h-full flex flex-col">
        {/* Top Left Section - Title */}
        <div className="flex-1 flex items-start justify-start pt-32 px-4 sm:px-6 lg:px-25">
          <div className="max-w-2xl">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 font-npi leading-tight">
              {title}
            </h1>
            <p className="text-lg sm:text-xl text-[#EFE3BA] font-npi leading-relaxed max-w-xl">
              {subtitle}
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
