import type { PayloadRequest } from 'payload'

interface TransformedProject {
  id: string
  title: string
  description: string
  summary: string
  image: string // Base64 encoded image data
  imageMetadata?: {
    filename?: string
    alt?: string
    width?: number
    height?: number
  }
  gallery?: Array<{
    image: string // Base64 encoded image data
    imageMetadata?: {
      filename?: string
      alt?: string
      width?: number
      height?: number
    }
    caption?: string
  }>
  category: string
  pillar: string
  status: string
  location: {
    counties?: TransformedCounty[]
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  timeline: {
    startDate: string
    endDate?: string
    duration?: string
    milestones?: Array<{
      title: string
      description?: string
      targetDate?: string
      completed: boolean
    }>
  }
  budget?: {
    totalBudget?: number
    currency: string
    fundingSources?: Array<{
      source: string
      amount?: number
      percentage?: number
    }>
  }
  impact?: {
    beneficiaries?: number
    communities?: number
    jobsCreated?: number
    metrics?: Array<{
      metric: string
      value: string
      unit?: string
    }>
  }
  team?: {
    projectManager?: string
    implementingPartners?: Array<{
      partner: any
      role?: string
    }>
    keyPersonnel?: Array<{
      name: string
      role: string
      organization?: string
    }>
  }
  featured: boolean
  published: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
}

interface TransformedCounty {
  id: string
  name: string
  code?: string
}

interface ProjectsResponse {
  projects: TransformedProject[]
  totalProjects: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility function to extract plain text from Lexical rich text
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
  }
}

const transformCounty = (county: any): TransformedCounty | undefined => {
  if (!county || typeof county === 'string') return undefined

  return {
    id: county.id,
    name: county.name,
    code: county.code,
  }
}

const transformProject = (project: any): TransformedProject => {
  return {
    id: project.id,
    title: project.title,
    description: extractTextFromLexical(project.description),
    summary: project.summary,
    image: project.image || '', // Base64 encoded image data
    imageMetadata: project.imageMetadata,
    gallery: Array.isArray(project.gallery)
      ? project.gallery.map((item: any) => ({
          image: item.image || '', // Base64 encoded image data
          imageMetadata: item.imageMetadata,
          caption: item.caption,
        })).filter((item: any) => item.image)
      : [],
    category: project.category,
    pillar: project.pillar,
    status: project.status,
    location: {
      counties: Array.isArray(project.location?.counties) 
        ? project.location.counties.map(transformCounty).filter(Boolean)
        : [],
      specificLocation: project.location?.specificLocation,
      coordinates: project.location?.coordinates,
    },
    timeline: {
      startDate: project.timeline?.startDate,
      endDate: project.timeline?.endDate,
      duration: project.timeline?.duration,
      milestones: Array.isArray(project.timeline?.milestones) 
        ? project.timeline.milestones.map((milestone: any) => ({
            title: milestone.title,
            description: milestone.description,
            targetDate: milestone.targetDate,
            completed: milestone.completed || false,
          }))
        : [],
    },
    budget: project.budget ? {
      totalBudget: project.budget.totalBudget,
      currency: project.budget.currency || 'KES',
      fundingSources: Array.isArray(project.budget.fundingSources) 
        ? project.budget.fundingSources.map((source: any) => ({
            source: source.source,
            amount: source.amount,
            percentage: source.percentage,
          }))
        : [],
    } : undefined,
    impact: project.impact ? {
      beneficiaries: project.impact.beneficiaries,
      communities: project.impact.communities,
      jobsCreated: project.impact.jobsCreated,
      metrics: Array.isArray(project.impact.metrics) 
        ? project.impact.metrics.map((metric: any) => ({
            metric: metric.metric,
            value: metric.value,
            unit: metric.unit,
          }))
        : [],
    } : undefined,
    team: project.team ? {
      projectManager: project.team.projectManager,
      implementingPartners: Array.isArray(project.team.implementingPartners) 
        ? project.team.implementingPartners.map((partner: any) => ({
            partner: partner.partner,
            role: partner.role,
          }))
        : [],
      keyPersonnel: Array.isArray(project.team.keyPersonnel) 
        ? project.team.keyPersonnel.map((person: any) => ({
            name: person.name,
            role: person.role,
            organization: person.organization,
          }))
        : [],
    } : undefined,
    featured: project.featured || false,
    published: project.published !== false,
    tags: Array.isArray(project.tags) 
      ? project.tags.map((tag: any) => tag.tag).filter(Boolean)
      : [],
    slug: project.slug,
    createdAt: project.createdAt,
    updatedAt: project.updatedAt,
  }
}

// Main Projects Handler
export const projectsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      category,
      pillar,
      status,
      featured,
      county,
      limit = '20',
      page = '1',
      sort = '-updatedAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      published: { equals: true },
    }

    if (category) where.category = { equals: category }
    if (pillar) where.pillar = { equals: pillar }
    if (status) where.status = { equals: status }
    if (featured === 'true') where.featured = { equals: true }
    if (county) {
      where['location.counties'] = { in: [county] }
    }
    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } },
        { 'tags.tag': { contains: search } },
      ]
    }

    // Fetch projects with populated relationships
    const projectsResult = await payload.find({
      collection: 'projects',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate counties, partners, etc.
    })

    // Transform projects
    const transformedProjects: TransformedProject[] = projectsResult.docs.map(transformProject)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(projectsResult.totalDocs / currentLimit)

    const response: ProjectsResponse = {
      projects: transformedProjects,
      totalProjects: projectsResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in projects endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single project by ID or slug
export const projectByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let project
    try {
      project = await payload.findByID({
        collection: 'projects',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'projects',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      project = result.docs[0]
    }

    if (!project) {
      return res.status(404).json({
        error: 'Project not found',
        message: `No project found with ID or slug: ${id}`,
      })
    }

    // Check if published (unless user is authenticated)
    if (!project.published && !req.user) {
      return res.status(404).json({
        error: 'Project not found',
        message: 'Project is not published',
      })
    }

    const transformedProject = transformProject(project)

    res.status(200).json({
      project: transformedProject,
    })
  } catch (error) {
    console.error('Error in project by ID endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
