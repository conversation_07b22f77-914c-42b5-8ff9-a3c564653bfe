import type { PayloadRequest } from 'payload'

interface TransformedNews {
  id: string
  title: string
  subtitle?: string
  summary: string
  content: string
  featuredImage?: TransformedMedia
  gallery?: Array<{
    image: TransformedMedia
    caption?: string
    credit?: string
  }>
  category: string
  status: string
  publishDate: string
  author: {
    name: string
    role?: string
    organization?: string
    bio?: string
    photo?: TransformedMedia
    email?: string
    socialLinks?: Array<{
      platform: string
      url: string
    }>
  }
  location?: {
    county?: TransformedCounty
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  relatedContent?: {
    projects?: any[]
    successStories?: any[]
    events?: any[]
    resources?: any[]
  }
  seo?: {
    metaTitle?: string
    metaDescription?: string
    keywords?: string[]
    ogImage?: TransformedMedia
  }
  engagement: {
    allowComments: boolean
    socialSharing: boolean
    newsletter: boolean
  }
  analytics: {
    viewCount: number
    shareCount: number
    lastViewed?: string
  }
  featured: boolean
  urgent: boolean
  tags?: string[]
  slug: string
  createdAt: string
  updatedAt: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
}

interface TransformedCounty {
  id: string
  name: string
  code?: string
}

interface NewsResponse {
  articles: TransformedNews[]
  totalArticles: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility functions
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
  }
}

const transformCounty = (county: any): TransformedCounty | undefined => {
  if (!county || typeof county === 'string') return undefined

  return {
    id: county.id,
    name: county.name,
    code: county.code,
  }
}

const transformNews = (article: any): TransformedNews => {
  return {
    id: article.id,
    title: article.title,
    subtitle: article.subtitle,
    summary: article.summary,
    content: extractTextFromLexical(article.content),
    featuredImage: transformMedia(article.featuredImage),
    gallery: Array.isArray(article.gallery) 
      ? article.gallery.map((item: any) => ({
          image: transformMedia(item.image),
          caption: item.caption,
          credit: item.credit,
        })).filter((item: any) => item.image)
      : [],
    category: article.category,
    status: article.status,
    publishDate: article.publishDate,
    author: {
      name: article.author?.name || 'NPI Team',
      role: article.author?.role,
      organization: article.author?.organization,
      bio: article.author?.bio,
      photo: transformMedia(article.author?.photo),
      email: article.author?.email,
      socialLinks: Array.isArray(article.author?.socialLinks) 
        ? article.author.socialLinks.map((link: any) => ({
            platform: link.platform,
            url: link.url,
          }))
        : [],
    },
    location: article.location ? {
      county: transformCounty(article.location.county),
      specificLocation: article.location.specificLocation,
      coordinates: article.location.coordinates,
    } : undefined,
    relatedContent: article.relatedContent ? {
      projects: article.relatedContent.projects || [],
      successStories: article.relatedContent.successStories || [],
      events: article.relatedContent.events || [],
      resources: article.relatedContent.resources || [],
    } : undefined,
    seo: article.seo ? {
      metaTitle: article.seo.metaTitle,
      metaDescription: article.seo.metaDescription,
      keywords: Array.isArray(article.seo.keywords) 
        ? article.seo.keywords.map((keyword: any) => keyword.keyword).filter(Boolean)
        : [],
      ogImage: transformMedia(article.seo.ogImage),
    } : undefined,
    engagement: {
      allowComments: article.engagement?.allowComments !== false,
      socialSharing: article.engagement?.socialSharing !== false,
      newsletter: article.engagement?.newsletter || false,
    },
    analytics: {
      viewCount: article.analytics?.viewCount || 0,
      shareCount: article.analytics?.shareCount || 0,
      lastViewed: article.analytics?.lastViewed,
    },
    featured: article.featured || false,
    urgent: article.urgent || false,
    tags: Array.isArray(article.tags) 
      ? article.tags.map((tag: any) => tag.tag).filter(Boolean)
      : [],
    slug: article.slug,
    createdAt: article.createdAt,
    updatedAt: article.updatedAt,
  }
}

// Main News Handler
export const newsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      category,
      status,
      featured,
      urgent,
      author,
      county,
      limit = '20',
      page = '1',
      sort = '-publishDate',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      status: { equals: 'published' },
      publishDate: { less_than_equal: new Date().toISOString() },
    }

    if (category) where.category = { equals: category }
    if (status && req.user) where.status = { equals: status } // Only authenticated users can filter by status
    if (featured === 'true') where.featured = { equals: true }
    if (urgent === 'true') where.urgent = { equals: true }
    if (author) where['author.name'] = { contains: author }
    if (county) where['location.county'] = { equals: county }
    if (search) {
      where.or = [
        { title: { contains: search } },
        { summary: { contains: search } },
        { 'tags.tag': { contains: search } },
        { 'author.name': { contains: search } },
      ]
    }

    // Fetch news articles with populated relationships
    const newsResult = await payload.find({
      collection: 'news',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate counties, related content, etc.
    })

    // Transform news articles
    const transformedNews: TransformedNews[] = newsResult.docs.map(transformNews)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(newsResult.totalDocs / currentLimit)

    const response: NewsResponse = {
      articles: transformedNews,
      totalArticles: newsResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in news endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single news article by ID or slug
export const newsByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let article
    try {
      article = await payload.findByID({
        collection: 'news',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'news',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      article = result.docs[0]
    }

    if (!article) {
      return res.status(404).json({
        error: 'Article not found',
        message: `No article found with ID or slug: ${id}`,
      })
    }

    // Check if published and not in future (unless user is authenticated)
    const now = new Date()
    const publishDate = new Date(article.publishDate)
    
    if (!req.user && (article.status !== 'published' || publishDate > now)) {
      return res.status(404).json({
        error: 'Article not found',
        message: 'Article is not published or not yet available',
      })
    }

    const transformedArticle = transformNews(article)

    // Increment view count (in a real implementation, you might want to do this asynchronously)
    try {
      await payload.update({
        collection: 'news',
        id: article.id,
        data: {
          analytics: {
            ...article.analytics,
            viewCount: (article.analytics?.viewCount || 0) + 1,
            lastViewed: new Date().toISOString(),
          },
        },
      })
    } catch (updateError) {
      console.warn('Failed to update view count:', updateError)
    }

    res.status(200).json({
      article: transformedArticle,
    })
  } catch (error) {
    console.error('Error in news by ID endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
