#!/usr/bin/env node

/**
 * MongoDB Connection Test Script
 * 
 * This script tests the MongoDB connection and provides detailed information
 * about the database setup for the NPI website.
 */

import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

dotenv.config({ path: join(__dirname, '..', '.env.local') });

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

async function testMongoDBConnection() {
  const uri = process.env.DATABASE_URI;
  
  if (!uri) {
    logError('DATABASE_URI environment variable is not set');
    logInfo('Please check your .env.local file and ensure DATABASE_URI is configured');
    process.exit(1);
  }

  logInfo('Testing MongoDB connection...');
  logInfo(`Connection URI: ${uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);

  const client = new MongoClient(uri);
  
  try {
    // Test connection
    await client.connect();
    logSuccess('Connected to MongoDB successfully!');
    
    // Get database info
    const db = client.db();
    const dbName = db.databaseName;
    logInfo(`Database name: ${dbName}`);
    
    // List collections
    const collections = await db.listCollections().toArray();
    if (collections.length > 0) {
      logSuccess(`Found ${collections.length} collections:`);
      collections.forEach(collection => {
        log(`  📁 ${collection.name}`, colors.cyan);
      });
    } else {
      logWarning('No collections found (this is normal for a new database)');
      logInfo('Collections will be created automatically when you start the application');
    }
    
    // Test write operation
    const testCollection = db.collection('connection_test');
    const testDoc = {
      timestamp: new Date(),
      test: 'MongoDB connection test',
      success: true
    };
    
    await testCollection.insertOne(testDoc);
    logSuccess('Write operation successful');
    
    // Test read operation
    const retrievedDoc = await testCollection.findOne({ test: 'MongoDB connection test' });
    if (retrievedDoc) {
      logSuccess('Read operation successful');
    }
    
    // Clean up test document
    await testCollection.deleteOne({ _id: testDoc._id });
    logSuccess('Cleanup successful');
    
    // Get server info
    const admin = db.admin();
    const serverStatus = await admin.serverStatus();
    logInfo(`MongoDB version: ${serverStatus.version}`);
    logInfo(`Server uptime: ${Math.floor(serverStatus.uptime / 3600)} hours`);
    
    // Connection info
    const connectionInfo = client.topology?.s?.description;
    if (connectionInfo) {
      logInfo(`Connection type: ${connectionInfo.type}`);
      if (connectionInfo.servers) {
        logInfo(`Connected servers: ${connectionInfo.servers.size}`);
      }
    }
    
    logSuccess('All tests passed! MongoDB is ready for use.');
    
  } catch (error) {
    logError('Connection test failed:');
    console.error(error);
    
    // Provide helpful error messages
    if (error.message.includes('authentication failed')) {
      logWarning('Authentication failed. Please check:');
      log('  • Username and password are correct', colors.yellow);
      log('  • Database user has proper permissions', colors.yellow);
      log('  • Special characters in password are URL-encoded', colors.yellow);
    } else if (error.message.includes('connection timed out')) {
      logWarning('Connection timed out. Please check:');
      log('  • Network connectivity', colors.yellow);
      log('  • IP address is whitelisted in MongoDB Atlas', colors.yellow);
      log('  • Firewall settings allow MongoDB connections', colors.yellow);
    } else if (error.message.includes('ENOTFOUND')) {
      logWarning('Host not found. Please check:');
      log('  • Connection string is correct', colors.yellow);
      log('  • MongoDB server is running (for local connections)', colors.yellow);
      log('  • Cluster URL is correct (for Atlas connections)', colors.yellow);
    }
    
    process.exit(1);
  } finally {
    await client.close();
    logInfo('Connection closed');
  }
}

async function checkEnvironment() {
  log('\n' + '='.repeat(50), colors.bright);
  log('MongoDB Connection Test for NPI Website', colors.bright);
  log('='.repeat(50), colors.bright);
  
  // Check Node.js version
  const nodeVersion = process.version;
  logInfo(`Node.js version: ${nodeVersion}`);
  
  // Check if required packages are installed
  try {
    await import('mongodb');
    logSuccess('MongoDB driver is installed');
  } catch (error) {
    logError('MongoDB driver is not installed');
    logInfo('Run: pnpm add mongodb');
    process.exit(1);
  }

  // Check environment file
  const fs = await import('fs');
  const envFiles = ['.env.local', '.env', '.env.development'];
  let envFileFound = false;
  
  for (const envFile of envFiles) {
    if (fs.default.existsSync(envFile)) {
      logSuccess(`Environment file found: ${envFile}`);
      envFileFound = true;
      break;
    }
  }
  
  if (!envFileFound) {
    logWarning('No environment file found');
    logInfo('Create a .env.local file with your DATABASE_URI');
  }
  
  log('\n' + '-'.repeat(50), colors.bright);
}

async function main() {
  try {
    await checkEnvironment();
    await testMongoDBConnection();
    
    log('\n' + '='.repeat(50), colors.bright);
    log('Next Steps:', colors.bright);
    log('='.repeat(50), colors.bright);
    log('1. Start your development server: pnpm dev', colors.cyan);
    log('2. Visit http://localhost:3000/admin to access PayloadCMS', colors.cyan);
    log('3. Create your first admin user', colors.cyan);
    log('4. Start building your content!', colors.cyan);
    log('');
    
  } catch (error) {
    logError('Test failed with unexpected error:');
    console.error(error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logError('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logError('Uncaught Exception:', error);
  process.exit(1);
});

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { testMongoDBConnection };
