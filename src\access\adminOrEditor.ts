import type { AccessArgs } from 'payload'
import type { User } from '@/payload-types'

type AdminOrEditor = (args: AccessArgs<User>) => boolean

export const adminOrEditor: AdminOrEditor = ({ req: { user } }) => {
  // Check if user exists and has admin, editor, or content-manager role
  return Boolean(
    user && 
    (user.role === 'admin' || 
     user.role === 'super-admin' || 
     user.role === 'editor' || 
     user.role === 'content-manager')
  )
}
