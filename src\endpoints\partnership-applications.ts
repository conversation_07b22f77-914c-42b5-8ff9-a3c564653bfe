import type { PayloadRequest } from 'payload'

// Get all partnership applications (Admin only)
export const partnershipApplicationsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      status,
      priority,
      organizationType,
      partnershipModel,
      limit = '20',
      page = '1',
      sort = '-createdAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {}

    if (status) where.status = { equals: status }
    if (priority) where.priority = { equals: priority }
    if (organizationType) where.organizationType = { equals: organizationType }
    if (partnershipModel) where.partnershipModel = { equals: partnershipModel }
    if (search) {
      where.or = [
        { organizationName: { contains: search } },
        { contactName: { contains: search } },
        { email: { contains: search } },
        { projectInterest: { contains: search } },
      ]
    }

    // Fetch applications
    const applicationsResult = await payload.find({
      collection: 'partnership-applications',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
    })

    const currentPage = parsedPage
    const totalPages = applicationsResult.totalPages || 1
    const totalDocs = applicationsResult.totalDocs || 0

    // Return response
    res.status(200).json({
      success: true,
      data: applicationsResult.docs,
      pagination: {
        page: currentPage,
        limit: parsedLimit,
        totalPages,
        totalDocs,
        hasNextPage: currentPage < totalPages,
        hasPrevPage: currentPage > 1,
        nextPage: currentPage < totalPages ? currentPage + 1 : null,
        prevPage: currentPage > 1 ? currentPage - 1 : null,
      },
    })
  } catch (error) {
    console.error('Partnership Applications API error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Create new partnership application (Public endpoint)
export const createPartnershipApplicationHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Extract data from request body
    const {
      organizationName,
      organizationType,
      website,
      establishedYear,
      contactName,
      contactTitle,
      email,
      phone,
      partnershipModel,
      investmentCapacity,
      projectInterest,
      timeline,
      experience,
      objectives,
      additionalInfo,
    } = req.body

    // Basic validation
    if (!organizationName || !organizationType || !contactName || !email || !partnershipModel) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Organization name, type, contact name, email, and partnership model are required',
      })
    }

    // Capture metadata
    const metadata = {
      source: 'website-form',
      ipAddress: req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress,
      userAgent: req.headers['user-agent'],
      referrer: req.headers.referer || req.headers.referrer,
    }

    // Create the application
    const application = await payload.create({
      collection: 'partnership-applications',
      data: {
        organizationName,
        organizationType,
        website,
        establishedYear,
        contactName,
        contactTitle,
        email,
        phone,
        partnershipModel,
        investmentCapacity,
        projectInterest,
        timeline,
        experience,
        objectives,
        additionalInfo,
        status: 'new',
        priority: 'medium',
        metadata,
        archived: false,
      },
    })

    // Return success response (without sensitive data)
    res.status(201).json({
      success: true,
      message: 'Partnership application submitted successfully',
      applicationId: application.id,
      status: 'new',
    })
  } catch (error) {
    console.error('Create Partnership Application error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single partnership application by ID (Admin only)
export const partnershipApplicationByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    const application = await payload.findByID({
      collection: 'partnership-applications',
      id,
    })

    if (!application) {
      return res.status(404).json({
        error: 'Partnership application not found',
        message: `No partnership application found with ID: ${id}`,
      })
    }

    res.status(200).json({
      success: true,
      data: application,
    })
  } catch (error) {
    console.error('Partnership Application by ID error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Update partnership application (Admin only)
export const updatePartnershipApplicationHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    const updatedApplication = await payload.update({
      collection: 'partnership-applications',
      id,
      data: req.body,
    })

    res.status(200).json({
      success: true,
      message: 'Partnership application updated successfully',
      data: updatedApplication,
    })
  } catch (error) {
    console.error('Update Partnership Application error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Delete partnership application (Admin only)
export const deletePartnershipApplicationHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    await payload.delete({
      collection: 'partnership-applications',
      id,
    })

    res.status(200).json({
      success: true,
      message: 'Partnership application deleted successfully',
    })
  } catch (error) {
    console.error('Delete Partnership Application error:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
