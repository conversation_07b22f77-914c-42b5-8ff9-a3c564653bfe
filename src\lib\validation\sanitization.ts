import DOMPurify from 'isomorphic-dompurify'

// HTML sanitization options
const ALLOWED_TAGS = [
  'p', 'br', 'strong', 'em', 'u', 's', 'sub', 'sup',
  'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
  'ul', 'ol', 'li',
  'blockquote', 'code', 'pre',
  'a', 'img',
  'table', 'thead', 'tbody', 'tr', 'th', 'td',
  'div', 'span',
]

const ALLOWED_ATTRIBUTES = {
  'a': ['href', 'title', 'target', 'rel'],
  'img': ['src', 'alt', 'title', 'width', 'height'],
  'blockquote': ['cite'],
  'code': ['class'],
  'pre': ['class'],
  'div': ['class'],
  'span': ['class'],
  'table': ['class'],
  'thead': ['class'],
  'tbody': ['class'],
  'tr': ['class'],
  'th': ['class', 'scope'],
  'td': ['class'],
}

// Sanitize HTML content
export function sanitizeHtml(html: string, options: {
  allowedTags?: string[]
  allowedAttributes?: Record<string, string[]>
  stripTags?: boolean
} = {}): string {
  if (!html || typeof html !== 'string') {
    return ''
  }

  const {
    allowedTags = ALLOWED_TAGS,
    allowedAttributes = ALLOWED_ATTRIBUTES,
    stripTags = false,
  } = options

  if (stripTags) {
    return DOMPurify.sanitize(html, { ALLOWED_TAGS: [] })
  }

  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: allowedTags,
    ALLOWED_ATTR: Object.values(allowedAttributes).flat(),
    ALLOWED_ATTR_BY_TAG: allowedAttributes,
    ALLOW_DATA_ATTR: false,
    ALLOW_UNKNOWN_PROTOCOLS: false,
    SANITIZE_DOM: true,
    KEEP_CONTENT: true,
  })
}

// Sanitize plain text
export function sanitizeText(text: string, options: {
  maxLength?: number
  allowNewlines?: boolean
  allowSpecialChars?: boolean
} = {}): string {
  if (!text || typeof text !== 'string') {
    return ''
  }

  const {
    maxLength,
    allowNewlines = true,
    allowSpecialChars = true,
  } = options

  let sanitized = text.trim()

  // Remove null bytes and control characters
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')

  // Handle newlines
  if (!allowNewlines) {
    sanitized = sanitized.replace(/[\r\n]/g, ' ')
  }

  // Handle special characters
  if (!allowSpecialChars) {
    sanitized = sanitized.replace(/[^\w\s\-.,!?()]/g, '')
  }

  // Normalize whitespace
  sanitized = sanitized.replace(/\s+/g, ' ')

  // Truncate if needed
  if (maxLength && sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength).trim()
  }

  return sanitized
}

// Sanitize email
export function sanitizeEmail(email: string): string {
  if (!email || typeof email !== 'string') {
    return ''
  }

  return email
    .toLowerCase()
    .trim()
    .replace(/[^\w@.-]/g, '')
}

// Sanitize phone number
export function sanitizePhone(phone: string): string {
  if (!phone || typeof phone !== 'string') {
    return ''
  }

  return phone
    .trim()
    .replace(/[^\d+\-\s()]/g, '')
}

// Sanitize URL
export function sanitizeUrl(url: string, options: {
  allowedProtocols?: string[]
  requireProtocol?: boolean
} = {}): string {
  if (!url || typeof url !== 'string') {
    return ''
  }

  const {
    allowedProtocols = ['http', 'https'],
    requireProtocol = true,
  } = options

  let sanitized = url.trim()

  // Remove dangerous protocols
  sanitized = sanitized.replace(/^(javascript|data|vbscript|file):/i, '')

  // Add protocol if missing and required
  if (requireProtocol && !sanitized.match(/^https?:\/\//i)) {
    sanitized = `https://${sanitized}`
  }

  try {
    const urlObj = new URL(sanitized)
    
    // Check if protocol is allowed
    const protocol = urlObj.protocol.slice(0, -1) // Remove trailing colon
    if (!allowedProtocols.includes(protocol)) {
      return ''
    }

    return urlObj.toString()
  } catch {
    return ''
  }
}

// Sanitize slug
export function sanitizeSlug(slug: string): string {
  if (!slug || typeof slug !== 'string') {
    return ''
  }

  return slug
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

// Sanitize filename
export function sanitizeFilename(filename: string): string {
  if (!filename || typeof filename !== 'string') {
    return ''
  }

  return filename
    .trim()
    .replace(/[<>:"/\\|?*\x00-\x1f]/g, '') // Remove invalid filename characters
    .replace(/^\.+/, '') // Remove leading dots
    .substring(0, 255) // Limit length
}

// Sanitize rich text content (Lexical format)
export function sanitizeRichText(richText: any): any {
  if (!richText || typeof richText !== 'object') {
    return null
  }

  // Deep clone to avoid mutating original
  const sanitized = JSON.parse(JSON.stringify(richText))

  function sanitizeNode(node: any): any {
    if (!node || typeof node !== 'object') {
      return node
    }

    // Sanitize text nodes
    if (node.type === 'text' && typeof node.text === 'string') {
      node.text = sanitizeText(node.text, { allowNewlines: false })
    }

    // Sanitize link nodes
    if (node.type === 'link' && typeof node.url === 'string') {
      node.url = sanitizeUrl(node.url)
      if (!node.url) {
        // Convert to text node if URL is invalid
        return {
          type: 'text',
          text: node.children?.map((child: any) => child.text || '').join('') || '',
        }
      }
    }

    // Recursively sanitize children
    if (Array.isArray(node.children)) {
      node.children = node.children
        .map(sanitizeNode)
        .filter(Boolean) // Remove null/undefined nodes
    }

    return node
  }

  return sanitizeNode(sanitized)
}

// Sanitize object recursively
export function sanitizeObject(obj: any, options: {
  maxDepth?: number
  currentDepth?: number
  sanitizers?: Record<string, (value: any) => any>
} = {}): any {
  const {
    maxDepth = 10,
    currentDepth = 0,
    sanitizers = {},
  } = options

  if (currentDepth >= maxDepth) {
    return null
  }

  if (obj === null || obj === undefined) {
    return obj
  }

  if (typeof obj === 'string') {
    return sanitizeText(obj)
  }

  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj
      .map(item => sanitizeObject(item, { ...options, currentDepth: currentDepth + 1 }))
      .filter(item => item !== null && item !== undefined)
  }

  if (typeof obj === 'object') {
    const sanitized: Record<string, any> = {}

    for (const [key, value] of Object.entries(obj)) {
      // Skip prototype properties
      if (!obj.hasOwnProperty(key)) {
        continue
      }

      // Apply custom sanitizer if available
      if (sanitizers[key]) {
        sanitized[key] = sanitizers[key](value)
        continue
      }

      // Apply field-specific sanitization
      if (key === 'email') {
        sanitized[key] = sanitizeEmail(value)
      } else if (key === 'phone') {
        sanitized[key] = sanitizePhone(value)
      } else if (key.includes('url') || key.includes('link')) {
        sanitized[key] = sanitizeUrl(value)
      } else if (key === 'slug') {
        sanitized[key] = sanitizeSlug(value)
      } else if (key.includes('html') || key.includes('content')) {
        sanitized[key] = typeof value === 'string' ? sanitizeHtml(value) : value
      } else {
        sanitized[key] = sanitizeObject(value, { ...options, currentDepth: currentDepth + 1 })
      }
    }

    return sanitized
  }

  return obj
}

// Validate and sanitize file upload
export function sanitizeFileUpload(file: {
  filename?: string
  mimetype?: string
  size?: number
}, options: {
  allowedTypes?: string[]
  maxSize?: number
  sanitizeFilename?: boolean
} = {}): {
  filename?: string
  mimetype?: string
  size?: number
  isValid: boolean
  errors: string[]
} {
  const {
    allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'text/csv',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
    maxSize = 10 * 1024 * 1024, // 10MB
    sanitizeFilename: shouldSanitizeFilename = true,
  } = options

  const errors: string[] = []
  let isValid = true

  // Validate file object
  if (!file || typeof file !== 'object') {
    errors.push('Invalid file object')
    isValid = false
    return { isValid, errors }
  }

  // Validate filename
  if (!file.filename || typeof file.filename !== 'string') {
    errors.push('Filename is required')
    isValid = false
  } else if (shouldSanitizeFilename) {
    file.filename = sanitizeFilename(file.filename)
    if (!file.filename) {
      errors.push('Invalid filename')
      isValid = false
    }
  }

  // Validate mimetype
  if (!file.mimetype || typeof file.mimetype !== 'string') {
    errors.push('File type is required')
    isValid = false
  } else if (!allowedTypes.includes(file.mimetype)) {
    errors.push(`File type ${file.mimetype} is not allowed`)
    isValid = false
  }

  // Validate file size
  if (typeof file.size !== 'number' || file.size <= 0) {
    errors.push('Invalid file size')
    isValid = false
  } else if (file.size > maxSize) {
    errors.push(`File size exceeds maximum allowed size of ${maxSize} bytes`)
    isValid = false
  }

  return {
    filename: file.filename,
    mimetype: file.mimetype,
    size: file.size,
    isValid,
    errors,
  }
}

// Content Security Policy helpers
export function generateCSPNonce(): string {
  const crypto = require('crypto')
  return crypto.randomBytes(16).toString('base64')
}

export function createCSPHeader(nonce?: string): string {
  const directives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'" + (nonce ? ` 'nonce-${nonce}'` : ''),
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self'",
    "media-src 'self'",
    "object-src 'none'",
    "frame-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ]

  return directives.join('; ')
}

// Export all sanitization functions
export const sanitizers = {
  html: sanitizeHtml,
  text: sanitizeText,
  email: sanitizeEmail,
  phone: sanitizePhone,
  url: sanitizeUrl,
  slug: sanitizeSlug,
  filename: sanitizeFilename,
  richText: sanitizeRichText,
  object: sanitizeObject,
  fileUpload: sanitizeFileUpload,
}
