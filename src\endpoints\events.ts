import type { PayloadRequest } from 'payload'

interface TransformedEvent {
  id: string
  title: string
  description: string
  type: string
  date: string
  startTime?: string
  endTime?: string
  day: number
  location?: string
  speakers: TransformedSpeaker[]
  downloads?: TransformedMedia
  formattedDate: string
  formattedTime?: string
  duration?: string
  isUpcoming: boolean
  dayLabel: string
}

interface TransformedSpeaker {
  id: string
  name: string
  title?: string
  company?: string
  bio?: string
  photo?: TransformedMedia
  slug?: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
}

interface EventsResponse {
  events: TransformedEvent[]
  totalEvents: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility function to extract plain text from Lexical rich text
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
  }
}

const transformSpeaker = (speaker: any): TransformedSpeaker => {
  if (typeof speaker === 'string') {
    return {
      id: speaker,
      name: 'Unknown Speaker',
    }
  }

  return {
    id: speaker.id,
    name: speaker.name,
    title: speaker.title,
    company: speaker.company,
    bio: extractTextFromLexical(speaker.bio),
    photo: transformMedia(speaker.photo),
    slug: speaker.slug,
  }
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

const formatTime = (startTime?: string, endTime?: string): string | undefined => {
  if (!startTime) return undefined
  
  if (endTime) {
    return `${startTime} - ${endTime}`
  }
  
  return startTime
}

const calculateDuration = (startTime?: string, endTime?: string): string | undefined => {
  if (!startTime || !endTime) return undefined
  
  // Simple duration calculation assuming format like "09:00" or "9:00 AM"
  // This is a basic implementation - you might want to enhance it based on your time format
  try {
    const start = new Date(`2000-01-01 ${startTime}`)
    const end = new Date(`2000-01-01 ${endTime}`)
    const diffMs = end.getTime() - start.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    
    if (diffHours > 0) {
      return diffMinutes > 0 ? `${diffHours}h ${diffMinutes}m` : `${diffHours}h`
    }
    return `${diffMinutes}m`
  } catch {
    return undefined
  }
}

const getDayLabel = (day: number): string => {
  const dayLabels: Record<number, string> = {
    1: 'Day 1',
    2: 'Day 2',
    3: 'Day 3',
    4: 'Day 4',
    5: 'Day 5',
  }
  return dayLabels[day] || `Day ${day}`
}

const isEventUpcoming = (dateString: string, startTime?: string): boolean => {
  const now = new Date()
  const eventDate = new Date(dateString)
  
  if (startTime) {
    // If we have a start time, create a more precise datetime
    const [time] = startTime.split(' ')
    const [hours, minutes] = time.split(':').map(Number)
    eventDate.setHours(hours, minutes || 0, 0, 0)
  }
  
  return eventDate > now
}

export const eventsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    
    // Parse query parameters
    const {
      day,
      type,
      upcoming,
      limit = '50',
      page = '1',
      sort = 'date'
    } = req.query as Record<string, string>

    // Build where clause
    const where: any = {}
    if (day) where.day = { equals: parseInt(day) }
    if (type) where.type = { equals: type }

    // Add upcoming filter to where clause if requested
    if (upcoming === 'true') {
      const now = new Date()
      where.date = { greater_than: now.toISOString() }
    }

    // Fetch events with populated relationships
    const eventsResult = await payload.find({
      collection: 'events',
      where,
      limit: parseInt(limit),
      page: parseInt(page),
      sort: sort as any,
      depth: 2, // Populate speakers and their photos
    })

    // Transform events
    const transformedEvents: TransformedEvent[] = eventsResult.docs.map((event: any) => {
      const transformedEvent: TransformedEvent = {
        id: event.id,
        title: event.title,
        description: extractTextFromLexical(event.description),
        type: event.type,
        date: event.date,
        startTime: event.startTime,
        endTime: event.endTime,
        day: event.day,
        location: event.location,
        speakers: Array.isArray(event.speakers)
          ? event.speakers.map(transformSpeaker)
          : [],
        downloads: transformMedia(event.downloads),
        formattedDate: formatDate(event.date),
        formattedTime: formatTime(event.startTime, event.endTime),
        duration: calculateDuration(event.startTime, event.endTime),
        isUpcoming: isEventUpcoming(event.date, event.startTime),
        dayLabel: getDayLabel(event.day),
      }

      return transformedEvent
    })

    const currentPage = parseInt(page)
    const currentLimit = parseInt(limit)
    const totalPages = Math.ceil(eventsResult.totalDocs / currentLimit)

    const response: EventsResponse = {
      events: transformedEvents,
      totalEvents: eventsResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in events endpoint:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
