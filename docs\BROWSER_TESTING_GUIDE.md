# Browser Testing Guide for NPI CMS

## Overview

This guide covers how to test the CMS functionality through the browser, including admin panel testing, API endpoint testing, and frontend integration testing.

## Prerequisites

1. **Environment Setup**: Ensure your `.env` file is configured
2. **Database**: PostgreSQL database running and accessible
3. **Development Server**: Next.js development server running

## Environment Configuration for PostgreSQL

Update your `.env` file with PostgreSQL configuration:

```env
# Database Configuration (PostgreSQL)
DATABASE_URI=postgresql://username:password@localhost:5432/npi_cms
# Alternative format: postgres://username:password@localhost:5432/npi_cms

# PayloadCMS Configuration
PAYLOAD_SECRET=your-super-secure-secret-key-min-32-chars
JWT_SECRET=your-jwt-secret-key-min-32-chars

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:3000
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3000

# File Storage (Vercel Blob)
BLOB_READ_WRITE_TOKEN=your-vercel-blob-token

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Natural Products Institute

# Development
NODE_ENV=development
```

## 1. Admin Panel Testing

### Accessing the Admin Panel

1. **Start Development Server**:
   ```bash
   npm run dev
   ```

2. **Access Admin Panel**:
   - URL: `http://localhost:3000/admin`
   - Default admin credentials (created on first run):
     - Email: `<EMAIL>`
     - Password: `admin123` (change immediately)

### Admin Panel Test Scenarios

#### A. User Authentication
1. **Login Test**:
   - Navigate to `http://localhost:3000/admin`
   - Enter credentials
   - Verify successful login and dashboard access

2. **Role-Based Access**:
   - Test different user roles (admin, editor, content-manager)
   - Verify appropriate menu items and permissions

#### B. Collection Management
1. **Projects Collection**:
   - Navigate to `Admin > Collections > Projects`
   - Test create, read, update, delete operations
   - Verify field validation and required fields

2. **Success Stories Collection**:
   - Navigate to `Admin > Collections > Success Stories`
   - Test rich text editor functionality
   - Verify image upload and gallery features

3. **Resources Collection**:
   - Navigate to `Admin > Collections > Resources`
   - Test file upload functionality
   - Verify access control settings

## 2. API Endpoint Testing

### Using Browser Developer Tools

#### A. Testing GET Endpoints

1. **Open Browser Developer Tools** (F12)
2. **Navigate to Console Tab**
3. **Test API Endpoints**:

```javascript
// Test Projects API
fetch('/api/projects')
  .then(response => response.json())
  .then(data => console.log('Projects:', data))
  .catch(error => console.error('Error:', error))

// Test with filters
fetch('/api/projects?featured=true&limit=5')
  .then(response => response.json())
  .then(data => console.log('Featured Projects:', data))

// Test Success Stories
fetch('/api/success-stories')
  .then(response => response.json())
  .then(data => console.log('Success Stories:', data))

// Test Resources
fetch('/api/resources?category=indigenous-knowledge')
  .then(response => response.json())
  .then(data => console.log('Resources:', data))

// Test News
fetch('/api/news?featured=true')
  .then(response => response.json())
  .then(data => console.log('Featured News:', data))
```

#### B. Testing POST Endpoints (Contact Form)

```javascript
// Test Contact Form Submission
fetch('/api/contact-submissions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'Test User',
    email: '<EMAIL>',
    subject: 'Test Inquiry',
    category: 'general',
    message: 'This is a test message from browser testing.',
    priority: 'medium'
  })
})
.then(response => response.json())
.then(data => console.log('Contact Submission:', data))
.catch(error => console.error('Error:', error))
```

### Using Browser Extensions

#### Postman Browser Extension
1. Install Postman browser extension
2. Import API collection (create from documentation)
3. Test all endpoints with various parameters

#### REST Client Extensions
1. **Thunder Client** (VS Code)
2. **REST Client** (VS Code)
3. **Insomnia** (Desktop app)

## 3. Frontend Integration Testing

### Testing React Hooks

Create a test page to verify CMS integration:

```typescript
// pages/test-cms.tsx
import { useProjects, useNews, useContactForm } from '@/lib/cms'

export default function TestCMSPage() {
  const { data: projects, loading: projectsLoading, error: projectsError } = useProjects({
    featured: true,
    limit: 5
  })
  
  const { data: news, loading: newsLoading } = useNews({ limit: 3 })
  
  const { submitForm, loading: submitting, success, error } = useContactForm()

  const handleContactSubmit = async (e) => {
    e.preventDefault()
    const formData = new FormData(e.target)
    
    await submitForm({
      name: formData.get('name'),
      email: formData.get('email'),
      subject: formData.get('subject'),
      category: 'general',
      message: formData.get('message')
    })
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-8">CMS Integration Test</h1>
      
      {/* Projects Test */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Featured Projects</h2>
        {projectsLoading && <p>Loading projects...</p>}
        {projectsError && <p className="text-red-500">Error: {projectsError}</p>}
        {projects && (
          <div className="grid gap-4">
            {projects.map(project => (
              <div key={project.id} className="border p-4 rounded">
                <h3 className="font-semibold">{project.title}</h3>
                <p className="text-gray-600">{project.summary}</p>
                <span className="text-sm bg-blue-100 px-2 py-1 rounded">
                  {project.category}
                </span>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* News Test */}
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Latest News</h2>
        {newsLoading && <p>Loading news...</p>}
        {news && (
          <div className="grid gap-4">
            {news.map(article => (
              <div key={article.id} className="border p-4 rounded">
                <h3 className="font-semibold">{article.title}</h3>
                <p className="text-gray-600">{article.summary}</p>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* Contact Form Test */}
      <section>
        <h2 className="text-xl font-semibold mb-4">Contact Form Test</h2>
        <form onSubmit={handleContactSubmit} className="space-y-4 max-w-md">
          <input
            name="name"
            placeholder="Your Name"
            required
            className="w-full p-2 border rounded"
          />
          <input
            name="email"
            type="email"
            placeholder="Your Email"
            required
            className="w-full p-2 border rounded"
          />
          <input
            name="subject"
            placeholder="Subject"
            required
            className="w-full p-2 border rounded"
          />
          <textarea
            name="message"
            placeholder="Your Message"
            required
            className="w-full p-2 border rounded h-24"
          />
          <button
            type="submit"
            disabled={submitting}
            className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
          >
            {submitting ? 'Submitting...' : 'Submit'}
          </button>
          {success && <p className="text-green-500">Form submitted successfully!</p>}
          {error && <p className="text-red-500">Error: {error}</p>}
        </form>
      </section>
    </div>
  )
}
```

### Access the Test Page
1. Navigate to `http://localhost:3000/test-cms`
2. Verify all sections load correctly
3. Test form submission
4. Check browser console for any errors

## 4. Database Testing

### PostgreSQL Connection Test

Create a database test endpoint:

```typescript
// pages/api/test-db.ts
import payload from 'payload'

export default async function handler(req, res) {
  try {
    // Test database connection
    const result = await payload.find({
      collection: 'users',
      limit: 1,
    })

    res.status(200).json({
      success: true,
      message: 'Database connection successful',
      userCount: result.totalDocs,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message,
      timestamp: new Date().toISOString()
    })
  }
}
```

Test by navigating to: `http://localhost:3000/api/test-db`

## 5. Performance Testing

### Browser Performance Tools

#### A. Chrome DevTools
1. **Network Tab**: Monitor API request times
2. **Performance Tab**: Analyze page load performance
3. **Lighthouse**: Run performance audits

#### B. Performance Metrics to Monitor
- **API Response Times**: Should be < 500ms
- **Page Load Times**: Should be < 3 seconds
- **Database Query Times**: Monitor in server logs
- **Memory Usage**: Check for memory leaks

### Load Testing with Browser

```javascript
// Console script for basic load testing
async function loadTest(endpoint, requests = 10) {
  console.log(`Starting load test: ${requests} requests to ${endpoint}`)
  const startTime = Date.now()
  
  const promises = Array.from({ length: requests }, (_, i) => 
    fetch(endpoint)
      .then(response => ({
        status: response.status,
        time: Date.now() - startTime,
        request: i + 1
      }))
      .catch(error => ({
        error: error.message,
        request: i + 1
      }))
  )
  
  const results = await Promise.all(promises)
  const endTime = Date.now()
  
  console.log('Load test results:', {
    totalTime: endTime - startTime,
    requests: requests,
    results: results
  })
}

// Run load test
loadTest('/api/projects', 20)
```

## 6. Error Testing

### Testing Error Scenarios

```javascript
// Test invalid API requests
fetch('/api/projects/invalid-id')
  .then(response => response.json())
  .then(data => console.log('Invalid ID response:', data))

// Test malformed data
fetch('/api/contact-submissions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: '', // Invalid: empty name
    email: 'invalid-email', // Invalid: malformed email
    subject: 'a'.repeat(201), // Invalid: too long
  })
})
.then(response => response.json())
.then(data => console.log('Validation error response:', data))
```

## 7. Authentication Testing

### Testing Protected Endpoints

```javascript
// Test without authentication (should fail)
fetch('/api/admin/projects', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: 'Test Project',
    summary: 'Test'
  })
})
.then(response => response.json())
.then(data => console.log('Unauthorized response:', data))

// Test with authentication (get token from admin panel)
const token = 'your-jwt-token-here' // Get from browser storage after login

fetch('/api/admin/projects', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    title: 'Authenticated Test Project',
    summary: 'Test with authentication'
  })
})
.then(response => response.json())
.then(data => console.log('Authenticated response:', data))
```

## 8. Mobile Testing

### Responsive Design Testing
1. **Chrome DevTools**: Use device emulation
2. **Real Devices**: Test on actual mobile devices
3. **Browser Stack**: Cross-browser testing service

### Mobile-Specific Tests
- Touch interactions
- Form input on mobile keyboards
- Image loading and optimization
- API performance on slower connections

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check PostgreSQL is running
   - Verify connection string in `.env`
   - Check database permissions

2. **API Errors**:
   - Check server logs in terminal
   - Verify endpoint URLs
   - Check request/response formats

3. **Authentication Issues**:
   - Clear browser cache and cookies
   - Check JWT token expiration
   - Verify user roles and permissions

4. **File Upload Issues**:
   - Check Vercel Blob configuration
   - Verify file size limits
   - Check MIME type restrictions

### Debug Tools
- **Browser Console**: Check for JavaScript errors
- **Network Tab**: Monitor API requests
- **Server Logs**: Check terminal output
- **Database Logs**: Monitor PostgreSQL logs

## Next Steps

1. **Automated Testing**: Set up Cypress or Playwright for automated browser testing
2. **CI/CD Integration**: Include browser tests in deployment pipeline
3. **Monitoring**: Set up error tracking and performance monitoring
4. **User Testing**: Conduct user acceptance testing with content managers
