# NPI Website Testing Guide

## Overview

This guide covers the comprehensive testing strategy for the Natural Products Industry Initiative (NPI) website, including unit tests, integration tests, accessibility tests, and end-to-end tests.

## Testing Stack

- **Jest**: JavaScript testing framework
- **React Testing Library**: Testing utilities for React components
- **Jest DOM**: Custom Jest matchers for DOM elements
- **Playwright**: End-to-end testing (existing)
- **Vitest**: Integration testing (existing)

## Test Structure

```
__tests__/
├── components/
│   └── ui/
│       ├── npi-button.test.tsx
│       └── npi-search.test.tsx
├── integration/
│   ├── homepage.test.tsx
│   └── navigation.test.tsx
├── accessibility/
│   └── wcag-compliance.test.tsx
└── utils/
    └── test-utils.tsx
```

## Running Tests

### All Tests
```bash
pnpm test
```

### Watch Mode (Development)
```bash
pnpm test:watch
```

### Coverage Report
```bash
pnpm test:coverage
```

### Specific Test Types
```bash
# Unit tests only
pnpm test -- --testPathPattern=components

# Integration tests only
pnpm test:integration

# Accessibility tests only
pnpm test:accessibility

# End-to-end tests
pnpm test:e2e
```

## Test Categories

### 1. Unit Tests (`__tests__/components/`)

Test individual components in isolation:

- **Component Rendering**: Verify components render correctly
- **Props Handling**: Test different prop combinations
- **Event Handling**: Test user interactions
- **State Management**: Test component state changes
- **Accessibility**: Basic accessibility features

**Example:**
```typescript
describe('NPIButton', () => {
  it('renders with default props', () => {
    render(<NPIButton>Test Button</NPIButton>)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })
})
```

### 2. Integration Tests (`__tests__/integration/`)

Test component interactions and page-level functionality:

- **Page Rendering**: Full page rendering tests
- **Navigation**: Menu and routing functionality
- **Form Interactions**: Multi-step form workflows
- **Data Flow**: Component communication

**Example:**
```typescript
describe('Homepage Integration', () => {
  it('renders all main sections', () => {
    render(<HomePage />)
    expect(screen.getByRole('main')).toBeInTheDocument()
  })
})
```

### 3. Accessibility Tests (`__tests__/accessibility/`)

Ensure WCAG 2.1 AA compliance:

- **Keyboard Navigation**: Tab order and focus management
- **Screen Reader Support**: ARIA labels and semantic HTML
- **Color Contrast**: Sufficient contrast ratios
- **Form Accessibility**: Proper labeling and error handling

**Example:**
```typescript
describe('WCAG 2.1 AA Compliance', () => {
  it('supports keyboard navigation', () => {
    render(<NPIButton>Test</NPIButton>)
    const button = screen.getByRole('button')
    button.focus()
    expect(button).toHaveFocus()
  })
})
```

## Testing Best Practices

### 1. Component Testing

**Do:**
- Test behavior, not implementation
- Use semantic queries (getByRole, getByLabelText)
- Test user interactions
- Mock external dependencies

**Don't:**
- Test internal state directly
- Use implementation-specific selectors
- Test third-party library functionality

### 2. Accessibility Testing

**Key Areas:**
- Keyboard navigation
- Screen reader compatibility
- Focus management
- Color contrast
- Form accessibility
- Error handling

### 3. Performance Testing

**Metrics to Monitor:**
- Component render time
- Bundle size impact
- Memory usage
- Core Web Vitals

## Mock Setup

### Next.js Components
```typescript
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: any) {
    return <a href={href} {...props}>{children}</a>
  }
})

jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: any) {
    return <img src={src} alt={alt} {...props} />
  }
})
```

### API Calls
```typescript
jest.mock('@/lib/api', () => ({
  fetchData: jest.fn().mockResolvedValue({ data: 'mock data' })
}))
```

## Coverage Requirements

### Minimum Coverage Targets
- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

### Critical Components (90%+ Coverage)
- UI components (`src/components/ui/`)
- Form components
- Navigation components
- Accessibility features

## Continuous Integration

### Pre-commit Hooks
```bash
# Run tests before commit
pnpm test:ci

# Run linting
pnpm lint

# Check accessibility
pnpm test:accessibility
```

### CI Pipeline
1. Install dependencies
2. Run unit tests
3. Run integration tests
4. Run accessibility tests
5. Generate coverage report
6. Run E2E tests (on staging)

## Debugging Tests

### Common Issues

**Test Timeouts:**
```typescript
// Increase timeout for slow tests
jest.setTimeout(10000)
```

**Async Operations:**
```typescript
// Wait for async operations
await waitFor(() => {
  expect(screen.getByText('Loaded')).toBeInTheDocument()
})
```

**Mock Issues:**
```typescript
// Clear mocks between tests
beforeEach(() => {
  jest.clearAllMocks()
})
```

### Debug Mode
```bash
# Run tests in debug mode
node --inspect-brk node_modules/.bin/jest --runInBand
```

## Performance Testing

### Component Performance
```typescript
it('renders within acceptable time', () => {
  const startTime = performance.now()
  render(<ComplexComponent />)
  const endTime = performance.now()
  
  expect(endTime - startTime).toBeLessThan(100)
})
```

### Memory Leak Detection
```typescript
it('does not have memory leaks', () => {
  const { unmount } = render(<Component />)
  unmount()
  
  // Check for cleanup
  expect(document.body.innerHTML).toBe('')
})
```

## Accessibility Testing Tools

### Automated Testing
- **jest-axe**: Automated accessibility testing
- **Testing Library**: Built-in accessibility queries
- **WAVE**: Browser extension for manual testing

### Manual Testing Checklist
- [ ] Keyboard navigation works
- [ ] Screen reader announces content correctly
- [ ] Focus indicators are visible
- [ ] Color contrast meets WCAG standards
- [ ] Forms are properly labeled
- [ ] Error messages are accessible

## Test Data Management

### Mock Data
```typescript
// Create reusable mock data
export const mockArticle = {
  id: '1',
  title: 'Test Article',
  content: 'Test content',
  author: 'Test Author'
}
```

### Test Utilities
```typescript
// Custom render function with providers
export const renderWithProviders = (ui: React.ReactElement) => {
  return render(ui, {
    wrapper: ({ children }) => (
      <TestProvider>{children}</TestProvider>
    )
  })
}
```

## Reporting

### Coverage Reports
- HTML report: `coverage/lcov-report/index.html`
- JSON report: `coverage/coverage-final.json`
- Text summary in terminal

### Test Results
- JUnit XML for CI integration
- Console output for development
- GitHub Actions integration

## Maintenance

### Regular Tasks
- Update test dependencies monthly
- Review and update test coverage targets
- Audit accessibility compliance quarterly
- Performance benchmark reviews

### When to Update Tests
- Component API changes
- New accessibility requirements
- Performance regression fixes
- User workflow changes

## Resources

- [Testing Library Documentation](https://testing-library.com/)
- [Jest Documentation](https://jestjs.io/)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [React Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
