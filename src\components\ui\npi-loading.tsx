import React from 'react'
import { cn } from '@/utilities/ui'

interface NPILoadingProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse'
  className?: string
  text?: string
}

export const NPILoading: React.FC<NPILoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  className,
  text,
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  }

  const renderSpinner = () => (
    <div
      className={cn(
        'border-2 border-primary border-t-transparent rounded-full animate-spin',
        sizeClasses[size],
        className,
      )}
    />
  )

  const renderDots = () => (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-primary rounded-full animate-pulse',
            size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-2 h-2' : 'w-3 h-3',
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  )

  const renderPulse = () => (
    <div className={cn('bg-primary rounded-full animate-pulse', sizeClasses[size], className)} />
  )

  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return renderDots()
      case 'pulse':
        return renderPulse()
      default:
        return renderSpinner()
    }
  }

  return (
    <div className="flex flex-col items-center justify-center space-y-2">
      {renderLoader()}
      {text && <p className="text-sm text-muted-foreground font-npi">{text}</p>}
    </div>
  )
}

// Page loading component
export const NPIPageLoading: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <NPILoading size="lg" text="Loading..." />
        <p className="mt-4 text-muted-foreground font-npi">Please wait while we load the content</p>
      </div>
    </div>
  )
}

// Section loading component
export const NPISectionLoading: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('py-16 flex items-center justify-center', className)}>
      <NPILoading size="md" text="Loading content..." />
    </div>
  )
}
