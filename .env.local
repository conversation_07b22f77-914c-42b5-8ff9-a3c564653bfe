# MongoDB Configuration
# For local development, use a local MongoDB instance
#DATABASE_URI=mongodb+srv://connect:<EMAIL>/npi-cms

# For MongoDB Atlas (cloud), use this format:
DATABASE_URI=mongodb+srv://connect:<EMAIL>/?retryWrites=true&w=majority&appName=npi

# PayloadCMS Configuration
PAYLOAD_SECRET=your-super-secure-secret-key-min-32-chars-here
JWT_SECRET=your-jwt-secret-key-min-32-chars-here

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:3000
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3000

# File Storage (Vercel Blob)
# BLOB_READ_WRITE_TOKEN=your-vercel-blob-token

# Google Maps API (if needed)
# NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Email Configuration (if needed)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password
