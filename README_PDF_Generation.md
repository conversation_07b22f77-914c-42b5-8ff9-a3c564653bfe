# NPI API Documentation PDF Generation

## Files Created

1. **NPI_API_Documentation.md** - Complete API documentation in Markdown format
2. **NPI_API_Documentation.html** - Styled HTML version for PDF conversion
3. **generate-pdf.cjs** - <PERSON>ript to convert Markdown to HTML
4. **open-documentation.bat** - Helper script to open documentation

## How to Generate PDF

### Method 1: Browser Print to PDF (Recommended)

1. The HTML file should already be open in your browser
2. Press `Ctrl+P` (or `Cmd+P` on Mac) to open print dialog
3. Select **"Save as PDF"** as the destination
4. In **"More settings"**:
   - Paper size: **A4**
   - Margins: **Default** or **Minimum**
   - Scale: **100%**
   - Options: Check **"Background graphics"**
5. Click **"Save"** and choose your desired location

### Method 2: Using Online Converters

1. Upload `NPI_API_Documentation.md` to online converters like:
   - [Markdown to PDF](https://md-to-pdf.fly.dev/)
   - [Dillinger.io](https://dillinger.io/)
   - [StackEdit](https://stackedit.io/)

### Method 3: Using Command Line Tools

If you have additional tools installed:

```bash
# Using pandoc (if installed)
pandoc NPI_API_Documentation.md -o NPI_API_Documentation.pdf

# Using wkhtmltopdf (if installed)
wkhtmltopdf NPI_API_Documentation.html NPI_API_Documentation.pdf
```

## Documentation Contents

The comprehensive API documentation includes:

### 📋 **Overview & Setup**
- Base URLs (Production & Development)
- Authentication methods
- Common parameters
- Response formats

### 🔐 **Authentication**
- Login/Logout endpoints
- JWT token management
- Password reset flow
- User management

### 📊 **Public Endpoints**
- **Projects API** - List, get, search projects
- **Success Stories API** - Community success stories
- **Resources API** - Documents, reports, downloads
- **News API** - News articles and updates
- **Media Gallery API** - Images, videos, media files
- **Partnerships API** - Partnership opportunities
- **Investment Opportunities API** - Funding opportunities
- **Counties API** - Geographic data
- **Events API** - Event listings
- **Contact API** - Contact form submissions

### 🔒 **Admin Endpoints**
- Content management (CRUD operations)
- User management
- Contact submission management
- County management
- Authentication required

### 🚀 **Advanced Features**
- **GraphQL API** - Alternative query interface
- **Rate Limiting** - API usage limits
- **Error Handling** - Comprehensive error codes
- **SDK Usage** - JavaScript/TypeScript examples
- **React Hooks** - Frontend integration examples

### 📝 **Examples & Code Samples**
- Authentication flow examples
- API request/response examples
- Error handling patterns
- Pagination examples
- Search and filtering examples

### 🛠 **Technical Details**
- Data type definitions
- Security features
- Database configuration
- Testing instructions

## Key Features Documented

### **Comprehensive Coverage**
- ✅ All 50+ API endpoints
- ✅ Authentication & authorization
- ✅ Request/response examples
- ✅ Error codes and handling
- ✅ Rate limiting policies

### **Developer-Friendly**
- ✅ Code examples in multiple languages
- ✅ SDK usage patterns
- ✅ React hooks integration
- ✅ Testing instructions
- ✅ Interactive API testing dashboard

### **Production-Ready**
- ✅ Security best practices
- ✅ Performance considerations
- ✅ Monitoring and debugging
- ✅ Database configuration
- ✅ Deployment guidelines

## File Locations

```
📁 npi-website/
├── 📄 NPI_API_Documentation.md      # Source markdown
├── 📄 NPI_API_Documentation.html    # Styled HTML version
├── 📄 generate-pdf.cjs              # Conversion script
├── 📄 open-documentation.bat        # Helper script
└── 📄 README_PDF_Generation.md      # This file
```

## Next Steps

1. **Review the documentation** in your browser
2. **Generate the PDF** using Method 1 above
3. **Share with your team** or stakeholders
4. **Update as needed** by editing the Markdown file and re-running the script

The documentation is comprehensive and covers all aspects of the NPI platform's API, making it suitable for:
- **Developers** integrating with the API
- **Technical teams** understanding the platform
- **Stakeholders** reviewing platform capabilities
- **Documentation** for compliance and auditing

## Support

If you need to update the documentation:
1. Edit `NPI_API_Documentation.md`
2. Run `node generate-pdf.cjs` to regenerate HTML
3. Follow the PDF generation steps above
