import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'
import { 
  partnershipApplicationById<PERSON><PERSON><PERSON>,
  updatePartnershipApplication<PERSON><PERSON><PERSON>,
  deletePartnershipApplicationHandler
} from '@/endpoints/partnership-applications'

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      params: { id },
      headers: {
        get: (name: string) => request.headers.get(name),
      },
      user: null,
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await partnershipApplicationByIdHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Partnership Application by ID API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params
    const body = await request.json()

    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      params: { id },
      body,
      headers: {
        get: (name: string) => request.headers.get(name),
      },
      user: null,
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await updatePartnershipApplicationHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Update Partnership Application API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const payload = await getPayload({ config })
    const { id } = await params

    // Create a mock request object that matches PayloadRequest interface
    const mockReq = {
      payload,
      params: { id },
      headers: {
        get: (name: string) => request.headers.get(name),
      },
      user: null,
    } as any

    // Create a mock response object
    let responseData: any
    let statusCode = 200

    const mockRes = {
      status: (code: number) => {
        statusCode = code
        return mockRes
      },
      json: (data: any) => {
        responseData = data
        return mockRes
      },
    } as any

    // Call the handler
    await deletePartnershipApplicationHandler(mockReq, mockRes)

    return NextResponse.json(responseData, { status: statusCode })
  } catch (error) {
    console.error('Delete Partnership Application API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
