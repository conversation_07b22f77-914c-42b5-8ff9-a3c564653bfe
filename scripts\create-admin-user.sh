#!/bin/bash

# Script to create an admin user via API
# Usage: ./scripts/create-admin-user.sh

BASE_URL="http://localhost:3000/api"

# New admin user details
ADMIN_NAME="Ivy Njoroge"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
ADMIN_ROLE="admin"

# Existing admin credentials for authentication (update these if needed)
EXISTING_ADMIN_EMAIL="<EMAIL>"
EXISTING_ADMIN_PASSWORD="admin123"

echo "🚀 Admin User Creation Script"
echo "=============================="
echo "Creating admin user: $ADMIN_NAME ($ADMIN_EMAIL)"
echo ""

# Step 1: Login to get auth token
echo "🔐 Logging in as existing admin..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/users/login" \
  -H "Content-Type: application/json" \
  -d "{\"email\":\"$EXISTING_ADMIN_EMAIL\",\"password\":\"$EXISTING_ADMIN_PASSWORD\"}")

# Check if login was successful
if echo "$LOGIN_RESPONSE" | grep -q '"token"'; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*' | cut -d'"' -f4)
    echo "✅ Login successful"
else
    echo "❌ Login failed. Response: $LOGIN_RESPONSE"
    echo "Please check your existing admin credentials in the script."
    exit 1
fi

# Step 2: Check if user already exists
echo "🔍 Checking if user $ADMIN_EMAIL already exists..."
USER_CHECK_RESPONSE=$(curl -s -X GET "$BASE_URL/users?where[email][equals]=$ADMIN_EMAIL" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

if echo "$USER_CHECK_RESPONSE" | grep -q '"totalDocs":0'; then
    echo "✅ User does not exist, proceeding with creation..."
else
    echo "⚠️  User $ADMIN_EMAIL already exists!"
    echo "Skipping creation..."
    exit 0
fi

# Step 3: Create the admin user
echo "👤 Creating admin user: $ADMIN_EMAIL..."
CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/users" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"$ADMIN_NAME\",
    \"email\": \"$ADMIN_EMAIL\",
    \"password\": \"$ADMIN_PASSWORD\",
    \"role\": \"$ADMIN_ROLE\",
    \"_verified\": true,
    \"_verificationToken\": null,
    \"loginAttempts\": 0,
    \"lockUntil\": null,
    \"isActive\": true,
    \"preferences\": {
      \"newsletter\": false,
      \"notifications\": true,
      \"language\": \"en\"
    }
  }")

# Check if user creation was successful
if echo "$CREATE_RESPONSE" | grep -q '"id"'; then
    USER_ID=$(echo "$CREATE_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    echo "✅ Admin user created successfully!"
    echo "📧 Email: $ADMIN_EMAIL"
    echo "👤 Name: $ADMIN_NAME"
    echo "🔑 Role: $ADMIN_ROLE"
    echo "🆔 ID: $USER_ID"
    echo ""
    echo "🎉 Admin user creation completed successfully!"
    echo ""
    echo "📋 Login Details:"
    echo "   Email: $ADMIN_EMAIL"
    echo "   Password: $ADMIN_PASSWORD"
    echo "   Role: $ADMIN_ROLE"
    echo ""
    echo "🔗 You can now login at: http://localhost:3000/admin"
else
    echo "❌ Failed to create admin user. Response: $CREATE_RESPONSE"
    exit 1
fi
