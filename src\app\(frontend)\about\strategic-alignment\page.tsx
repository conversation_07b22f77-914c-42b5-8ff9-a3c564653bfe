import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import { NPIScrollToTop } from '@/components/ui/npi-scroll-to-top'
import PageClient from './page.client'

// Strategic alignment sub-page components
// Uses: NPIStrategicAlignmentHeroBlock, NPIStrategicAlignmentBlock from about/strategic-alignment

export const metadata: Metadata = {
  title: 'Strategic Alignment - About NPI',
  description:
    "Learn about NPI's strategic alignment with Kenya's key development frameworks including Vision 2030, MTP IV, BeTA, and National Museums of Kenya Strategic Plan.",
}

const strategicAlignmentPageLayout = [
  {
    blockType: 'npiStrategicAlignmentHero' as const,
  },
  {
    blockType: 'npiStrategicAlignment' as const,
  },
]

export default function StrategicAlignmentPage() {
  return (
    <>
      <PageClient />
      <article className="pb-12" style={{ scrollBehavior: 'smooth' }}>
        {strategicAlignmentPageLayout.map((block, index) => (
          <section
            key={index}
            className={`
              ${index === 0 ? '' : '-mt-1'}
              relative
              ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
            `}
          >
            <RenderBlocks blocks={[block]} />
          </section>
        ))}
      </article>
      <NPIScrollToTop showAfter={400} />
    </>
  )
}
