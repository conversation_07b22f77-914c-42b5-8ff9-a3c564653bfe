# NPI Platform API Documentation

## Table of Contents
1. [Overview](#overview)
2. [Base URLs](#base-urls)
3. [Authentication](#authentication)
4. [Common Parameters](#common-parameters)
5. [Response Format](#response-format)
6. [Public Endpoints](#public-endpoints)
7. [Authentication Endpoints](#authentication-endpoints)
8. [Admin Endpoints](#admin-endpoints)
9. [GraphQL API](#graphql-api)
10. [<PERSON>rro<PERSON> Handling](#error-handling)
11. [Rate Limiting](#rate-limiting)
12. [SDK Usage](#sdk-usage)
13. [Examples](#examples)

## Overview

The NPI (National Platform for Innovation) API provides comprehensive access to projects, success stories, resources, news, partnerships, investment opportunities, and more. The API is built on PayloadCMS and supports both REST and GraphQL interfaces.

## Base URLs

```
Production: https://npi-website.vercel.app/api
Development: http://localhost:3000/api
GraphQL: /api/graphql
Admin Panel: /admin
API Testing: /test-api
```

## Authentication

### Public Access
Most read endpoints are publicly accessible without authentication.

### Protected Access
Admin endpoints require JWT authentication:
```http
Authorization: Bearer <jwt_token>
```

### Authentication Flow
1. Login via `/api/users/login`
2. Receive JWT token
3. Include token in subsequent requests
4. Refresh token via `/api/users/refresh-token`

## Common Parameters

All list endpoints support these query parameters:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number |
| `limit` | integer | 20 | Items per page (max 100) |
| `sort` | string | `-updatedAt` | Sort field and direction |
| `search` | string | - | Text search |
| `featured` | boolean | - | Filter featured items |

## Response Format

### Success Response
```json
{
  "data": [...],
  "totalDocs": 150,
  "page": 1,
  "limit": 20,
  "totalPages": 8,
  "hasNextPage": true,
  "hasPrevPage": false
}
```

### Error Response
```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "code": "ERROR_CODE",
  "statusCode": 400,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Public Endpoints

### Projects API

#### List Projects
```http
GET /api/projects
```

**Query Parameters:**
- `category`: Filter by category
- `pillar`: Filter by strategic pillar
- `status`: Filter by status
- `county`: Filter by county ID
- `featured`: Filter featured projects
- `search`: Text search

**Example:**
```http
GET /api/projects?category=community-empowerment&featured=true&limit=10
```

#### Get Single Project
```http
GET /api/projects/:id
```

### Success Stories API

#### List Success Stories
```http
GET /api/success-stories
```

**Query Parameters:**
- `category`: Filter by category
- `county`: Filter by county ID

#### Get Success Story
```http
GET /api/success-stories/:id
```

### Resources API

#### List Resources
```http
GET /api/resources
```

**Query Parameters:**
- `type`: Filter by resource type
- `category`: Filter by category
- `language`: Filter by language
- `access`: Filter by access level

#### Get Resource
```http
GET /api/resources/:id
```

#### Download Resource
```http
GET /api/resources/:id/download
```

### News API

#### List News Articles
```http
GET /api/news
```

**Query Parameters:**
- `category`: Filter by category
- `urgent`: Filter urgent news
- `author`: Filter by author name

#### Get News Article
```http
GET /api/news/:id
```

### Media Gallery API

#### List Media Items
```http
GET /api/media-gallery
```

**Query Parameters:**
- `type`: Filter by media type
- `category`: Filter by category
- `event`: Filter by event ID
- `project`: Filter by project ID
- `license`: Filter by license type

#### Get Media Item
```http
GET /api/media-gallery/:id
```

### Partnerships API

#### List Partnerships
```http
GET /api/partnerships
```

**Query Parameters:**
- `type`: Filter by partnership type
- `status`: Filter by status
- `partner`: Filter by partner ID

#### Get Partnership
```http
GET /api/partnerships/:id
```

### Investment Opportunities API

#### List Investment Opportunities
```http
GET /api/investment-opportunities
```

**Query Parameters:**
- `sector`: Filter by sector
- `investmentType`: Filter by investment type
- `status`: Filter by status
- `urgent`: Filter urgent opportunities
- `minAmount`: Minimum funding amount
- `maxAmount`: Maximum funding amount

#### Get Investment Opportunity
```http
GET /api/investment-opportunities/:id
```

### Counties API

#### List Counties
```http
GET /api/counties
```

#### Get County
```http
GET /api/counties/:id
```

#### Get Counties in Bounds
```http
GET /api/counties/bounds?north=1.0&south=-5.0&east=42.0&west=33.0
```

### Events API

#### List Events
```http
GET /api/events
```

**Query Parameters:**
- `type`: Filter by event type
- `status`: Filter by status
- `county`: Filter by county
- `upcoming`: Filter upcoming events
- `featured`: Filter featured events

#### Get Event
```http
GET /api/events/:id
```

### Contact API

#### Submit Contact Form
```http
POST /api/contact-submissions
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+254700000000",
  "organization": "Example Org",
  "role": "Manager",
  "subject": "Partnership Inquiry",
  "category": "partnership",
  "priority": "medium",
  "message": "I am interested in partnering with NPI...",
  "location": {
    "county": "county-id",
    "city": "Nairobi",
    "country": "Kenya"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Contact submission received successfully",
  "submissionId": "submission-id",
  "status": "new"
}
```

## Authentication Endpoints

### Login
```http
POST /api/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "message": "Auth Passed",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "exp": **********
}
```

### Logout
```http
POST /api/users/logout
Authorization: Bearer <token>
```

### Get Current User
```http
GET /api/users/me
Authorization: Bearer <token>
```

### Refresh Token
```http
POST /api/users/refresh-token
Authorization: Bearer <token>
```

### Forgot Password
```http
POST /api/users/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### Reset Password
```http
POST /api/users/reset-password
Content-Type: application/json

{
  "token": "reset-token-from-email",
  "password": "newpassword123"
}
```

## Admin Endpoints

All admin endpoints require authentication and appropriate permissions.

### Projects Management

#### Create Project
```http
POST /api/projects
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "New Community Project",
  "summary": "Empowering communities through sustainable development",
  "category": "community-empowerment",
  "pillar": "community-innovation",
  "status": "active",
  "timeline": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T00:00:00.000Z"
  },
  "budget": {
    "totalBudget": 500000,
    "currency": "KES"
  },
  "location": {
    "counties": ["nairobi", "kiambu"],
    "specificLocation": "Nairobi and surrounding areas"
  },
  "featured": false,
  "published": true
}
```

#### Update Project
```http
PUT /api/projects/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Updated Project Title",
  "status": "completed",
  "impact": {
    "beneficiaries": 1200,
    "communities": 6,
    "jobsCreated": 75
  }
}
```

#### Delete Project
```http
DELETE /api/projects/:id
Authorization: Bearer <token>
```

### Contact Submissions Management

#### List Contact Submissions
```http
GET /api/contact-submissions
Authorization: Bearer <token>
```

#### Get Contact Submission
```http
GET /api/contact-submissions/:id
Authorization: Bearer <token>
```

#### Update Contact Submission
```http
PUT /api/contact-submissions/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "in-progress",
  "assignedTo": "user-id",
  "department": "partnerships",
  "priority": "high"
}
```

### Counties Management

#### Create County
```http
POST /api/counties
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "New County",
  "code": "001"
}
```

#### Update County
```http
PUT /api/counties/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Updated County Name"
}
```

#### Delete County
```http
DELETE /api/counties/:id
Authorization: Bearer <token>
```

## GraphQL API

### Endpoint
```http
POST /api/graphql
Content-Type: application/json
```

### Example Query
```graphql
query GetProjects($limit: Int, $where: Project_where) {
  Projects(limit: $limit, where: $where) {
    docs {
      id
      title
      summary
      category
      status
      featured
      createdAt
      updatedAt
    }
    totalDocs
    hasNextPage
    hasPrevPage
  }
}
```

### Example Variables
```json
{
  "limit": 10,
  "where": {
    "featured": {
      "equals": true
    }
  }
}
```

## Error Handling

### Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `VALIDATION_ERROR` | 400 | Input validation failed |
| `AUTHENTICATION_ERROR` | 401 | Authentication required |
| `AUTHORIZATION_ERROR` | 403 | Insufficient permissions |
| `NOT_FOUND_ERROR` | 404 | Resource not found |
| `CONFLICT_ERROR` | 409 | Resource conflict |
| `RATE_LIMIT_ERROR` | 429 | Rate limit exceeded |
| `INTERNAL_SERVER_ERROR` | 500 | Server error |
| `SERVICE_UNAVAILABLE_ERROR` | 503 | Service unavailable |

### Error Response Example
```json
{
  "error": "VALIDATION_ERROR",
  "message": "Name, email, subject, message, and category are required",
  "statusCode": 400,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestId": "req_123456"
}
```

## Rate Limiting

API endpoints are rate limited to ensure fair usage:

- **Authenticated users**: 1000 requests per 15 minutes
- **Anonymous users**: 100 requests per 15 minutes
- **Contact form**: 5 submissions per hour per IP
- **Authentication endpoints**: 10 attempts per 15 minutes per IP

## SDK Usage

### JavaScript/TypeScript SDK

```javascript
import { cmsAPI } from '@/lib/cms'

// Fetch projects
const projects = await cmsAPI.projects.getAll({
  category: 'community-empowerment',
  featured: true,
  limit: 10
})

// Submit contact form
await cmsAPI.contact.submit({
  name: 'John Doe',
  email: '<EMAIL>',
  subject: 'Partnership Inquiry',
  category: 'partnership',
  message: 'I am interested in partnering with NPI...'
})

// Get single project
const project = await cmsAPI.projects.getById('project-id')

// Search resources
const resources = await cmsAPI.resources.getAll({
  search: 'traditional medicine',
  type: 'research-report'
})
```

### React Hooks

```javascript
import { useProjects, useContactForm } from '@/lib/cms'

function ProjectsList() {
  const { data: projects, loading, error } = useProjects({
    featured: true,
    limit: 6
  })

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>

  return (
    <div>
      {projects.map(project => (
        <div key={project.id}>{project.title}</div>
      ))}
    </div>
  )
}

function ContactForm() {
  const { submitForm, loading, error, success } = useContactForm()

  const handleSubmit = async (formData) => {
    await submitForm(formData)
  }

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      {error && <div>Error: {error}</div>}
      {success && <div>Form submitted successfully!</div>}
    </form>
  )
}
```

## Examples

### Fetch Featured Projects
```javascript
const response = await fetch('/api/projects?featured=true&limit=6')
const { data: projects } = await response.json()

projects.forEach(project => {
  console.log(`${project.title} - ${project.category}`)
})
```

### Submit Contact Form with Error Handling
```javascript
try {
  const response = await fetch('/api/contact-submissions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      name: 'John Doe',
      email: '<EMAIL>',
      subject: 'Partnership Inquiry',
      category: 'partnership',
      message: 'I am interested in partnering with NPI...'
    })
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message)
  }

  const result = await response.json()
  console.log('Submission successful:', result.submissionId)
} catch (error) {
  console.error('Submission failed:', error.message)
}
```

### Search Resources with Filters
```javascript
const searchParams = new URLSearchParams({
  search: 'traditional medicine',
  type: 'research-report',
  language: 'en',
  limit: '20'
})

const response = await fetch(`/api/resources?${searchParams}`)
const { data: resources } = await response.json()
```

### Authentication Flow Example
```javascript
// Login
const loginResponse = await fetch('/api/users/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
})

const { token, user } = await loginResponse.json()
localStorage.setItem('auth-token', token)

// Make authenticated request
const projectsResponse = await fetch('/api/projects', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    title: 'New Project',
    category: 'community-empowerment',
    status: 'active'
  })
})
```

### Pagination Example
```javascript
async function fetchAllProjects() {
  let allProjects = []
  let page = 1
  let hasMore = true

  while (hasMore) {
    const response = await fetch(`/api/projects?page=${page}&limit=50`)
    const data = await response.json()

    allProjects = [...allProjects, ...data.data]
    hasMore = data.hasNextPage
    page++
  }

  return allProjects
}
```

## Data Types

### Media Object
```json
{
  "id": "media-id",
  "filename": "image.jpg",
  "url": "https://storage.url/image.jpg",
  "alt": "Alt text",
  "width": 1920,
  "height": 1080,
  "mimeType": "image/jpeg",
  "filesize": 1024000
}
```

### County Object
```json
{
  "id": "county-id",
  "name": "Nairobi",
  "code": "047"
}
```

### Rich Text Object
```json
{
  "root": {
    "children": [
      {
        "children": [
          {
            "detail": 0,
            "format": 0,
            "mode": "normal",
            "style": "",
            "text": "This is a paragraph of text.",
            "type": "text",
            "version": 1
          }
        ],
        "direction": "ltr",
        "format": "",
        "indent": 0,
        "type": "paragraph",
        "version": 1
      }
    ],
    "direction": "ltr",
    "format": "",
    "indent": 0,
    "type": "root",
    "version": 1
  }
}
```

### Project Object (Full)
```json
{
  "id": "project-id",
  "title": "Community Empowerment Initiative",
  "summary": "Empowering local communities through skill development",
  "category": "community-empowerment",
  "pillar": "community-innovation",
  "status": "active",
  "timeline": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T00:00:00.000Z",
    "milestones": [
      {
        "title": "Project Launch",
        "date": "2024-01-01T00:00:00.000Z",
        "completed": true
      }
    ]
  },
  "budget": {
    "totalBudget": 500000,
    "currency": "KES",
    "fundingSources": [
      {
        "source": "Government Grant",
        "amount": 300000,
        "percentage": 60
      }
    ]
  },
  "location": {
    "counties": ["nairobi", "kiambu"],
    "specificLocation": "Nairobi and surrounding areas",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    }
  },
  "impact": {
    "beneficiaries": 1000,
    "communities": 5,
    "jobsCreated": 50,
    "metrics": [
      {
        "metric": "Training Sessions Conducted",
        "value": "25",
        "unit": "sessions"
      }
    ]
  },
  "team": {
    "projectManager": {
      "name": "John Doe",
      "role": "Project Manager",
      "contact": "<EMAIL>"
    }
  },
  "tags": [
    { "tag": "community" },
    { "tag": "empowerment" }
  ],
  "featured": true,
  "published": true,
  "slug": "community-empowerment-initiative",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

## Testing

### API Testing Dashboard
Visit `/test-api` in your browser for an interactive API testing interface:
- **URL**: `http://localhost:3000/test-api`
- **Features**: Test all endpoints, authentication, real-time results
- **Authentication**: Login directly from the testing interface

### Manual Testing with Browser Console
```javascript
// Test public endpoints
fetch('/api/projects?featured=true')
  .then(r => r.json())
  .then(console.log)

// Test with authentication
fetch('/api/contact-submissions', {
  headers: { 'Authorization': 'Bearer ' + localStorage.getItem('auth-token') }
})
.then(r => r.json())
.then(console.log)
```

## Database Configuration

The API uses PostgreSQL as the primary database:

```env
# PostgreSQL Configuration
DATABASE_URI=postgresql://username:password@localhost:5432/npi_cms

# For cloud databases (e.g., Supabase, AWS RDS)
DATABASE_URI=********************************/dbname?sslmode=require
```

## Security Features

- **Password Hashing**: Automatic bcrypt hashing for user passwords
- **JWT Tokens**: Secure, stateless authentication with configurable expiration
- **Account Locking**: Automatic account locking after failed login attempts
- **Rate Limiting**: Built-in protection against brute force attacks
- **Input Validation**: Comprehensive validation for all API inputs
- **CORS Protection**: Configurable CORS policies
- **SQL Injection Protection**: Built-in protection via ORM

## Support

For API support and questions:
- **Documentation**: See `/docs` directory for additional documentation
- **Testing**: Use `/test-api` for interactive testing
- **Issues**: Report issues through the platform's issue tracking system

---

*This documentation covers the NPI Platform API v1.0. For the latest updates and changes, please refer to the platform's changelog and release notes.*
```
