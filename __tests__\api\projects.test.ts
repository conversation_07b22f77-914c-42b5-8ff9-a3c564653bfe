import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals'
import { MongoMemoryServer } from 'mongodb-memory-server'
import mongoose from 'mongoose'
import payload from 'payload'
import { initPayloadTest } from '../helpers/initPayloadTest'

describe('Projects API', () => {
  let mongod: MongoMemoryServer
  let payloadInstance: typeof payload

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create()
    const uri = mongod.getUri()
    
    payloadInstance = await initPayloadTest({
      __dirname,
      mongoURL: uri,
    })
  })

  afterAll(async () => {
    if (payloadInstance) {
      await payloadInstance.db.destroy()
    }
    if (mongod) {
      await mongod.stop()
    }
    await mongoose.connection.close()
  })

  beforeEach(async () => {
    // Clean up collections before each test
    await payloadInstance.delete({
      collection: 'projects',
      where: {},
    })
  })

  describe('POST /api/projects', () => {
    it('should create a new project with valid data', async () => {
      const projectData = {
        title: 'Test Project',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'This is a test project description.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'A test project for unit testing',
        category: 'community-empowerment',
        pillar: 'community-innovation',
        status: 'active',
        timeline: {
          startDate: '2024-01-01T00:00:00.000Z',
        },
        featured: false,
        published: true,
        slug: 'test-project',
      }

      const project = await payloadInstance.create({
        collection: 'projects',
        data: projectData,
      })

      expect(project).toBeDefined()
      expect(project.title).toBe(projectData.title)
      expect(project.summary).toBe(projectData.summary)
      expect(project.category).toBe(projectData.category)
      expect(project.status).toBe(projectData.status)
      expect(project.slug).toBe(projectData.slug)
    })

    it('should fail to create project with invalid data', async () => {
      const invalidProjectData = {
        // Missing required fields
        summary: 'Invalid project',
      }

      await expect(
        payloadInstance.create({
          collection: 'projects',
          data: invalidProjectData,
        })
      ).rejects.toThrow()
    })

    it('should fail to create project with duplicate slug', async () => {
      const projectData = {
        title: 'First Project',
        description: {
          root: {
            children: [],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'First project',
        category: 'community-empowerment',
        pillar: 'community-innovation',
        status: 'active',
        timeline: {
          startDate: '2024-01-01T00:00:00.000Z',
        },
        slug: 'duplicate-slug',
      }

      // Create first project
      await payloadInstance.create({
        collection: 'projects',
        data: projectData,
      })

      // Try to create second project with same slug
      const duplicateProjectData = {
        ...projectData,
        title: 'Second Project',
        summary: 'Second project',
      }

      await expect(
        payloadInstance.create({
          collection: 'projects',
          data: duplicateProjectData,
        })
      ).rejects.toThrow()
    })
  })

  describe('GET /api/projects', () => {
    beforeEach(async () => {
      // Create test projects
      const projects = [
        {
          title: 'Featured Project',
          description: { root: { children: [], direction: 'ltr', format: '', indent: 0, type: 'root', version: 1 } },
          summary: 'A featured project',
          category: 'community-empowerment',
          pillar: 'community-innovation',
          status: 'active',
          timeline: { startDate: '2024-01-01T00:00:00.000Z' },
          featured: true,
          published: true,
          slug: 'featured-project',
        },
        {
          title: 'Regular Project',
          description: { root: { children: [], direction: 'ltr', format: '', indent: 0, type: 'root', version: 1 } },
          summary: 'A regular project',
          category: 'research-development',
          pillar: 'indigenous-knowledge',
          status: 'completed',
          timeline: { startDate: '2023-01-01T00:00:00.000Z' },
          featured: false,
          published: true,
          slug: 'regular-project',
        },
        {
          title: 'Draft Project',
          description: { root: { children: [], direction: 'ltr', format: '', indent: 0, type: 'root', version: 1 } },
          summary: 'A draft project',
          category: 'capacity-building',
          pillar: 'capacity-building',
          status: 'planning',
          timeline: { startDate: '2024-06-01T00:00:00.000Z' },
          featured: false,
          published: false,
          slug: 'draft-project',
        },
      ]

      for (const project of projects) {
        await payloadInstance.create({
          collection: 'projects',
          data: project,
        })
      }
    })

    it('should return all published projects', async () => {
      const result = await payloadInstance.find({
        collection: 'projects',
        where: {
          published: { equals: true },
        },
      })

      expect(result.docs).toHaveLength(2)
      expect(result.docs.every(project => project.published)).toBe(true)
    })

    it('should filter projects by category', async () => {
      const result = await payloadInstance.find({
        collection: 'projects',
        where: {
          category: { equals: 'community-empowerment' },
          published: { equals: true },
        },
      })

      expect(result.docs).toHaveLength(1)
      expect(result.docs[0].category).toBe('community-empowerment')
    })

    it('should filter featured projects', async () => {
      const result = await payloadInstance.find({
        collection: 'projects',
        where: {
          featured: { equals: true },
          published: { equals: true },
        },
      })

      expect(result.docs).toHaveLength(1)
      expect(result.docs[0].featured).toBe(true)
    })

    it('should support pagination', async () => {
      const result = await payloadInstance.find({
        collection: 'projects',
        where: {
          published: { equals: true },
        },
        limit: 1,
        page: 1,
      })

      expect(result.docs).toHaveLength(1)
      expect(result.totalDocs).toBe(2)
      expect(result.totalPages).toBe(2)
      expect(result.hasNextPage).toBe(true)
    })

    it('should support sorting', async () => {
      const result = await payloadInstance.find({
        collection: 'projects',
        where: {
          published: { equals: true },
        },
        sort: 'title',
      })

      expect(result.docs).toHaveLength(2)
      expect(result.docs[0].title).toBe('Featured Project')
      expect(result.docs[1].title).toBe('Regular Project')
    })
  })

  describe('GET /api/projects/:id', () => {
    let testProject: any

    beforeEach(async () => {
      testProject = await payloadInstance.create({
        collection: 'projects',
        data: {
          title: 'Test Project',
          description: { root: { children: [], direction: 'ltr', format: '', indent: 0, type: 'root', version: 1 } },
          summary: 'A test project',
          category: 'community-empowerment',
          pillar: 'community-innovation',
          status: 'active',
          timeline: { startDate: '2024-01-01T00:00:00.000Z' },
          published: true,
          slug: 'test-project',
        },
      })
    })

    it('should return project by ID', async () => {
      const result = await payloadInstance.findByID({
        collection: 'projects',
        id: testProject.id,
      })

      expect(result).toBeDefined()
      expect(result.id).toBe(testProject.id)
      expect(result.title).toBe(testProject.title)
    })

    it('should return 404 for non-existent project', async () => {
      await expect(
        payloadInstance.findByID({
          collection: 'projects',
          id: '507f1f77bcf86cd799439011', // Valid ObjectId that doesn't exist
        })
      ).rejects.toThrow()
    })
  })

  describe('PUT /api/projects/:id', () => {
    let testProject: any

    beforeEach(async () => {
      testProject = await payloadInstance.create({
        collection: 'projects',
        data: {
          title: 'Original Title',
          description: { root: { children: [], direction: 'ltr', format: '', indent: 0, type: 'root', version: 1 } },
          summary: 'Original summary',
          category: 'community-empowerment',
          pillar: 'community-innovation',
          status: 'active',
          timeline: { startDate: '2024-01-01T00:00:00.000Z' },
          published: true,
          slug: 'original-project',
        },
      })
    })

    it('should update project with valid data', async () => {
      const updateData = {
        title: 'Updated Title',
        summary: 'Updated summary',
        status: 'completed',
      }

      const result = await payloadInstance.update({
        collection: 'projects',
        id: testProject.id,
        data: updateData,
      })

      expect(result.title).toBe(updateData.title)
      expect(result.summary).toBe(updateData.summary)
      expect(result.status).toBe(updateData.status)
      expect(result.category).toBe(testProject.category) // Unchanged
    })

    it('should fail to update with invalid data', async () => {
      const invalidUpdateData = {
        category: 'invalid-category',
      }

      await expect(
        payloadInstance.update({
          collection: 'projects',
          id: testProject.id,
          data: invalidUpdateData,
        })
      ).rejects.toThrow()
    })
  })

  describe('DELETE /api/projects/:id', () => {
    let testProject: any

    beforeEach(async () => {
      testProject = await payloadInstance.create({
        collection: 'projects',
        data: {
          title: 'Project to Delete',
          description: { root: { children: [], direction: 'ltr', format: '', indent: 0, type: 'root', version: 1 } },
          summary: 'This project will be deleted',
          category: 'community-empowerment',
          pillar: 'community-innovation',
          status: 'active',
          timeline: { startDate: '2024-01-01T00:00:00.000Z' },
          published: true,
          slug: 'project-to-delete',
        },
      })
    })

    it('should delete project successfully', async () => {
      await payloadInstance.delete({
        collection: 'projects',
        id: testProject.id,
      })

      await expect(
        payloadInstance.findByID({
          collection: 'projects',
          id: testProject.id,
        })
      ).rejects.toThrow()
    })

    it('should fail to delete non-existent project', async () => {
      await expect(
        payloadInstance.delete({
          collection: 'projects',
          id: '507f1f77bcf86cd799439011', // Valid ObjectId that doesn't exist
        })
      ).rejects.toThrow()
    })
  })
})
