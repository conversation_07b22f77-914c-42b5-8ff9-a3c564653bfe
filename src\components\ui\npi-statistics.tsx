import * as React from 'react'
import { cn } from '@/utilities/ui'

interface StatisticItem {
  value: string | number
  label: string
  description?: string
  icon?: React.ReactNode
}

interface NPIStatisticsProps extends React.HTMLAttributes<HTMLDivElement> {
  statistics: StatisticItem[]
  columns?: 2 | 3 | 4
}

const NPIStatistics = React.forwardRef<HTMLDivElement, NPIStatisticsProps>(
  ({ className, statistics, columns = 4, ...props }, ref) => {
    const gridClasses = {
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-3',
      4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    }

    return (
      <div ref={ref} className={cn('grid gap-8', gridClasses[columns], className)} {...props}>
        {statistics.map((stat, index) => (
          <NPIStatisticCard key={index} {...stat} />
        ))}
      </div>
    )
  },
)
NPIStatistics.displayName = 'NPIStatistics'

interface NPIStatisticCardProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string | number
  label: string
  description?: string
  icon?: React.ReactNode
}

const NPIStatisticCard = React.forwardRef<HTMLDivElement, NPIStatisticCardProps>(
  ({ className, value, label, description, icon, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        'text-center p-6 bg-card border border-border hover:shadow-md transition-shadow duration-200',
        className,
      )}
      {...props}
    >
      {icon && <div className="flex justify-center mb-4 text-primary">{icon}</div>}
      <div className="text-3xl lg:text-4xl font-bold text-primary mb-2 font-npi">{value}</div>
      <div className="text-lg font-semibold text-foreground mb-1 font-npi">{label}</div>
      {description && <div className="text-sm text-muted-foreground font-npi">{description}</div>}
    </div>
  ),
)
NPIStatisticCard.displayName = 'NPIStatisticCard'

export { NPIStatistics, NPIStatisticCard }
