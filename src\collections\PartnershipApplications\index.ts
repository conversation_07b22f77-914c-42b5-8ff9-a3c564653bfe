import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'

export const PartnershipApplications: CollectionConfig = {
  slug: 'partnership-applications',
  access: {
    create: anyone, // Allow public submissions
    delete: authenticated,
    read: authenticated, // Only authenticated users can read submissions
    update: authenticated,
  },
  admin: {
    useAsTitle: 'organizationName',
    defaultColumns: ['organizationName', 'contactName', 'partnershipModel', 'status', 'createdAt'],
    group: 'Submissions',
  },
  labels: {
    singular: 'Partnership Application',
    plural: 'Partnership Applications',
  },
  fields: [
    // Organization Details
    {
      name: 'organizationName',
      type: 'text',
      required: true,
      admin: {
        description: 'Name of the organization applying for partnership',
      },
    },
    {
      name: 'organizationType',
      type: 'select',
      required: true,
      options: [
        { label: 'Private Company', value: 'private-company' },
        { label: 'NGO/Non-Profit', value: 'ngo-nonprofit' },
        { label: 'Government Agency', value: 'government-agency' },
        { label: 'Academic Institution', value: 'academic-institution' },
        { label: 'International Organization', value: 'international-organization' },
        { label: 'Foundation', value: 'foundation' },
        { label: 'Cooperative', value: 'cooperative' },
        { label: 'Other', value: 'other' },
      ],
      admin: {
        description: 'Type of organization',
      },
    },
    {
      name: 'website',
      type: 'text',
      admin: {
        description: 'Organization website URL',
      },
    },
    {
      name: 'establishedYear',
      type: 'text',
      admin: {
        description: 'Year the organization was established',
      },
    },

    // Contact Information
    {
      name: 'contactName',
      type: 'text',
      required: true,
      admin: {
        description: 'Name of the primary contact person',
      },
    },
    {
      name: 'contactTitle',
      type: 'text',
      admin: {
        description: 'Title/position of the contact person',
      },
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      admin: {
        description: 'Contact email address',
      },
    },
    {
      name: 'phone',
      type: 'text',
      admin: {
        description: 'Contact phone number',
      },
    },

    // Partnership Details
    {
      name: 'partnershipModel',
      type: 'select',
      required: true,
      options: [
        { label: 'Strategic Partnership', value: 'strategic-partnership' },
        { label: 'Investment Partnership', value: 'investment-partnership' },
        { label: 'Technical Partnership', value: 'technical-partnership' },
        { label: 'Community Partnership', value: 'community-partnership' },
        { label: 'Research Partnership', value: 'research-partnership' },
      ],
      admin: {
        description: 'Type of partnership being sought',
      },
    },
    {
      name: 'investmentCapacity',
      type: 'select',
      options: [
        { label: 'Under KES 1M', value: 'under-1m' },
        { label: 'KES 1M - 5M', value: '1m-5m' },
        { label: 'KES 5M - 20M', value: '5m-20m' },
        { label: 'KES 20M - 50M', value: '20m-50m' },
        { label: 'KES 50M - 100M', value: '50m-100m' },
        { label: 'Over KES 100M', value: 'over-100m' },
      ],
      admin: {
        description: 'Investment capacity range',
      },
    },
    {
      name: 'projectInterest',
      type: 'textarea',
      admin: {
        description: 'Areas of project interest or specific projects',
      },
    },
    {
      name: 'timeline',
      type: 'select',
      options: [
        { label: 'Immediate (0-3 months)', value: 'immediate' },
        { label: 'Short-term (3-6 months)', value: 'short-term' },
        { label: 'Medium-term (6-12 months)', value: 'medium-term' },
        { label: 'Long-term (1-2 years)', value: 'long-term' },
        { label: 'Strategic (2+ years)', value: 'strategic' },
      ],
      admin: {
        description: 'Expected timeline for partnership',
      },
    },

    // Additional Information
    {
      name: 'experience',
      type: 'textarea',
      admin: {
        description: 'Relevant experience and background',
      },
    },
    {
      name: 'objectives',
      type: 'textarea',
      admin: {
        description: 'Partnership objectives and goals',
      },
    },
    {
      name: 'additionalInfo',
      type: 'textarea',
      admin: {
        description: 'Any additional information',
      },
    },

    // Application Management
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'new',
      options: [
        { label: 'New', value: 'new' },
        { label: 'Under Review', value: 'under-review' },
        { label: 'In Discussion', value: 'in-discussion' },
        { label: 'Approved', value: 'approved' },
        { label: 'Rejected', value: 'rejected' },
        { label: 'On Hold', value: 'on-hold' },
      ],
      admin: {
        description: 'Application status',
      },
    },
    {
      name: 'priority',
      type: 'select',
      defaultValue: 'medium',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Urgent', value: 'urgent' },
      ],
      admin: {
        description: 'Application priority level',
      },
    },
    {
      name: 'notes',
      type: 'textarea',
      admin: {
        description: 'Internal notes and comments',
      },
    },
    {
      name: 'assignedTo',
      type: 'text',
      admin: {
        description: 'Staff member assigned to handle this application',
      },
    },

    // Metadata
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'source',
          type: 'text',
          defaultValue: 'website-form',
          admin: {
            description: 'Source of the application',
          },
        },
        {
          name: 'ipAddress',
          type: 'text',
          admin: {
            description: 'IP address of the applicant',
          },
        },
        {
          name: 'userAgent',
          type: 'text',
          admin: {
            description: 'Browser user agent',
          },
        },
        {
          name: 'referrer',
          type: 'text',
          admin: {
            description: 'Referrer URL',
          },
        },
      ],
      admin: {
        description: 'Application metadata',
      },
    },
    {
      name: 'archived',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Archive this application',
      },
    },
  ],
}

export default PartnershipApplications
