import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'
import { revalidateProjects } from './hooks/revalidateProjects'
import { validateProjectData } from './hooks/validateProjectData'
import { generateProjectId } from './hooks/generateProjectId'
import { directImageUploadField, enhancedImageField, heroImageField } from '../../fields/imageUpload'

export const Projects: CollectionConfig = {
  slug: 'projects',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'status', 'location', 'updatedAt'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Project',
    plural: 'Projects',
  },
  fields: [
    {
      name: 'projectId',
      type: 'text',
      admin: {
        readOnly: true,
        description: 'Auto-generated unique project identifier',
        position: 'sidebar',
      },
      access: {
        create: () => false, // Prevent manual creation
        update: () => false, // Prevent manual updates
      },
    },
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The project title that will be displayed publicly',
      },
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      admin: {
        description: 'Detailed project description with rich text formatting',
      },
      validate: (value) => {
        if (!value) return 'Description is required'

        // Validate Lexical JSON structure
        try {
          if (typeof value === 'string') {
            JSON.parse(value)
          } else if (typeof value === 'object') {
            JSON.stringify(value)
          }
          return true
        } catch (error) {
          return 'Invalid description format. Please check your content.'
        }
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief project summary for cards and previews (max 300 characters)',
      },
    },
    enhancedImageField({
      name: 'image',
      label: 'Main Project Image',
      required: false,
      admin: {
        description: 'Upload the main project image with alt text and caption',
      },
    }),
    {
      name: 'gallery',
      type: 'array',
      fields: [
        directImageUploadField({
          name: 'image',
          label: 'Gallery Image',
          required: true,
          admin: {
            description: 'Upload a gallery image',
          },
        }),
        {
          name: 'caption',
          type: 'text',
          admin: {
            description: 'Image caption',
          },
        },
      ],
      admin: {
        description: 'Additional project images',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Knowledge Preservation', value: 'knowledge-preservation' },
        { label: 'Community Empowerment', value: 'community-empowerment' },
        { label: 'Capacity Building', value: 'capacity-building' },
        { label: 'Research & Development', value: 'research-development' },
        { label: 'Policy & Advocacy', value: 'policy-advocacy' },
        { label: 'Market Development', value: 'market-development' },
        { label: 'Technology Innovation', value: 'technology-innovation' },
      ],
    },
    {
      name: 'pillar',
      type: 'select',
      required: true,
      options: [
        { label: 'Indigenous Knowledge Documentation', value: 'indigenous-knowledge' },
        { label: 'Community-Led Innovation', value: 'community-innovation' },
        { label: 'Capacity Building & Training', value: 'capacity-building' },
        { label: 'Market Access & Development', value: 'market-development' },
        { label: 'Policy & Regulatory Framework', value: 'policy-framework' },
      ],
      admin: {
        description: 'Strategic pillar this project aligns with',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'active',
      options: [
        { label: 'Planning', value: 'planning' },
        { label: 'Active', value: 'active' },
        { label: 'Completed', value: 'completed' },
        { label: 'On Hold', value: 'on-hold' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'counties',
          type: 'relationship',
          relationTo: 'counties',
          hasMany: true,
          admin: {
            description: 'Counties where this project is implemented',
          },
        },
        {
          name: 'specificLocation',
          type: 'text',
          dbName: 'specific_loc',
          admin: {
            description: 'Specific location details (e.g., "Baringo County, Marigat Sub-County")',
          },
        },
        {
          name: 'coordinates',
          type: 'group',
          fields: [
            {
              name: 'latitude',
              type: 'number',
              validate: (value) => {
                if (value !== undefined && value !== null && value !== '') {
                  const num = Number(value)
                  if (isNaN(num) || num < -90 || num > 90) {
                    return 'Please enter a valid latitude between -90 and 90'
                  }
                }
                return true
              },
            },
            {
              name: 'longitude',
              type: 'number',
              validate: (value) => {
                if (value !== undefined && value !== null && value !== '') {
                  const num = Number(value)
                  if (isNaN(num) || num < -180 || num > 180) {
                    return 'Please enter a valid longitude between -180 and 180'
                  }
                }
                return true
              },
            },
          ],
          admin: {
            description: 'GPS coordinates for mapping (optional)',
          },
        },
      ],
    },
    {
      name: 'timeline',
      type: 'group',
      fields: [
        {
          name: 'startDate',
          type: 'date',
          required: true,
          validate: (value) => {
            if (!value) return 'Start date is required'

            try {
              const date = new Date(value)
              if (isNaN(date.getTime())) {
                return 'Please enter a valid date'
              }
              return true
            } catch {
              return 'Please enter a valid date format'
            }
          },
        },
        {
          name: 'endDate',
          type: 'date',
        },
        {
          name: 'duration',
          type: 'text',
          admin: {
            description: 'Human-readable duration (e.g., "2022-2025", "18 months")',
          },
        },
        {
          name: 'milestones',
          type: 'array',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
            },
            {
              name: 'targetDate',
              type: 'date',
            },
            {
              name: 'completed',
              type: 'checkbox',
              defaultValue: false,
            },
          ],
        },
      ],
    },
    {
      name: 'budget',
      type: 'group',
      fields: [
        {
          name: 'totalBudget',
          type: 'number',
          admin: {
            description: 'Total project budget in KES',
          },
          validate: (value) => {
            if (value !== undefined && value !== null && value !== '') {
              const num = Number(value)
              if (isNaN(num) || num < 0) {
                return 'Please enter a valid positive number'
              }
            }
            return true
          },
        },
        {
          name: 'currency',
          type: 'select',
          defaultValue: 'KES',
          options: [
            { label: 'KES', value: 'KES' },
            { label: 'USD', value: 'USD' },
            { label: 'EUR', value: 'EUR' },
          ],
        },
        {
          name: 'fundingSources',
          type: 'array',
          fields: [
            {
              name: 'source',
              type: 'text',
              required: true,
            },
            {
              name: 'amount',
              type: 'number',
              validate: (value) => {
                if (value !== undefined && value !== null && value !== '') {
                  const num = Number(value)
                  if (isNaN(num) || num < 0) {
                    return 'Please enter a valid positive number'
                  }
                }
                return true
              },
            },
            {
              name: 'percentage',
              type: 'number',
              min: 0,
              max: 100,
              validate: (value) => {
                if (value !== undefined && value !== null && value !== '') {
                  const num = Number(value)
                  if (isNaN(num) || num < 0 || num > 100) {
                    return 'Please enter a percentage between 0 and 100'
                  }
                }
                return true
              },
            },
          ],
        },
      ],
    },
    {
      name: 'impact',
      type: 'group',
      fields: [
        {
          name: 'beneficiaries',
          type: 'number',
          admin: {
            description: 'Number of direct beneficiaries',
          },
          validate: (value) => {
            if (value !== undefined && value !== null && value !== '') {
              const num = Number(value)
              if (isNaN(num) || num < 0 || !Number.isInteger(num)) {
                return 'Please enter a valid positive whole number'
              }
            }
            return true
          },
        },
        {
          name: 'communities',
          type: 'number',
          admin: {
            description: 'Number of communities reached',
          },
          validate: (value) => {
            if (value !== undefined && value !== null && value !== '') {
              const num = Number(value)
              if (isNaN(num) || num < 0 || !Number.isInteger(num)) {
                return 'Please enter a valid positive whole number'
              }
            }
            return true
          },
        },
        {
          name: 'jobsCreated',
          type: 'number',
          validate: (value) => {
            if (value !== undefined && value !== null && value !== '') {
              const num = Number(value)
              if (isNaN(num) || num < 0 || !Number.isInteger(num)) {
                return 'Please enter a valid positive whole number'
              }
            }
            return true
          },
        },
        {
          name: 'metrics',
          type: 'array',
          fields: [
            {
              name: 'metric',
              type: 'text',
              required: true,
            },
            {
              name: 'value',
              type: 'text',
              required: true,
            },
            {
              name: 'unit',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'team',
      type: 'group',
      fields: [
        {
          name: 'projectManager',
          type: 'text',
        },
        {
          name: 'implementingPartners',
          type: 'array',
          dbName: 'impl_partners',
          fields: [
            {
              name: 'partner',
              type: 'relationship',
              relationTo: 'partners',
            },
            {
              name: 'role',
              type: 'text',
            },
          ],
        },
        {
          name: 'keyPersonnel',
          type: 'array',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'organization',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'resources',
      type: 'group',
      fields: [
        {
          name: 'documents',
          type: 'array',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'file',
              type: 'upload',
              relationTo: 'media',
              required: true,
            },
            {
              name: 'type',
              type: 'select',
              options: [
                { label: 'Project Proposal', value: 'proposal' },
                { label: 'Progress Report', value: 'progress-report' },
                { label: 'Final Report', value: 'final-report' },
                { label: 'Research Paper', value: 'research-paper' },
                { label: 'Policy Brief', value: 'policy-brief' },
                { label: 'Training Material', value: 'training-material' },
                { label: 'Other', value: 'other' },
              ],
            },
          ],
        },
        {
          name: 'links',
          type: 'array',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
            },
            {
              name: 'url',
              type: 'text',
              required: true,
              validate: (value) => {
                if (!value) return 'URL is required'

                try {
                  new URL(value)
                  return true
                } catch {
                  return 'Please enter a valid URL (e.g., https://example.com)'
                }
              },
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this project on homepage and key sections',
      },
    },
    {
      name: 'published',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        description: 'Make this project visible to the public',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    ...slugField(),
  ],
  hooks: {
    beforeChange: [generateProjectId],
    beforeValidate: [validateProjectData],
    afterChange: [revalidateProjects],
  },
}

export default Projects
