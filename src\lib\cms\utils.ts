import type { CMSMedia, CMSProject, CMSSuccessStory, CMSResource, CMSNews } from './types'

// Media utilities
export function getMediaUrl(media: CMSMedia | undefined, size?: string): string {
  if (!media) return ''
  
  if (size && media.url) {
    // If using Vercel Blob or similar service with size parameters
    const url = new URL(media.url)
    if (size !== 'original') {
      url.searchParams.set('size', size)
    }
    return url.toString()
  }
  
  return media.url || ''
}

export function getOptimizedImageUrl(
  media: CMSMedia | undefined,
  options: {
    width?: number
    height?: number
    quality?: number
    format?: 'webp' | 'jpeg' | 'png'
  } = {}
): string {
  if (!media) return ''
  
  const url = new URL(media.url || '')
  
  if (options.width) url.searchParams.set('w', options.width.toString())
  if (options.height) url.searchParams.set('h', options.height.toString())
  if (options.quality) url.searchParams.set('q', options.quality.toString())
  if (options.format) url.searchParams.set('f', options.format)
  
  return url.toString()
}

export function getImageSrcSet(media: CMSMedia | undefined): string {
  if (!media) return ''
  
  const sizes = [400, 600, 800, 1200, 1600]
  const srcSet = sizes
    .map(size => `${getOptimizedImageUrl(media, { width: size })} ${size}w`)
    .join(', ')
  
  return srcSet
}

export function formatFileSize(bytes: number | undefined): string {
  if (!bytes) return ''
  
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`
}

// Date utilities
export function formatDate(dateString: string, options: Intl.DateTimeFormatOptions = {}): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }
  
  return new Date(dateString).toLocaleDateString('en-US', { ...defaultOptions, ...options })
}

export function formatRelativeDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMs = now.getTime() - date.getTime()
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))
  
  if (diffInDays === 0) return 'Today'
  if (diffInDays === 1) return 'Yesterday'
  if (diffInDays < 7) return `${diffInDays} days ago`
  if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`
  if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`
  
  return `${Math.floor(diffInDays / 365)} years ago`
}

export function isDateInFuture(dateString: string): boolean {
  return new Date(dateString) > new Date()
}

export function isDateInPast(dateString: string): boolean {
  return new Date(dateString) < new Date()
}

// Currency utilities
export function formatCurrency(
  amount: number | undefined,
  currency: string = 'KES',
  options: Intl.NumberFormatOptions = {}
): string {
  if (amount === undefined) return ''
  
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }
  
  // Use appropriate locale based on currency
  const locale = currency === 'KES' ? 'en-KE' : 'en-US'
  
  return new Intl.NumberFormat(locale, { ...defaultOptions, ...options }).format(amount)
}

export function formatNumber(num: number | undefined, options: Intl.NumberFormatOptions = {}): string {
  if (num === undefined) return ''
  
  return new Intl.NumberFormat('en-US', options).format(num)
}

// Text utilities
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength - suffix.length) + suffix
}

export function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '')
}

export function extractExcerpt(content: string, maxLength: number = 200): string {
  const plainText = stripHtml(content)
  return truncateText(plainText, maxLength)
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

// Content utilities
export function getReadingTime(content: string): number {
  const wordsPerMinute = 200
  const words = stripHtml(content).split(/\s+/).length
  return Math.ceil(words / wordsPerMinute)
}

export function highlightSearchTerms(text: string, searchTerm: string): string {
  if (!searchTerm) return text
  
  const regex = new RegExp(`(${searchTerm})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// Status utilities
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    // Project statuses
    planning: 'bg-yellow-100 text-yellow-800',
    active: 'bg-green-100 text-green-800',
    completed: 'bg-blue-100 text-blue-800',
    'on-hold': 'bg-gray-100 text-gray-800',
    cancelled: 'bg-red-100 text-red-800',
    
    // News statuses
    draft: 'bg-gray-100 text-gray-800',
    review: 'bg-yellow-100 text-yellow-800',
    published: 'bg-green-100 text-green-800',
    archived: 'bg-gray-100 text-gray-800',
    
    // Investment opportunity statuses
    open: 'bg-green-100 text-green-800',
    'under-review': 'bg-yellow-100 text-yellow-800',
    funded: 'bg-blue-100 text-blue-800',
    closed: 'bg-gray-100 text-gray-800',
    
    // Contact submission statuses
    new: 'bg-blue-100 text-blue-800',
    'in-progress': 'bg-yellow-100 text-yellow-800',
    'pending-response': 'bg-orange-100 text-orange-800',
    resolved: 'bg-green-100 text-green-800',
    escalated: 'bg-red-100 text-red-800',
  }
  
  return statusColors[status] || 'bg-gray-100 text-gray-800'
}

export function getPriorityColor(priority: string): string {
  const priorityColors: Record<string, string> = {
    low: 'bg-gray-100 text-gray-800',
    medium: 'bg-yellow-100 text-yellow-800',
    high: 'bg-orange-100 text-orange-800',
    urgent: 'bg-red-100 text-red-800',
  }
  
  return priorityColors[priority] || 'bg-gray-100 text-gray-800'
}

// Category utilities
export function getCategoryIcon(category: string): string {
  const categoryIcons: Record<string, string> = {
    // Project categories
    'knowledge-preservation': '📚',
    'community-empowerment': '🤝',
    'capacity-building': '🎓',
    'research-development': '🔬',
    'policy-advocacy': '📋',
    'market-development': '📈',
    'technology-innovation': '💡',
    
    // Success story categories
    'community-innovation': '🚀',
    'economic-empowerment': '💰',
    'market-access': '🛒',
    'technology-adoption': '📱',
    'policy-impact': '⚖️',
    
    // Resource categories
    'indigenous-knowledge': '🌿',
    'community-development': '🏘️',
    'sustainability': '♻️',
    'finance-investment': '💼',
    
    // News categories
    news: '📰',
    'press-release': '📢',
    'blog-post': '✍️',
    'event-update': '📅',
    'project-update': '🔄',
    'policy-update': '📜',
    'partnership-announcement': '🤝',
    'research-update': '🔍',
    
    // Media categories
    events: '🎉',
    projects: '🏗️',
    'community-stories': '👥',
    'training-workshops': '🎓',
    partnerships: '🤝',
    'research-innovation': '🔬',
    'cultural-heritage': '🏛️',
    'natural-products': '🌱',
    'leadership-governance': '👑',
    'marketing-promotion': '📣',
  }
  
  return categoryIcons[category] || '📄'
}

// Validation utilities
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Search utilities
export function createSearchParams(params: Record<string, any>): URLSearchParams {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(v => searchParams.append(key, String(v)))
      } else {
        searchParams.set(key, String(value))
      }
    }
  })
  
  return searchParams
}

export function parseSearchParams(searchParams: URLSearchParams): Record<string, any> {
  const params: Record<string, any> = {}
  
  for (const [key, value] of searchParams.entries()) {
    if (params[key]) {
      if (Array.isArray(params[key])) {
        params[key].push(value)
      } else {
        params[key] = [params[key], value]
      }
    } else {
      params[key] = value
    }
  }
  
  return params
}

// Content filtering utilities
export function filterBySearchTerm<T extends { title: string; summary?: string; tags?: string[] }>(
  items: T[],
  searchTerm: string
): T[] {
  if (!searchTerm) return items
  
  const term = searchTerm.toLowerCase()
  
  return items.filter(item => 
    item.title.toLowerCase().includes(term) ||
    item.summary?.toLowerCase().includes(term) ||
    item.tags?.some(tag => tag.toLowerCase().includes(term))
  )
}

export function sortByDate<T extends { createdAt: string }>(
  items: T[],
  order: 'asc' | 'desc' = 'desc'
): T[] {
  return [...items].sort((a, b) => {
    const dateA = new Date(a.createdAt).getTime()
    const dateB = new Date(b.createdAt).getTime()
    return order === 'desc' ? dateB - dateA : dateA - dateB
  })
}

export function groupByCategory<T extends { category: string }>(items: T[]): Record<string, T[]> {
  return items.reduce((groups, item) => {
    const category = item.category
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(item)
    return groups
  }, {} as Record<string, T[]>)
}
