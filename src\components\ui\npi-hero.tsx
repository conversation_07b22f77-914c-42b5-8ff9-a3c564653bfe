'use client'

import * as React from 'react'
import { cn } from '@/utilities/ui'
import { NPIImage } from './npi-image'

interface NPIHeroProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'default' | 'gradient' | 'image' | 'video'
  backgroundImage?: string
  backgroundVideo?: string
  overlay?: boolean
  overlayOpacity?: number
  parallax?: boolean
}

const NPIHero = React.forwardRef<HTMLElement, NPIHeroProps>(
  (
    {
      className,
      variant = 'default',
      backgroundImage,
      backgroundVideo,
      overlay = true,
      overlayOpacity = 0.5,
      parallax = false,
      children,
      ...props
    },
    ref,
  ) => {
    const [scrollY, setScrollY] = React.useState(0)

    React.useEffect(() => {
      if (!parallax) return

      const handleScroll = () => setScrollY(window.scrollY)
      window.addEventListener('scroll', handleScroll)
      return () => window.removeEventListener('scroll', handleScroll)
    }, [parallax])

    const variantClasses = {
      default:
        'bg-gradient-to-br from-primary via-primary/90 to-secondary text-primary-foreground dark:text-white',
      gradient: 'bg-gradient-to-br from-primary via-secondary to-accent text-white',
      image: 'relative text-white overflow-hidden',
      video: 'relative text-white overflow-hidden',
    }

    const parallaxTransform = parallax ? `translateY(${scrollY * 0.5}px)` : undefined

    return (
      <section
        ref={ref}
        className={cn(
          'relative min-h-screen flex items-center justify-center',
          variantClasses[variant],
          className,
        )}
        {...props}
      >
        {/* Background Image */}
        {variant === 'image' && backgroundImage && (
          <div className="absolute inset-0 w-full h-full" style={{ transform: parallaxTransform }}>
            <NPIImage
              src={backgroundImage}
              alt="Hero background"
              fill
              priority
              objectFit="cover"
              className="w-full h-full"
              sizes="100vw"
              loading="eager"
            />
          </div>
        )}

        {/* Background Video */}
        {variant === 'video' && backgroundVideo && (
          <div className="absolute inset-0 w-full h-full">
            <video autoPlay muted loop playsInline className="w-full h-full object-cover">
              <source src={backgroundVideo} type="video/mp4" />
            </video>
          </div>
        )}

        {/* Overlay */}
        {(variant === 'image' || variant === 'video') && overlay && (
          <div
            className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/50 to-black/70"
            style={{ opacity: overlayOpacity }}
          />
        )}

        {/* Animated background elements for gradient variants */}
        {(variant === 'default' || variant === 'gradient') && (
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full animate-pulse" />
            <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-white/5 rounded-full animate-pulse delay-1000" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/3 rounded-full animate-ping" />
          </div>
        )}

        {/* Content */}
        <div className="relative z-10 npi-container text-center">
          <div className="animate-fade-in-up">{children}</div>
        </div>
      </section>
    )
  },
)
NPIHero.displayName = 'NPIHero'

const NPIHeroTitle = React.forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h1
      ref={ref}
      className={cn(
        'npi-heading-1 mb-4 font-npi animate-fade-in-up animation-delay-200',
        'bg-gradient-to-r from-white via-white to-white/90 bg-clip-text text-transparent',
        'dark:from-white dark:via-white dark:to-white/90 dark:bg-clip-text dark:text-transparent',
        'drop-shadow-lg dark:drop-shadow-2xl',
        className,
      )}
      {...props}
    />
  ),
)
NPIHeroTitle.displayName = 'NPIHeroTitle'

const NPIHeroSubtitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn(
      'text-lg lg:text-xl mb-6 max-w-4xl mx-auto opacity-90 font-npi text-white dark:text-white',
      className,
    )}
    {...props}
  />
))
NPIHeroSubtitle.displayName = 'NPIHeroSubtitle'

const NPIHeroActions = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex flex-col sm:flex-row gap-4 justify-center items-center', className)}
      {...props}
    />
  ),
)
NPIHeroActions.displayName = 'NPIHeroActions'

const NPIHeroOrganization = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        'mb-6 text-lg lg:text-xl opacity-80 font-npi text-white dark:text-white',
        className,
      )}
      {...props}
    />
  ),
)
NPIHeroOrganization.displayName = 'NPIHeroOrganization'

export { NPIHero, NPIHeroTitle, NPIHeroSubtitle, NPIHeroActions, NPIHeroOrganization }
