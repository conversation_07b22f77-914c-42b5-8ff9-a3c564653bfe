import path from 'path'
import payload, { Payload } from 'payload'
import { MongoMemoryServer } from 'mongodb-memory-server'

interface InitPayloadTestOptions {
  __dirname: string
  mongoURL?: string
  secret?: string
}

let cached: {
  payload?: Payload
  promise?: Promise<Payload>
} = {}

export const initPayloadTest = async (options: InitPayloadTestOptions): Promise<Payload> => {
  const { __dirname: dirname, mongoURL, secret = 'test-secret' } = options

  if (cached.payload) {
    return cached.payload
  }

  if (!cached.promise) {
    cached.promise = initPayload()
  }

  try {
    cached.payload = await cached.promise
    return cached.payload
  } catch (error) {
    cached.promise = undefined
    throw error
  }

  async function initPayload(): Promise<Payload> {
    // Initialize Payload
    await payload.init({
      secret,
      mongoURL: mongoURL || process.env.DATABASE_URI || 'mongodb://localhost:27017/payload-test',
      local: true,
      onInit: () => {
        payload.logger.info('Payload Test initialized')
      },
    })

    return payload
  }
}

export const clearCollections = async (payload: Payload, collections: string[]) => {
  for (const collection of collections) {
    await payload.delete({
      collection,
      where: {},
    })
  }
}

export const createTestUser = async (payload: Payload, userData: any = {}) => {
  const defaultUserData = {
    name: 'Test User',
    email: '<EMAIL>',
    password: 'test123',
    role: 'admin',
    ...userData,
  }

  return await payload.create({
    collection: 'users',
    data: defaultUserData,
  })
}

export const createTestProject = async (payload: Payload, projectData: any = {}) => {
  const defaultProjectData = {
    title: 'Test Project',
    description: {
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: 'This is a test project description.',
                type: 'text',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1,
      },
    },
    summary: 'A test project for unit testing',
    category: 'community-empowerment',
    pillar: 'community-innovation',
    status: 'active',
    timeline: {
      startDate: '2024-01-01T00:00:00.000Z',
    },
    featured: false,
    published: true,
    slug: 'test-project',
    ...projectData,
  }

  return await payload.create({
    collection: 'projects',
    data: defaultProjectData,
  })
}

export const createTestSuccessStory = async (payload: Payload, storyData: any = {}) => {
  const defaultStoryData = {
    title: 'Test Success Story',
    summary: 'A test success story',
    content: {
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: 'This is a test success story content.',
                type: 'text',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1,
      },
    },
    category: 'community-innovation',
    participants: {
      beneficiary: {
        name: 'Test Beneficiary',
        role: 'Community Member',
      },
    },
    timeline: {
      startDate: '2023-01-01T00:00:00.000Z',
      completionDate: '2023-12-31T00:00:00.000Z',
    },
    investment: {
      totalAmount: 100000,
      currency: 'KES',
    },
    featured: false,
    published: true,
    slug: 'test-success-story',
    ...storyData,
  }

  return await payload.create({
    collection: 'success-stories',
    data: defaultStoryData,
  })
}

export const createTestResource = async (payload: Payload, resourceData: any = {}) => {
  const defaultResourceData = {
    title: 'Test Resource',
    description: {
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: 'This is a test resource description.',
                type: 'text',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1,
      },
    },
    summary: 'A test resource',
    type: 'research-report',
    category: 'indigenous-knowledge',
    metadata: {
      publishDate: '2024-01-01T00:00:00.000Z',
      version: '1.0',
      language: 'en',
    },
    access: {
      level: 'public',
      requiresRegistration: false,
    },
    analytics: {
      downloadCount: 0,
      viewCount: 0,
    },
    featured: false,
    published: true,
    slug: 'test-resource',
    ...resourceData,
  }

  return await payload.create({
    collection: 'resources',
    data: defaultResourceData,
  })
}

export const createTestNews = async (payload: Payload, newsData: any = {}) => {
  const defaultNewsData = {
    title: 'Test News Article',
    summary: 'A test news article',
    content: {
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text: 'This is a test news article content.',
                type: 'text',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1,
      },
    },
    category: 'news',
    status: 'published',
    publishDate: '2024-01-01T00:00:00.000Z',
    author: {
      name: 'Test Author',
      role: 'Writer',
      organization: 'NPI',
    },
    engagement: {
      allowComments: true,
      socialSharing: true,
      newsletter: false,
    },
    analytics: {
      viewCount: 0,
      shareCount: 0,
    },
    featured: false,
    urgent: false,
    slug: 'test-news-article',
    ...newsData,
  }

  return await payload.create({
    collection: 'news',
    data: defaultNewsData,
  })
}

export const createTestContactSubmission = async (payload: Payload, submissionData: any = {}) => {
  const defaultSubmissionData = {
    name: 'Test User',
    email: '<EMAIL>',
    subject: 'Test Inquiry',
    category: 'general',
    priority: 'medium',
    message: 'This is a test contact submission.',
    status: 'new',
    metadata: {
      source: 'website-form',
    },
    archived: false,
    ...submissionData,
  }

  return await payload.create({
    collection: 'contact-submissions',
    data: defaultSubmissionData,
  })
}

export const loginTestUser = async (payload: Payload, email: string = '<EMAIL>', password: string = 'test123') => {
  const result = await payload.login({
    collection: 'users',
    data: {
      email,
      password,
    },
  })

  return result
}

export const generateTestJWT = async (payload: Payload, user: any) => {
  return payload.jwt.sign({
    id: user.id,
    email: user.email,
    collection: 'users',
  })
}

// Mock request object for testing
export const createMockRequest = (overrides: any = {}) => {
  return {
    user: null,
    payload: null,
    query: {},
    body: {},
    headers: {},
    ip: '127.0.0.1',
    url: '/test',
    method: 'GET',
    ...overrides,
  }
}

// Mock response object for testing
export const createMockResponse = () => {
  const res: any = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
  }
  return res
}

// Test data generators
export const generateRandomString = (length: number = 10): string => {
  return Math.random().toString(36).substring(2, length + 2)
}

export const generateRandomEmail = (): string => {
  return `test-${generateRandomString(8)}@example.com`
}

export const generateRandomSlug = (): string => {
  return `test-${generateRandomString(8)}-${Date.now()}`
}

// Cleanup function for tests
export const cleanupPayloadTest = async (payload: Payload) => {
  const collections = [
    'projects',
    'success-stories',
    'resources',
    'news',
    'media-gallery',
    'partnerships',
    'investment-opportunities',
    'contact-submissions',
    'users',
  ]

  await clearCollections(payload, collections)
}
