// PayloadCMS API approach to create admin user
import { config as dotenvConfig } from 'dotenv'

// Load environment variables
dotenvConfig()

const SUPER_ADMIN_USER = {
  name: 'Super Administrator',
  email: '<EMAIL>',
  password: 'SuperAdmin123!',
  role: 'super-admin'
}

const BASE_URL = process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3000'

const createSuperAdminAPI = async () => {
  try {
    console.log('🚀 Creating Super Admin User (PayloadCMS API)...')
    console.log('====================================')

    // Check if user already exists by trying to login
    console.log('🔍 Checking if user already exists...')
    const loginResponse = await fetch(`${BASE_URL}/api/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: SUPER_ADMIN_USER.email,
        password: SUPER_ADMIN_USER.password,
      }),
    })

    if (loginResponse.ok) {
      const loginResult = await loginResponse.json()
      console.log(`⚠️  User with email ${SUPER_ADMIN_USER.email} already exists and can login`)
      console.log('✅ Super admin user is already set up!')
      console.log(`📧 Email: ${loginResult.user.email}`)
      console.log(`👤 Name: ${loginResult.user.name}`)
      console.log(`🔑 Role: ${loginResult.user.role}`)
      console.log(`🆔 ID: ${loginResult.user.id}`)

      console.log('\n🎉 Super admin setup completed!')
      console.log(`You can now login at: ${BASE_URL}/admin`)
      console.log(`Email: ${SUPER_ADMIN_USER.email}`)
      console.log(`Password: ${SUPER_ADMIN_USER.password}`)
      return
    }

    // User doesn't exist or can't login, try to create
    console.log('👤 Creating new super admin user...')
    const createResponse = await fetch(`${BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: SUPER_ADMIN_USER.name,
        email: SUPER_ADMIN_USER.email,
        password: SUPER_ADMIN_USER.password,
        role: SUPER_ADMIN_USER.role,
        isActive: true,
        preferences: {
          newsletter: false,
          notifications: true,
          language: 'en'
        }
      }),
    })

    const createResult = await createResponse.json()

    if (createResponse.ok) {
      console.log('✅ Super admin user created successfully!')
      console.log(`📧 Email: ${createResult.doc.email}`)
      console.log(`👤 Name: ${createResult.doc.name}`)
      console.log(`🔑 Role: ${createResult.doc.role}`)
      console.log(`🆔 ID: ${createResult.doc.id}`)

      console.log('\n🎉 Super admin setup completed!')
      console.log(`You can now login at: ${BASE_URL}/admin`)
      console.log(`Email: ${SUPER_ADMIN_USER.email}`)
      console.log(`Password: ${SUPER_ADMIN_USER.password}`)
    } else {
      console.error('❌ Error creating super admin user:', createResult)

      // If creation failed, it might be because the user exists but with different credentials
      // Let's try to get user info to see what's wrong
      console.log('\n🔍 Attempting to get user information...')
      const getUserResponse = await fetch(`${BASE_URL}/api/users?where[email][equals]=${SUPER_ADMIN_USER.email}`)

      if (getUserResponse.ok) {
        const getUserResult = await getUserResponse.json()
        if (getUserResult.docs && getUserResult.docs.length > 0) {
          const existingUser = getUserResult.docs[0]
          console.log(`⚠️  User exists but creation failed. Existing user details:`)
          console.log(`📧 Email: ${existingUser.email}`)
          console.log(`👤 Name: ${existingUser.name}`)
          console.log(`🔑 Role: ${existingUser.role}`)
          console.log(`🆔 ID: ${existingUser.id}`)
          console.log('\n💡 Try logging in with existing credentials or reset the password.')
        }
      }
    }

  } catch (error) {
    console.error('❌ Error creating super admin user:', error.message)
    console.log('\n🔧 Troubleshooting tips:')
    console.log('1. Make sure your development server is running: npm run dev')
    console.log('2. Check your DATABASE_URI in .env file')
    console.log('3. Verify PAYLOAD_PUBLIC_SERVER_URL is correct')
    console.log(`4. Current server URL: ${BASE_URL}`)
  }
}

// Run the script
createSuperAdminAPI()
