'use client'

import React from 'react'
import { Dashboard } from './Dashboard'

export const BeforeDashboard: React.FC = () => {
  return (
    <div>
      {/* Enhanced Dashboard with API Testing */}
      <Dashboard />
      
      {/* Quick Actions Panel */}
      <div style={{ 
        margin: '2rem 0',
        padding: '1.5rem',
        backgroundColor: '#f9fafb',
        borderRadius: '0.5rem',
        border: '1px solid #e5e7eb',
      }}>
        <h3 style={{ 
          fontSize: '1.25rem', 
          fontWeight: '600', 
          marginBottom: '1rem',
          color: '#374151',
        }}>
          Quick Actions
        </h3>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem',
        }}>
          <a 
            href="/admin/collections/projects/create"
            className="crud-button crud-button--create"
            style={{ 
              textDecoration: 'none',
              textAlign: 'center',
              padding: '1rem',
            }}
          >
            📊 Create New Project
          </a>
          
          <a 
            href="/admin/collections/news/create"
            className="crud-button crud-button--create"
            style={{ 
              textDecoration: 'none',
              textAlign: 'center',
              padding: '1rem',
            }}
          >
            📰 Add News Article
          </a>
          
          <a 
            href="/admin/collections/success-stories/create"
            className="crud-button crud-button--create"
            style={{ 
              textDecoration: 'none',
              textAlign: 'center',
              padding: '1rem',
            }}
          >
            🌟 Share Success Story
          </a>
          
          <a 
            href="/admin/collections/events/create"
            className="crud-button crud-button--create"
            style={{ 
              textDecoration: 'none',
              textAlign: 'center',
              padding: '1rem',
            }}
          >
            📅 Schedule Event
          </a>
          
          <a 
            href="/admin/collections/resources/create"
            className="crud-button crud-button--create"
            style={{ 
              textDecoration: 'none',
              textAlign: 'center',
              padding: '1rem',
            }}
          >
            📚 Upload Resource
          </a>
          
          <a 
            href="/admin/collections/contact-submissions"
            className="crud-button crud-button--edit"
            style={{ 
              textDecoration: 'none',
              textAlign: 'center',
              padding: '1rem',
            }}
          >
            📧 Review Messages
          </a>
        </div>
      </div>

      {/* API Status Panel */}
      <div style={{ 
        margin: '2rem 0',
        padding: '1.5rem',
        backgroundColor: '#f0f9ff',
        borderRadius: '0.5rem',
        border: '1px solid #0ea5e9',
      }}>
        <h3 style={{ 
          fontSize: '1.25rem', 
          fontWeight: '600', 
          marginBottom: '1rem',
          color: '#0c4a6e',
        }}>
          🔧 API & Development Tools
        </h3>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
        }}>
          <div style={{
            padding: '1rem',
            backgroundColor: 'white',
            borderRadius: '0.375rem',
            border: '1px solid #e0f2fe',
          }}>
            <h4 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>
              API Testing
            </h4>
            <p style={{ 
              fontSize: '0.875rem', 
              color: '#6b7280', 
              marginBottom: '1rem',
            }}>
              Test all CRUD endpoints directly from the admin interface
            </p>
            <button 
              onClick={() => {
                // Scroll to API tester or navigate to it
                const apiTesterSection = document.querySelector('.api-tester')
                if (apiTesterSection) {
                  apiTesterSection.scrollIntoView({ behavior: 'smooth' })
                } else {
                  // If not visible, we could trigger a tab change
                  console.log('Navigate to API tester')
                }
              }}
              className="crud-button crud-button--test"
              style={{ width: '100%' }}
            >
              Open API Tester
            </button>
          </div>
          
          <div style={{
            padding: '1rem',
            backgroundColor: 'white',
            borderRadius: '0.375rem',
            border: '1px solid #e0f2fe',
          }}>
            <h4 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>
              System Health
            </h4>
            <p style={{ 
              fontSize: '0.875rem', 
              color: '#6b7280', 
              marginBottom: '1rem',
            }}>
              Monitor database and API status
            </p>
            <a 
              href="/api/health"
              target="_blank"
              rel="noopener noreferrer"
              className="crud-button crud-button--edit"
              style={{ 
                textDecoration: 'none',
                width: '100%',
                textAlign: 'center',
              }}
            >
              Check Health
            </a>
          </div>
          
          <div style={{
            padding: '1rem',
            backgroundColor: 'white',
            borderRadius: '0.375rem',
            border: '1px solid #e0f2fe',
          }}>
            <h4 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>
              Documentation
            </h4>
            <p style={{ 
              fontSize: '0.875rem', 
              color: '#6b7280', 
              marginBottom: '1rem',
            }}>
              Access API documentation and guides
            </p>
            <a 
              href="/api/docs"
              target="_blank"
              rel="noopener noreferrer"
              className="crud-button crud-button--edit"
              style={{ 
                textDecoration: 'none',
                width: '100%',
                textAlign: 'center',
              }}
            >
              View Docs
            </a>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div style={{ 
        margin: '2rem 0',
        padding: '1.5rem',
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        border: '1px solid #e5e7eb',
      }}>
        <h3 style={{ 
          fontSize: '1.25rem', 
          fontWeight: '600', 
          marginBottom: '1rem',
          color: '#374151',
        }}>
          📈 Getting Started
        </h3>
        
        <div style={{ fontSize: '0.875rem', color: '#6b7280', lineHeight: '1.6' }}>
          <p style={{ marginBottom: '1rem' }}>
            Welcome to the NPI CMS Admin Panel! This comprehensive content management system provides:
          </p>
          
          <ul style={{ 
            listStyle: 'disc', 
            paddingLeft: '1.5rem', 
            marginBottom: '1rem',
          }}>
            <li>Complete CRUD operations for all content types</li>
            <li>Built-in API testing interface</li>
            <li>Auto-generated unique IDs for all records</li>
            <li>Database-stored media files</li>
            <li>Enhanced validation and error handling</li>
            <li>Real-time system health monitoring</li>
          </ul>
          
          <p>
            Use the navigation menu to manage different content types, or use the API Tester tab above 
            to test endpoints directly from this interface.
          </p>
        </div>
      </div>
    </div>
  )
}
