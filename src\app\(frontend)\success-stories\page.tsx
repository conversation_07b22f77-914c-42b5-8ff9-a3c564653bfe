import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'Success Stories - Natural Products Industry Initiative',
  description:
    'Discover inspiring success stories from across Kenya. See how NPI initiatives are transforming lives, empowering communities, and preserving cultural heritage while creating sustainable economic opportunities.',
}

const successStoriesLayout = [
  {
    blockType: 'npiSuccessStoriesHero' as const,
  },
  {
    blockType: 'npiSuccessStoriesGrid' as const,
    id: 'success-stories',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Impact Across Kenya',
    variant: 'pattern',
  },
]

export default function SuccessStoriesPage() {
  return (
    <>
      <PageClient />
      <article className="pb-24">
        {successStoriesLayout.map((block, index) => (
          <section
            key={index}
            className={`
              ${index === 0 ? '' : '-mt-1'}
              relative
              ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
            `}
          >
            <RenderBlocks blocks={[block]} />
          </section>
        ))}
      </article>
    </>
  )
}
