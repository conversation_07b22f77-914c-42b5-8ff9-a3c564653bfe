'use client'

import React, { useState } from 'react'

interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  path: string
  description: string
  collection?: string
}

const API_ENDPOINTS: ApiEndpoint[] = [
  // Projects
  { method: 'GET', path: '/api/projects', description: 'Get all projects', collection: 'projects' },
  { method: 'POST', path: '/api/projects', description: 'Create new project', collection: 'projects' },
  { method: 'GET', path: '/api/projects/:id', description: 'Get project by ID', collection: 'projects' },
  { method: 'PUT', path: '/api/projects/:id', description: 'Update project', collection: 'projects' },
  { method: 'DELETE', path: '/api/projects/:id', description: 'Delete project', collection: 'projects' },
  
  // Success Stories
  { method: 'GET', path: '/api/success-stories', description: 'Get all success stories', collection: 'success-stories' },
  { method: 'POST', path: '/api/success-stories', description: 'Create new success story', collection: 'success-stories' },
  { method: 'GET', path: '/api/success-stories/:id', description: 'Get success story by ID', collection: 'success-stories' },
  { method: 'PUT', path: '/api/success-stories/:id', description: 'Update success story', collection: 'success-stories' },
  { method: 'DELETE', path: '/api/success-stories/:id', description: 'Delete success story', collection: 'success-stories' },
  
  // News
  { method: 'GET', path: '/api/news', description: 'Get all news articles', collection: 'news' },
  { method: 'POST', path: '/api/news', description: 'Create new news article', collection: 'news' },
  { method: 'GET', path: '/api/news/:id', description: 'Get news article by ID', collection: 'news' },
  { method: 'PUT', path: '/api/news/:id', description: 'Update news article', collection: 'news' },
  { method: 'DELETE', path: '/api/news/:id', description: 'Delete news article', collection: 'news' },
  
  // Resources
  { method: 'GET', path: '/api/resources', description: 'Get all resources', collection: 'resources' },
  { method: 'POST', path: '/api/resources', description: 'Create new resource', collection: 'resources' },
  { method: 'GET', path: '/api/resources/:id', description: 'Get resource by ID', collection: 'resources' },
  { method: 'PUT', path: '/api/resources/:id', description: 'Update resource', collection: 'resources' },
  { method: 'DELETE', path: '/api/resources/:id', description: 'Delete resource', collection: 'resources' },
  
  // Events
  { method: 'GET', path: '/api/events', description: 'Get all events', collection: 'events' },
  { method: 'POST', path: '/api/events', description: 'Create new event', collection: 'events' },
  { method: 'GET', path: '/api/events/:id', description: 'Get event by ID', collection: 'events' },
  { method: 'PUT', path: '/api/events/:id', description: 'Update event', collection: 'events' },
  { method: 'DELETE', path: '/api/events/:id', description: 'Delete event', collection: 'events' },
  
  // Contact Submissions
  { method: 'GET', path: '/api/contact-submissions', description: 'Get all contact submissions', collection: 'contact-submissions' },
  { method: 'POST', path: '/api/contact-submissions', description: 'Create new contact submission', collection: 'contact-submissions' },
  { method: 'GET', path: '/api/contact-submissions/:id', description: 'Get contact submission by ID', collection: 'contact-submissions' },
  { method: 'PUT', path: '/api/contact-submissions/:id', description: 'Update contact submission', collection: 'contact-submissions' },
  { method: 'DELETE', path: '/api/contact-submissions/:id', description: 'Delete contact submission', collection: 'contact-submissions' },
]

interface TestResult {
  endpoint: string
  method: string
  status: number
  success: boolean
  data?: any
  error?: string
  timestamp: string
}

export const ApiTester: React.FC = () => {
  const [selectedEndpoint, setSelectedEndpoint] = useState<ApiEndpoint | null>(null)
  const [testId, setTestId] = useState('')
  const [requestBody, setRequestBody] = useState('')
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const testEndpoint = async (endpoint: ApiEndpoint) => {
    setIsLoading(true)
    
    try {
      let url = endpoint.path
      
      // Replace :id with actual ID if provided
      if (url.includes(':id') && testId) {
        url = url.replace(':id', testId)
      } else if (url.includes(':id') && !testId) {
        throw new Error('ID is required for this endpoint')
      }

      const options: RequestInit = {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
        },
      }

      // Add request body for POST and PUT requests
      if ((endpoint.method === 'POST' || endpoint.method === 'PUT') && requestBody) {
        try {
          JSON.parse(requestBody) // Validate JSON
          options.body = requestBody
        } catch {
          throw new Error('Invalid JSON in request body')
        }
      }

      const response = await fetch(url, options)
      const data = await response.json()

      const result: TestResult = {
        endpoint: url,
        method: endpoint.method,
        status: response.status,
        success: response.ok,
        data: response.ok ? data : undefined,
        error: !response.ok ? data.message || data.error || 'Unknown error' : undefined,
        timestamp: new Date().toISOString(),
      }

      setTestResults(prev => [result, ...prev.slice(0, 9)]) // Keep last 10 results
      
    } catch (error) {
      const result: TestResult = {
        endpoint: endpoint.path,
        method: endpoint.method,
        status: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        timestamp: new Date().toISOString(),
      }
      
      setTestResults(prev => [result, ...prev.slice(0, 9)])
    } finally {
      setIsLoading(false)
    }
  }

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'api-method--get'
      case 'POST': return 'api-method--post'
      case 'PUT': return 'api-method--put'
      case 'DELETE': return 'api-method--delete'
      default: return ''
    }
  }

  const groupedEndpoints = API_ENDPOINTS.reduce((acc, endpoint) => {
    const collection = endpoint.collection || 'Other'
    if (!acc[collection]) acc[collection] = []
    acc[collection].push(endpoint)
    return acc
  }, {} as Record<string, ApiEndpoint[]>)

  return (
    <div className="api-tester">
      <h2 className="api-tester__title">API Endpoint Tester</h2>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem' }}>
        {/* Endpoint Selection */}
        <div>
          <h3>Available Endpoints</h3>
          {Object.entries(groupedEndpoints).map(([collection, endpoints]) => (
            <div key={collection} style={{ marginBottom: '1.5rem' }}>
              <h4 style={{ 
                fontSize: '1rem', 
                fontWeight: '600', 
                marginBottom: '0.5rem',
                textTransform: 'capitalize'
              }}>
                {collection.replace('-', ' ')}
              </h4>
              {endpoints.map((endpoint, index) => (
                <div 
                  key={index}
                  className="api-endpoint"
                  style={{ cursor: 'pointer' }}
                  onClick={() => setSelectedEndpoint(endpoint)}
                >
                  <span className={`api-method ${getMethodColor(endpoint.method)}`}>
                    {endpoint.method}
                  </span>
                  <span className="api-path">{endpoint.path}</span>
                  <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                    {endpoint.description}
                  </span>
                </div>
              ))}
            </div>
          ))}
        </div>

        {/* Test Interface */}
        <div>
          <h3>Test Endpoint</h3>
          {selectedEndpoint ? (
            <div>
              <div className="api-endpoint" style={{ marginBottom: '1rem' }}>
                <span className={`api-method ${getMethodColor(selectedEndpoint.method)}`}>
                  {selectedEndpoint.method}
                </span>
                <span className="api-path">{selectedEndpoint.path}</span>
              </div>

              {selectedEndpoint.path.includes(':id') && (
                <div style={{ marginBottom: '1rem' }}>
                  <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                    ID Parameter:
                  </label>
                  <input
                    type="text"
                    value={testId}
                    onChange={(e) => setTestId(e.target.value)}
                    placeholder="Enter ID"
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '0.375rem',
                    }}
                  />
                </div>
              )}

              {(selectedEndpoint.method === 'POST' || selectedEndpoint.method === 'PUT') && (
                <div style={{ marginBottom: '1rem' }}>
                  <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>
                    Request Body (JSON):
                  </label>
                  <textarea
                    value={requestBody}
                    onChange={(e) => setRequestBody(e.target.value)}
                    placeholder='{"title": "Example", "description": "Test data"}'
                    rows={6}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '0.375rem',
                      fontFamily: 'monospace',
                      fontSize: '0.875rem',
                    }}
                  />
                </div>
              )}

              <button
                onClick={() => testEndpoint(selectedEndpoint)}
                disabled={isLoading}
                className="crud-button crud-button--test"
                style={{ marginBottom: '1rem' }}
              >
                {isLoading ? 'Testing...' : 'Test Endpoint'}
              </button>
            </div>
          ) : (
            <p style={{ color: '#6b7280' }}>Select an endpoint to test</p>
          )}

          {/* Test Results */}
          {testResults.length > 0 && (
            <div>
              <h3>Test Results</h3>
              {testResults.map((result, index) => (
                <div
                  key={index}
                  style={{
                    padding: '1rem',
                    border: '1px solid #e5e7eb',
                    borderRadius: '0.375rem',
                    marginBottom: '0.5rem',
                    backgroundColor: result.success ? '#f0fdf4' : '#fef2f2',
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                    <span className={`api-method ${getMethodColor(result.method)}`}>
                      {result.method}
                    </span>
                    <span className="api-path">{result.endpoint}</span>
                    <span className={`status-indicator ${result.success ? 'status-indicator--success' : 'status-indicator--error'}`}>
                      {result.status}
                    </span>
                  </div>
                  
                  {result.success && result.data && (
                    <pre style={{
                      fontSize: '0.75rem',
                      backgroundColor: '#f9fafb',
                      padding: '0.5rem',
                      borderRadius: '0.25rem',
                      overflow: 'auto',
                      maxHeight: '200px',
                    }}>
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  )}
                  
                  {result.error && (
                    <div style={{ color: '#dc2626', fontSize: '0.875rem' }}>
                      Error: {result.error}
                    </div>
                  )}
                  
                  <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.5rem' }}>
                    {new Date(result.timestamp).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
