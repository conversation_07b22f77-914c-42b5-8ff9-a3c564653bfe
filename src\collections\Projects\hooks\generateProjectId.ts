import type { CollectionBeforeChangeHook } from 'payload'
import { v4 as uuidv4 } from 'uuid'

export const generateProjectId: CollectionBeforeChangeHook = ({ data, operation, req }) => {
  // Only generate ID for new projects (create operation)
  if (operation === 'create') {
    try {
      // Generate a unique project ID
      const timestamp = Date.now().toString(36) // Base36 timestamp for shorter string
      const randomPart = Math.random().toString(36).substring(2, 8) // 6 random characters
      const projectId = `NPI-${timestamp}-${randomPart}`.toUpperCase()
      
      // Add the generated ID to the project data
      data.projectId = projectId
      
      req.payload.logger.info(`Generated project ID: ${projectId}`)
      
      // Also ensure slug is generated if not provided
      if (!data.slug && data.title) {
        const baseSlug = data.title
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/-+/g, '-') // Replace multiple hyphens with single
          .trim()
        
        // Add timestamp to ensure uniqueness
        data.slug = `${baseSlug}-${timestamp}`
        
        req.payload.logger.info(`Generated slug: ${data.slug}`)
      }
      
    } catch (error) {
      req.payload.logger.error('Error generating project ID:', error)
      throw new Error('Failed to generate project ID')
    }
  }
  
  return data
}
