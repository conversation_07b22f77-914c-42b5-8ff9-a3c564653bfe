// <PERSON>ript to create an admin user
// Run with: node scripts/create-admin-user.js

const BASE_URL = 'http://localhost:3000/api'

// New admin user details
const NEW_ADMIN = {
  name: '<PERSON>',
  email: 'ivyn<PERSON><PERSON><EMAIL>',
  password: 'admin123',
  role: 'admin',
  _verified: true,
  _verificationToken: null,
  loginAttempts: 0,
  lockUntil: null,
  isActive: true,
  preferences: {
    newsletter: false,
    notifications: true,
    language: 'en'
  }
}

// Possible existing admin credentials to try
const POSSIBLE_ADMIN_CREDENTIALS = [
  { email: '<EMAIL>', password: 'admin123' },
  { email: '<EMAIL>', password: 'SuperAdmin123!' },
  { email: '<EMAIL>', password: 'Admin123!' },
  { email: '<EMAIL>', password: 'password' },
  { email: '<EMAIL>', password: 'password' },
]

let authToken = null
let successfulCredentials = null

async function tryLogin(email, password) {
  try {
    const response = await fetch(`${BASE_URL}/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    return data.token || null
  } catch (error) {
    return null
  }
}

async function login() {
  console.log('🔐 Trying to login with existing admin credentials...')

  for (const credentials of POSSIBLE_ADMIN_CREDENTIALS) {
    console.log(`   Trying: ${credentials.email}`)
    const token = await tryLogin(credentials.email, credentials.password)

    if (token) {
      authToken = token
      successfulCredentials = credentials
      console.log(`✅ Login successful with: ${credentials.email}`)
      return true
    }
  }

  console.error('❌ Login failed with all attempted credentials')
  console.log('\n🔧 To fix this, you can:')
  console.log('1. Create a first admin user via the admin panel at http://localhost:3000/admin')
  console.log('2. Or update the POSSIBLE_ADMIN_CREDENTIALS array in this script with your actual admin credentials')
  console.log('3. Or run the seed script to create default admin users')
  return false
}

async function checkUserExists(email) {
  console.log(`🔍 Checking if user ${email} already exists...`)
  
  try {
    const response = await fetch(`${BASE_URL}/users?where[email][equals]=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to check user: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.docs && data.docs.length > 0
  } catch (error) {
    console.error('❌ Error checking user existence:', error.message)
    return false
  }
}

async function createAdminUser() {
  console.log(`👤 Creating admin user: ${NEW_ADMIN.email}...`)
  
  try {
    const response = await fetch(`${BASE_URL}/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(NEW_ADMIN),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Failed to create user: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`)
    }

    const data = await response.json()
    console.log('✅ Admin user created successfully!')
    console.log(`📧 Email: ${data.email}`)
    console.log(`👤 Name: ${data.name}`)
    console.log(`🔑 Role: ${data.role}`)
    console.log(`🆔 ID: ${data.id}`)
    
    return data
  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message)
    return null
  }
}

async function main() {
  console.log('🚀 Admin User Creation Script')
  console.log('==============================')
  console.log(`Creating admin user: ${NEW_ADMIN.name} (${NEW_ADMIN.email})`)
  console.log('')

  // Step 1: Login with existing admin credentials
  const loginSuccess = await login()
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication')
    process.exit(1)
  }

  // Step 2: Check if user already exists
  const userExists = await checkUserExists(NEW_ADMIN.email)
  if (userExists) {
    console.log(`⚠️  User ${NEW_ADMIN.email} already exists!`)
    console.log('Skipping creation...')
    process.exit(0)
  }

  // Step 3: Create the admin user
  const newUser = await createAdminUser()
  if (!newUser) {
    console.log('❌ Failed to create admin user')
    process.exit(1)
  }

  console.log('')
  console.log('🎉 Admin user creation completed successfully!')
  console.log('')
  console.log('📋 Login Details:')
  console.log(`   Email: ${NEW_ADMIN.email}`)
  console.log(`   Password: ${NEW_ADMIN.password}`)
  console.log(`   Role: ${NEW_ADMIN.role}`)
  console.log('')
  console.log('🔗 You can now login at: http://localhost:3000/admin')
}

// Run the script
main().catch(error => {
  console.error('💥 Script failed:', error)
  process.exit(1)
})
