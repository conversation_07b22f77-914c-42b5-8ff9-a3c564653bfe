const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],

  // Use different test environments based on test type
  projects: [
    {
      displayName: 'client',
      testEnvironment: 'jsdom',
      testMatch: [
        '<rootDir>/__tests__/components/**/*.(test|spec).(js|jsx|ts|tsx)',
        '<rootDir>/__tests__/hooks/**/*.(test|spec).(js|jsx|ts|tsx)',
        '<rootDir>/__tests__/pages/**/*.(test|spec).(js|jsx|ts|tsx)',
      ],
    },
    {
      displayName: 'server',
      testEnvironment: 'node',
      testMatch: [
        '<rootDir>/__tests__/api/**/*.(test|spec).(js|jsx|ts|tsx)',
        '<rootDir>/__tests__/lib/**/*.(test|spec).(js|jsx|ts|tsx)',
        '<rootDir>/__tests__/collections/**/*.(test|spec).(js|jsx|ts|tsx)',
        '<rootDir>/__tests__/endpoints/**/*.(test|spec).(js|jsx|ts|tsx)',
      ],
    },
  ],

  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/collections/(.*)$': '<rootDir>/src/collections/$1',
    '^@/endpoints/(.*)$': '<rootDir>/src/endpoints/$1',
  },

  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/index.{js,jsx,ts,tsx}',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/payload.config.ts',
    '!src/server.ts',
  ],

  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage',

  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/build/',
  ],

  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
  },

  transformIgnorePatterns: [
    '/node_modules/',
    '^.+\\.module\\.(css|sass|scss)$',
  ],

  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],

  // Test timeout for async operations
  testTimeout: 30000,

  // Verbose output
  verbose: true,

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)
