import type { Field } from 'payload'

/**
 * Simple direct image upload field that uses the media collection
 * but provides a seamless upload experience
 */
export const directImageUploadField = (options: {
  name: string
  label?: string
  required?: boolean
  admin?: {
    description?: string
    position?: 'sidebar'
  }
}): Field => {
  return {
    name: options.name,
    type: 'upload',
    relationTo: 'media',
    label: options.label || 'Image',
    required: options.required,
    admin: {
      description: options.admin?.description || 'Upload an image (stored in database)',
      position: options.admin?.position,
    },
  }
}

/**
 * Enhanced image field with alt text and metadata
 */
export const enhancedImageField = (options: {
  name: string
  label?: string
  required?: boolean
  admin?: {
    description?: string
    position?: 'sidebar'
  }
}): Field => {
  return {
    name: options.name,
    type: 'group',
    label: options.label || 'Image',
    admin: {
      description: options.admin?.description || 'Image with metadata',
      position: options.admin?.position,
    },
    fields: [
      {
        name: 'image',
        type: 'upload',
        relationTo: 'media',
        required: options.required,
        admin: {
          description: 'Upload an image file',
        },
      },
      {
        name: 'alt',
        type: 'text',
        required: options.required,
        admin: {
          description: 'Alternative text for accessibility',
        },
      },
      {
        name: 'caption',
        type: 'text',
        admin: {
          description: 'Optional image caption',
        },
      },
    ],
  }
}

/**
 * Multiple images field for galleries
 */
export const multipleImagesField = (options: {
  name: string
  label?: string
  required?: boolean
  admin?: {
    description?: string
    position?: 'sidebar'
  }
}): Field => {
  return {
    name: options.name,
    type: 'array',
    label: options.label || 'Images',
    required: options.required,
    admin: {
      description: options.admin?.description || 'Upload multiple images',
      position: options.admin?.position,
    },
    fields: [
      {
        name: 'image',
        type: 'upload',
        relationTo: 'media',
        required: true,
        admin: {
          description: 'Upload an image file',
        },
      },
      {
        name: 'alt',
        type: 'text',
        required: true,
        admin: {
          description: 'Alternative text for accessibility',
        },
      },
      {
        name: 'caption',
        type: 'text',
        admin: {
          description: 'Optional image caption',
        },
      },
    ],
  }
}

/**
 * Hero image field with focal point
 */
export const heroImageField = (options: {
  name: string
  label?: string
  required?: boolean
  admin?: {
    description?: string
    position?: 'sidebar'
  }
}): Field => {
  return {
    name: options.name,
    type: 'group',
    label: options.label || 'Hero Image',
    admin: {
      description: options.admin?.description || 'Main hero image with focal point',
      position: options.admin?.position,
    },
    fields: [
      {
        name: 'image',
        type: 'upload',
        relationTo: 'media',
        required: options.required,
        admin: {
          description: 'Upload hero image',
        },
      },
      {
        name: 'alt',
        type: 'text',
        required: options.required,
        admin: {
          description: 'Alternative text for accessibility',
        },
      },
      {
        name: 'focalPoint',
        type: 'group',
        admin: {
          description: 'Image focal point for responsive cropping',
        },
        fields: [
          {
            name: 'x',
            type: 'number',
            min: 0,
            max: 100,
            admin: {
              description: 'Horizontal focal point (0-100%)',
            },
          },
          {
            name: 'y',
            type: 'number',
            min: 0,
            max: 100,
            admin: {
              description: 'Vertical focal point (0-100%)',
            },
          },
        ],
      },
    ],
  }
}
