import type { PayloadRequest } from 'payload'

interface TransformedContactSubmission {
  id: string
  name: string
  email: string
  phone?: string
  organization?: string
  role?: string
  subject: string
  category: string
  priority: string
  message: string
  location?: {
    county?: TransformedCounty
    city?: string
    country: string
  }
  attachments?: Array<{
    file: TransformedMedia
    description?: string
  }>
  status: string
  assignedTo?: any
  department?: string
  responses?: Array<{
    respondent: any
    responseDate: string
    responseMethod?: string
    response: string
    followUpRequired: boolean
    followUpDate?: string
  }>
  internalNotes?: Array<{
    author: any
    date: string
    note: string
    confidential: boolean
  }>
  followUp?: {
    required: boolean
    dueDate?: string
    assignedTo?: any
    notes?: string
    completed: boolean
    completedDate?: string
  }
  satisfaction?: {
    rating?: number
    feedback?: string
    surveyDate?: string
  }
  metadata: {
    source: string
    ipAddress?: string
    userAgent?: string
    referrer?: string
  }
  archived: boolean
  tags?: string[]
  createdAt: string
  updatedAt: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
}

interface TransformedCounty {
  id: string
  name: string
  code?: string
}

interface ContactSubmissionsResponse {
  submissions: TransformedContactSubmission[]
  totalSubmissions: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility functions
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
  }
}

const transformCounty = (county: any): TransformedCounty | undefined => {
  if (!county || typeof county === 'string') return undefined

  return {
    id: county.id,
    name: county.name,
    code: county.code,
  }
}

const transformContactSubmission = (submission: any): TransformedContactSubmission => {
  return {
    id: submission.id,
    name: submission.name,
    email: submission.email,
    phone: submission.phone,
    organization: submission.organization,
    role: submission.role,
    subject: submission.subject,
    category: submission.category,
    priority: submission.priority,
    message: submission.message,
    location: submission.location ? {
      county: transformCounty(submission.location.county),
      city: submission.location.city,
      country: submission.location.country || 'Kenya',
    } : undefined,
    attachments: Array.isArray(submission.attachments) 
      ? submission.attachments.map((attachment: any) => ({
          file: transformMedia(attachment.file),
          description: attachment.description,
        })).filter((attachment: any) => attachment.file)
      : [],
    status: submission.status,
    assignedTo: submission.assignedTo,
    department: submission.department,
    responses: Array.isArray(submission.responses) 
      ? submission.responses.map((response: any) => ({
          respondent: response.respondent,
          responseDate: response.responseDate,
          responseMethod: response.responseMethod,
          response: extractTextFromLexical(response.response),
          followUpRequired: response.followUpRequired || false,
          followUpDate: response.followUpDate,
        }))
      : [],
    internalNotes: Array.isArray(submission.internalNotes) 
      ? submission.internalNotes.map((note: any) => ({
          author: note.author,
          date: note.date,
          note: extractTextFromLexical(note.note),
          confidential: note.confidential || false,
        }))
      : [],
    followUp: submission.followUp ? {
      required: submission.followUp.required || false,
      dueDate: submission.followUp.dueDate,
      assignedTo: submission.followUp.assignedTo,
      notes: submission.followUp.notes,
      completed: submission.followUp.completed || false,
      completedDate: submission.followUp.completedDate,
    } : undefined,
    satisfaction: submission.satisfaction ? {
      rating: submission.satisfaction.rating,
      feedback: submission.satisfaction.feedback,
      surveyDate: submission.satisfaction.surveyDate,
    } : undefined,
    metadata: {
      source: submission.metadata?.source || 'website-form',
      ipAddress: submission.metadata?.ipAddress,
      userAgent: submission.metadata?.userAgent,
      referrer: submission.metadata?.referrer,
    },
    archived: submission.archived || false,
    tags: Array.isArray(submission.tags) 
      ? submission.tags.map((tag: any) => tag.tag).filter(Boolean)
      : [],
    createdAt: submission.createdAt,
    updatedAt: submission.updatedAt,
  }
}

// Main Contact Submissions Handler (Admin only)
export const contactSubmissionsHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Check if user is authenticated (admin access only)
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access contact submissions',
      })
    }

    // Parse query parameters
    const {
      category,
      status,
      priority,
      department,
      assignedTo,
      archived,
      county,
      limit = '20',
      page = '1',
      sort = '-createdAt',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {}

    if (category) where.category = { equals: category }
    if (status) where.status = { equals: status }
    if (priority) where.priority = { equals: priority }
    if (department) where.department = { equals: department }
    if (assignedTo) where.assignedTo = { equals: assignedTo }
    if (archived === 'true') where.archived = { equals: true }
    if (archived === 'false') where.archived = { equals: false }
    if (county) where['location.county'] = { equals: county }
    if (search) {
      where.or = [
        { name: { contains: search } },
        { email: { contains: search } },
        { subject: { contains: search } },
        { message: { contains: search } },
        { organization: { contains: search } },
      ]
    }

    // Fetch contact submissions with populated relationships
    const submissionsResult = await payload.find({
      collection: 'contact-submissions',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate users, counties, etc.
    })

    // Transform contact submissions
    const transformedSubmissions: TransformedContactSubmission[] = submissionsResult.docs.map(transformContactSubmission)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(submissionsResult.totalDocs / currentLimit)

    const response: ContactSubmissionsResponse = {
      submissions: transformedSubmissions,
      totalSubmissions: submissionsResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in contact submissions endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Create new contact submission (Public endpoint)
export const createContactSubmissionHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Extract data from request body
    const {
      name,
      email,
      phone,
      organization,
      role,
      subject,
      category,
      message,
      location,
      priority = 'medium',
    } = req.body

    // Basic validation
    if (!name || !email || !subject || !message || !category) {
      return res.status(400).json({
        error: 'Validation error',
        message: 'Name, email, subject, message, and category are required',
      })
    }

    // Capture metadata
    const metadata = {
      source: 'website-form',
      ipAddress: req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress,
      userAgent: req.headers['user-agent'],
      referrer: req.headers.referer || req.headers.referrer,
    }

    // Create the submission
    const submission = await payload.create({
      collection: 'contact-submissions',
      data: {
        name,
        email,
        phone,
        organization,
        role,
        subject,
        category,
        priority,
        message,
        location,
        status: 'new',
        metadata,
        archived: false,
      },
    })

    // Return success response (without sensitive data)
    res.status(201).json({
      success: true,
      message: 'Contact submission received successfully',
      submissionId: submission.id,
      status: 'new',
    })

    // TODO: Send notification email to admin team
    // TODO: Send confirmation email to submitter

  } catch (error) {
    console.error('Error creating contact submission:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to submit contact form',
    })
  }
}

// Get single contact submission by ID (Admin only)
export const contactSubmissionByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Check if user is authenticated (admin access only)
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to access contact submissions',
      })
    }

    const submission = await payload.findByID({
      collection: 'contact-submissions',
      id,
      depth: 2,
    })

    if (!submission) {
      return res.status(404).json({
        error: 'Submission not found',
        message: `No contact submission found with ID: ${id}`,
      })
    }

    const transformedSubmission = transformContactSubmission(submission)

    res.status(200).json({
      submission: transformedSubmission,
    })
  } catch (error) {
    console.error('Error in contact submission by ID endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Update contact submission status (Admin only)
export const updateContactSubmissionHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Check if user is authenticated (admin access only)
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required to update contact submissions',
      })
    }

    const { status, assignedTo, department, priority } = req.body

    const updatedSubmission = await payload.update({
      collection: 'contact-submissions',
      id,
      data: {
        status,
        assignedTo,
        department,
        priority,
      },
    })

    res.status(200).json({
      success: true,
      message: 'Contact submission updated successfully',
      submission: transformContactSubmission(updatedSubmission),
    })
  } catch (error) {
    console.error('Error updating contact submission:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
