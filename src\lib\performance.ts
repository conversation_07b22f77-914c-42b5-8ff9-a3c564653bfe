// Performance monitoring utilities for NPI website

export interface PerformanceMetrics {
  pageLoadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
  timeToInteractive: number
}

export class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private observers: PerformanceObserver[] = []

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers()
    }
  }

  private initializeObservers() {
    // Observe paint metrics
    if ('PerformanceObserver' in window) {
      try {
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.firstContentfulPaint = entry.startTime
            }
          }
        })
        paintObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(paintObserver)
      } catch (e) {
        console.warn('Paint observer not supported')
      }

      // Observe LCP
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          this.metrics.largestContentfulPaint = lastEntry.startTime
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)
      } catch (e) {
        console.warn('LCP observer not supported')
      }

      // Observe FID
      try {
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.metrics.firstInputDelay = entry.processingStart - entry.startTime
          }
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)
      } catch (e) {
        console.warn('FID observer not supported')
      }

      // Observe CLS
      try {
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          }
          this.metrics.cumulativeLayoutShift = clsValue
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)
      } catch (e) {
        console.warn('CLS observer not supported')
      }
    }

    // Page load time
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart
      }
    })
  }

  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics }
  }

  public logMetrics(): void {
    console.group('🚀 NPI Performance Metrics')
    console.log('Page Load Time:', this.metrics.pageLoadTime?.toFixed(2), 'ms')
    console.log('First Contentful Paint:', this.metrics.firstContentfulPaint?.toFixed(2), 'ms')
    console.log('Largest Contentful Paint:', this.metrics.largestContentfulPaint?.toFixed(2), 'ms')
    console.log('First Input Delay:', this.metrics.firstInputDelay?.toFixed(2), 'ms')
    console.log('Cumulative Layout Shift:', this.metrics.cumulativeLayoutShift?.toFixed(4))
    console.groupEnd()
  }

  public sendMetrics(endpoint: string): void {
    if (typeof window !== 'undefined' && navigator.sendBeacon) {
      const data = JSON.stringify({
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        metrics: this.metrics
      })
      
      navigator.sendBeacon(endpoint, data)
    }
  }

  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Core Web Vitals thresholds
export const PERFORMANCE_THRESHOLDS = {
  firstContentfulPaint: {
    good: 1800,
    needsImprovement: 3000
  },
  largestContentfulPaint: {
    good: 2500,
    needsImprovement: 4000
  },
  firstInputDelay: {
    good: 100,
    needsImprovement: 300
  },
  cumulativeLayoutShift: {
    good: 0.1,
    needsImprovement: 0.25
  }
}

export function getPerformanceGrade(metric: keyof typeof PERFORMANCE_THRESHOLDS, value: number): 'good' | 'needs-improvement' | 'poor' {
  const thresholds = PERFORMANCE_THRESHOLDS[metric]
  if (value <= thresholds.good) return 'good'
  if (value <= thresholds.needsImprovement) return 'needs-improvement'
  return 'poor'
}

// Utility to measure component render time
export function measureRenderTime<T extends any[]>(
  componentName: string,
  fn: (...args: T) => any
) {
  return (...args: T) => {
    const start = performance.now()
    const result = fn(...args)
    const end = performance.now()
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🎯 ${componentName} render time: ${(end - start).toFixed(2)}ms`)
    }
    
    return result
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// Auto-log metrics after page load (development only)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.addEventListener('load', () => {
    setTimeout(() => {
      performanceMonitor.logMetrics()
    }, 5000) // Wait 5 seconds for all metrics to be collected
  })
}
