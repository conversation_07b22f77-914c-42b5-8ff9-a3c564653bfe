import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'

// Media gallery sub-page components
// Uses: NPIMediaGalleryHeroBlock, NPIMediaGalleryContentBlock from resources/media-gallery
// Plus shared: NPIStatisticsBlock

export const metadata: Metadata = {
  title: 'Media Gallery - Resources - Natural Products Industry Initiative',
  description:
    'Explore our comprehensive media gallery featuring videos, photos, and multimedia content showcasing NPI initiatives, community impact, and natural products development across Kenya.',
}

const mediaGalleryPageLayout = [
  {
    blockType: 'npiMediaGalleryHero' as const,
    title: 'Media Gallery',
    backgroundImage: '/assets/product 3.jpg',
  },
  {
    blockType: 'npiMediaGalleryContent' as const,
    id: 'media-content',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Media Impact & Reach',
    variant: 'secondary',
  },
]

export default function MediaGalleryPage() {
  return (
    <article className="min-h-screen">
      {mediaGalleryPageLayout.map((block, index) => (
        <section
          key={index}
          className={`
            ${index === 0 ? '' : '-mt-1'}
            relative
            ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
          `}
        >
          <RenderBlocks blocks={[block]} />
        </section>
      ))}
    </article>
  )
}
