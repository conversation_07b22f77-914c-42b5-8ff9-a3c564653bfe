# Natural Products Institute - Content Management System

A comprehensive content management system built with PayloadCMS for the Natural Products Institute, enabling efficient management of projects, success stories, resources, news, media, partnerships, and investment opportunities.

## 🚀 Features

### Content Management
- **Projects**: Manage development projects and initiatives with detailed tracking
- **Success Stories**: Showcase community impact and achievements
- **Resources & Publications**: Manage downloadable resources with access control
- **News & Updates**: Publish news articles and announcements
- **Media Gallery**: Organize multimedia content with metadata
- **Partnerships**: Track organizational partnerships and collaborations
- **Investment Opportunities**: Manage funding and investment opportunities
- **Contact Management**: Handle inquiries and submissions

### Technical Features
- **Role-based Access Control**: Granular permissions for different user types
- **Rich Text Editing**: Advanced content editing with Lexical editor
- **File Management**: Integrated file storage with Vercel Blob
- **API-First Architecture**: RESTful APIs for all content types
- **Data Validation**: Comprehensive input validation and sanitization
- **Search & Filtering**: Advanced search capabilities across all content
- **Responsive Admin Panel**: Mobile-friendly administration interface
- **Multi-language Support**: Content localization capabilities

## 🛠 Technology Stack

- **Backend**: PayloadCMS (Node.js/Express)
- **Frontend**: Next.js 14 with TypeScript
- **Database**: MongoDB (with Cloud Atlas support)
- **File Storage**: Vercel Blob Storage
- **Authentication**: JWT with role-based access
- **Validation**: Zod schemas
- **Styling**: Tailwind CSS
- **Deployment**: Vercel Platform

## 📋 Prerequisites

- Node.js 18 or higher
- MongoDB database
- Vercel account (for blob storage)
- Git

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/npi-website.git
cd npi-website
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Setup

Create a `.env.local` file:

```env
# Database - MongoDB (Local)
DATABASE_URI=mongodb://localhost:27017/npi-cms

# Database - MongoDB Cloud Atlas (Production)
# DATABASE_URI=mongodb+srv://username:<EMAIL>/npi-cms?retryWrites=true&w=majority

# Authentication
PAYLOAD_SECRET=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# File Storage (optional for development)
BLOB_READ_WRITE_TOKEN=your-vercel-blob-token

# Application
NEXT_PUBLIC_API_URL=http://localhost:3000
```

### 4. Start Development Server

```bash
npm run dev
```

### 5. Access Admin Panel

Visit `http://localhost:3000/admin` to access the CMS admin panel.

**Default Admin User** (created during first run):
- Email: `<EMAIL>`
- Password: `admin123` (change immediately)

## 📚 Documentation

### Core Documentation
- [CMS Documentation](./docs/CMS_DOCUMENTATION.md) - Comprehensive system overview
- [API Reference](./docs/API_REFERENCE.md) - Complete API documentation
- [Deployment Guide](./docs/DEPLOYMENT_GUIDE.md) - Production deployment instructions

### Quick Links
- [Collections Overview](#collections)
- [API Endpoints](#api-endpoints)
- [Frontend Integration](#frontend-integration)
- [Development Guide](#development)

## 📊 Collections

### Core Content Collections

| Collection | Description | Key Features |
|------------|-------------|--------------|
| **Projects** | Development projects and initiatives | Timeline tracking, budget management, impact metrics |
| **Success Stories** | Community achievements and case studies | Participant profiles, testimonials, impact data |
| **Resources** | Publications and downloadable content | Access control, analytics, multi-format support |
| **News** | Articles and announcements | Publishing workflow, categorization, SEO optimization |
| **Media Gallery** | Multimedia content management | Metadata, usage rights, accessibility features |
| **Partnerships** | Organizational collaborations | Scope tracking, resource allocation, outcomes |
| **Investment Opportunities** | Funding and investment listings | Financial details, requirements, application process |
| **Contact Submissions** | Inquiry and contact management | Status tracking, response management, analytics |

### Supporting Collections
- **Counties**: Geographic reference data
- **Partners**: Partner organization profiles
- **Users**: System users with role-based access
- **Events**: Event management and promotion

## 🔌 API Endpoints

### Public Endpoints
```
GET /api/projects              # List projects
GET /api/projects/:id          # Get single project
GET /api/success-stories       # List success stories
GET /api/resources             # List resources
GET /api/news                  # List news articles
GET /api/media-gallery         # List media items
GET /api/investment-opportunities  # List opportunities
POST /api/contact-submissions  # Submit contact form
```

### Admin Endpoints (Authentication Required)
```
POST /api/projects             # Create project
PUT /api/projects/:id          # Update project
DELETE /api/projects/:id       # Delete project
GET /api/contact-submissions   # List submissions
PUT /api/contact-submissions/:id  # Update submission
```

## 🎨 Frontend Integration

### React Hooks

```typescript
import { useProjects, useNews, useContactForm } from '@/lib/cms'

// Fetch projects
const { data: projects, loading, error } = useProjects({
  featured: true,
  limit: 6
})

// Submit contact form
const { submitForm, loading, success } = useContactForm()
```

### API Client

```typescript
import { cmsAPI } from '@/lib/cms'

// Fetch data
const projects = await cmsAPI.projects.getAll({ featured: true })
const news = await cmsAPI.news.getFeatured(5)

// Submit form
await cmsAPI.contact.submit(formData)
```

### Utility Functions

```typescript
import { formatCurrency, formatDate, getOptimizedImageUrl } from '@/lib/cms'

const price = formatCurrency(1500000, 'KES') // "KES 1,500,000"
const date = formatDate('2024-01-15') // "January 15, 2024"
const imageUrl = getOptimizedImageUrl(media, { width: 800 })
```

## 🔒 Authentication & Authorization

### User Roles
- **Super Admin**: Full system access
- **Admin**: Content and user management
- **Content Manager**: Content creation and editing
- **Editor**: Content editing only
- **User**: Read-only access

### Access Control
Collections implement role-based permissions with granular control over create, read, update, and delete operations.

## 🛡 Security Features

- **Input Validation**: Zod schema validation for all inputs
- **Data Sanitization**: XSS and injection attack prevention
- **Rate Limiting**: API endpoint protection
- **File Upload Security**: MIME type validation and size limits
- **CORS Configuration**: Proper cross-origin request handling
- **JWT Authentication**: Secure token-based authentication

## 🚀 Development

### Project Structure

```
src/
├── collections/          # PayloadCMS collections
├── endpoints/           # Custom API endpoints
├── lib/
│   ├── cms/            # CMS utilities and hooks
│   └── validation/     # Validation schemas and error handling
├── access/             # Access control functions
├── fields/             # Reusable field configurations
└── hooks/              # PayloadCMS hooks

docs/                   # Documentation
├── CMS_DOCUMENTATION.md
├── API_REFERENCE.md
└── DEPLOYMENT_GUIDE.md
```

### Adding New Collections

1. Create collection configuration in `src/collections/`
2. Add to `payload.config.ts`
3. Create API endpoints in `src/endpoints/`
4. Add TypeScript types to `src/lib/cms/types.ts`
5. Create React hooks in `src/lib/cms/hooks.ts`

### Development Commands

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run seed         # Seed database with sample data
npm run type-check   # Run TypeScript checks
npm run lint         # Run ESLint
```

## 📦 Deployment

### Vercel (Recommended)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Docker

```bash
# Build image
docker build -t npi-cms .

# Run container
docker run -p 3000:3000 npi-cms
```

See [Deployment Guide](./docs/DEPLOYMENT_GUIDE.md) for detailed instructions.

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `DATABASE_URI` | MongoDB connection string | Yes |
| `PAYLOAD_SECRET` | PayloadCMS secret key | Yes |
| `JWT_SECRET` | JWT signing secret | Yes |
| `BLOB_READ_WRITE_TOKEN` | Vercel Blob storage token | Yes |
| `NEXT_PUBLIC_API_URL` | Public API URL | Yes |
| `SMTP_HOST` | Email server host | No |
| `SMTP_USER` | Email username | No |
| `SMTP_PASS` | Email password | No |

### Database Indexes

For optimal performance, create these MongoDB indexes:

```javascript
// Projects
db.projects.createIndex({ "slug": 1 }, { unique: true })
db.projects.createIndex({ "featured": 1, "published": 1 })
db.projects.createIndex({ "category": 1, "status": 1 })

// News
db.news.createIndex({ "slug": 1 }, { unique: true })
db.news.createIndex({ "status": 1, "publishDate": -1 })
db.news.createIndex({ "featured": 1, "urgent": 1 })
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write comprehensive tests
- Update documentation for new features
- Follow the existing code style
- Ensure all validations pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help

- **Documentation**: Check the [docs](./docs/) directory
- **Issues**: Create a GitHub issue for bugs or feature requests
- **Discussions**: Use GitHub Discussions for questions

### Common Issues

- **Build Errors**: Check Node.js version and environment variables
- **Database Connection**: Verify MongoDB connection string
- **File Uploads**: Ensure Vercel Blob is properly configured

## 🙏 Acknowledgments

- [PayloadCMS](https://payloadcms.com/) - Headless CMS framework
- [Next.js](https://nextjs.org/) - React framework
- [Vercel](https://vercel.com/) - Deployment platform
- [MongoDB](https://www.mongodb.com/) - Database
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework

---

**Natural Products Institute** - Empowering communities through indigenous knowledge and sustainable development.

For more information, visit [npi.org](https://npi.org) or contact us at [<EMAIL>](mailto:<EMAIL>).
