'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardTitle } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, ArrowRight } from 'lucide-react'

interface NewsItem {
  title: string
  excerpt: string
  image: string
  date: string
  category: string
  link: string
  featured?: boolean
}

interface NPILatestUpdatesProps {
  title?: string
  description?: string
  news?: NewsItem[]
}

export const NPILatestUpdatesBlock: React.FC<NPILatestUpdatesProps> = ({
  title = 'News & Updates',
  description = "Stay informed about our latest developments, achievements, and upcoming initiatives in Kenya's natural products sector.",
  news = [
    {
      title: 'NPI Launches Comprehensive Knowledge Documentation Platform',
      excerpt:
        'Revolutionary digital platform now provides access to over 1,200 documented indigenous knowledge assets, supporting researchers and entrepreneurs nationwide.',
      image: '/assets/background.jpg',
      date: '2024-01-15',
      category: 'Platform Launch',
      link: '/news/knowledge-platform-launch',
      featured: true,
    },
    {
      title: 'Successful IP Registration for Maasai Traditional Medicine',
      excerpt:
        'Historic achievement as Maasai community secures intellectual property rights for traditional healing formulations, setting precedent for indigenous knowledge protection.',
      image: '/assets/product 1.jpg',
      date: '2024-01-10',
      category: 'IP Protection',
      link: '/news/maasai-ip-success',
    },
    {
      title: "Women's Aloe Cooperative Wins National Innovation Award",
      excerpt:
        "Baringo County women's cooperative recognized for outstanding innovation in natural product development and community empowerment.",
      image: '/assets/product 2.jpg',
      date: '2024-01-05',
      category: 'Awards',
      link: '/news/aloe-cooperative-award',
    },
    {
      title: 'International Investment Forum Attracts $50M Commitments',
      excerpt:
        "NPI's first International Natural Products Investment Forum successfully attracts significant funding commitments for community-based projects.",
      image: '/assets/product 3.jpg',
      date: '2023-12-20',
      category: 'Investment',
      link: '/news/investment-forum-success',
    },
  ],
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const featuredNews = news.find((item) => item.featured)
  const regularNews = news.filter((item) => !item.featured)

  return (
    <NPISection size="sm" className="bg-white relative overflow-hidden pb-0">
      <div className="relative z-10">
        <NPISectionHeader className="text-center mb-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="inline-flex items-center px-4 py-2 bg-[#8A3E25]/15 border border-[#8A3E25]/30 text-[#8A3E25] text-sm font-semibold mb-3"
          >
            <Calendar className="w-4 h-4 mr-3 text-[#25718A]" />
            News & Updates
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-2 text-black font-bold text-2xl lg:text-3xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <NPISectionDescription className="font-light leading-[1.6] text-black max-w-xl mx-auto text-sm lg:text-base">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        {/* Regular News - Second Row */}
        <div className="grid lg:grid-cols-3 gap-4 mb-4 max-w-4xl mx-auto">
          {/* Regular News - Square Cards */}
          {regularNews.slice(0, 3).map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -6 }}
            >
              <NPICard
                className={`overflow-hidden shadow-lg border-2 hover:shadow-xl group transition-all duration-300 aspect-square flex flex-col hover:scale-[1.03] hover:-translate-y-1 ${
                  index % 3 === 0
                    ? 'bg-[#725242] border-[#725242] hover:border-[#8A3E25] hover:shadow-[#725242]/30'
                    : index % 3 === 1
                      ? 'bg-[#8A3E25] border-[#8A3E25] hover:border-[#725242] hover:shadow-[#8A3E25]/30'
                      : 'bg-white border-white hover:border-[#8A3E25] hover:shadow-white/30'
                }`}
              >
                <div className="relative h-1/2 w-full flex-shrink-0">
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-black/40" />
                  <div className="absolute top-2 right-2">
                    <span
                      className={`px-2 py-1 text-xs font-bold text-white ${
                        index % 3 === 0
                          ? 'bg-[#25718A]'
                          : index % 3 === 1
                            ? 'bg-[#725242]'
                            : 'bg-[#8A3E25]'
                      }`}
                    >
                      {item.category}
                    </span>
                  </div>
                </div>

                <div className="h-1/2 p-3 flex flex-col justify-between">
                  <div>
                    <div
                      className={`flex items-center gap-2 text-xs mb-2 ${
                        index % 3 === 0
                          ? 'text-white/90'
                          : index % 3 === 1
                            ? 'text-white/90'
                            : 'text-black/80'
                      }`}
                    >
                      <Calendar className="w-3 h-3 text-[#25718A]" />
                      <span className="font-medium">{formatDate(item.date)}</span>
                    </div>

                    <h3
                      className={`font-bold text-sm leading-tight mb-2 line-clamp-2 ${
                        index % 3 === 0
                          ? 'text-white'
                          : index % 3 === 1
                            ? 'text-white'
                            : 'text-black'
                      }`}
                    >
                      <Link href={item.link} className="transition-colors">
                        {item.title}
                      </Link>
                    </h3>
                  </div>

                  <NPIButton
                    asChild
                    className="w-full bg-[#25718A] hover:bg-[#25718A] text-white font-medium transition-all duration-300 text-xs py-2"
                  >
                    <Link href={item.link} className="flex items-center justify-center gap-1">
                      Read More <ArrowRight className="w-3 h-3" />
                    </Link>
                  </NPIButton>
                </div>
              </NPICard>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center mt-4"
        >
          <NPIButton
            asChild
            size="lg"
            className="bg-[#8A3E25] hover:bg-[#8A3E25] text-white font-bold px-8 py-3 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            <Link href="/news">View All News & Updates</Link>
          </NPIButton>
        </motion.div>
      </div>
    </NPISection>
  )
}
