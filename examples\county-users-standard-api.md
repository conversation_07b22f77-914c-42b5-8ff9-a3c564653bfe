# County Users - Standard REST API

## 🔗 **Standard REST Endpoint**

I've created a proper REST endpoint that follows standard conventions for getting users by county.

## 📋 **Endpoint**

### **GET /api/counties/:id/users**

Get all users in a specific county using standard REST path parameters.

**Authentication:** Not required (public access)

**Path Parameters:**
- `id` - County ID (required)

**Query Parameters (optional):**
- `limit` - Items per page (default: 50)
- `page` - Page number (default: 1)
- `sort` - Sort field (default: name)

## 🧪 **Usage Examples**

### **Basic Usage:**
```bash
# Get all users in county with ID 1
curl "http://localhost:3000/api/counties/1/users"

# Get users in county 2
curl "http://localhost:3000/api/counties/2/users"

# Get users in county 3
curl "http://localhost:3000/api/counties/3/users"
```

### **With Pagination:**
```bash
# Get first 10 users in county 1
curl "http://localhost:3000/api/counties/1/users?limit=10&page=1"

# Get next 10 users in county 1
curl "http://localhost:3000/api/counties/1/users?limit=10&page=2"

# Get 20 users per page
curl "http://localhost:3000/api/counties/1/users?limit=20&page=1"
```

### **With Sorting:**
```bash
# Sort users by name (default)
curl "http://localhost:3000/api/counties/1/users?sort=name"

# Sort users by email
curl "http://localhost:3000/api/counties/1/users?sort=email"

# Sort users by creation date (newest first)
curl "http://localhost:3000/api/counties/1/users?sort=-createdAt"

# Sort users by creation date (oldest first)
curl "http://localhost:3000/api/counties/1/users?sort=createdAt"
```

### **Combined Parameters:**
```bash
# Get 5 users per page, page 2, sorted by email
curl "http://localhost:3000/api/counties/1/users?limit=5&page=2&sort=email"
```

## 📊 **Response Format**

### **Success Response (200):**
```json
{
  "county": {
    "id": "1",
    "name": "Nairobi",
    "code": "KE-047"
  },
  "users": [
    {
      "id": "1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "county": {
        "id": "1",
        "name": "Nairobi",
        "code": "KE-047"
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    },
    {
      "id": "2",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "county": {
        "id": "1",
        "name": "Nairobi",
        "code": "KE-047"
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalUsers": 25,
  "page": 1,
  "limit": 50,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPrevPage": false
}
```

### **Error Responses:**

**County ID Missing (400):**
```json
{
  "error": "County ID is required"
}
```

**County Not Found (404):**
```json
{
  "error": "County not found"
}
```

**Internal Server Error (500):**
```json
{
  "error": "Internal server error",
  "message": "Detailed error message"
}
```

## 🔄 **Complete CRUD Workflow**

### **1. Create County and Users:**
```bash
# Login to get token
TOKEN=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  | jq -r '.token')

# Create a county
COUNTY_RESPONSE=$(curl -s -X POST http://localhost:3000/api/counties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Nairobi",
    "code": "KE-047",
    "coordinates": {"latitude": -1.2921, "longitude": 36.8219},
    "description": "Kenya'\''s capital city"
  }')

COUNTY_ID=$(echo $COUNTY_RESPONSE | jq -r '.county.id')
echo "Created County ID: $COUNTY_ID"

# Create users in the county
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Alice Johnson",
    "county": '$COUNTY_ID'
  }'

curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Bob Smith",
    "county": '$COUNTY_ID'
  }'
```

### **2. Get Users in County:**
```bash
# Get all users in the county
curl "http://localhost:3000/api/counties/$COUNTY_ID/users"

# Get users with pagination
curl "http://localhost:3000/api/counties/$COUNTY_ID/users?limit=10&page=1"
```

## 📈 **Analytics Examples**

### **Get County Statistics:**
```bash
# Get user count for county
curl -s "http://localhost:3000/api/counties/1/users" | jq '{
  countyName: .county.name,
  countyCode: .county.code,
  totalUsers: .totalUsers,
  userNames: [.users[].name]
}'
```

### **Export Users:**
```bash
# Export all users in county to CSV format
curl -s "http://localhost:3000/api/counties/1/users?limit=1000" | \
jq -r '.users[] | [.name, .email, .county.name, .county.code] | @csv'
```

### **Get Multiple Counties:**
```bash
# Get users from multiple counties
for county_id in 1 2 3; do
  echo "County $county_id:"
  curl -s "http://localhost:3000/api/counties/$county_id/users" | \
  jq -r '.users[] | "  - \(.name) (\(.email))"'
  echo ""
done
```

## 🎯 **Frontend Integration**

### **JavaScript/React Example:**
```javascript
// Fetch users in a county
const fetchCountyUsers = async (countyId, page = 1, limit = 10) => {
  try {
    const response = await fetch(
      `/api/counties/${countyId}/users?page=${page}&limit=${limit}`
    )
    
    if (!response.ok) {
      throw new Error('Failed to fetch county users')
    }
    
    return await response.json()
  } catch (error) {
    console.error('Error fetching county users:', error)
    throw error
  }
}

// Usage
const countyUsers = await fetchCountyUsers(1, 1, 20)
console.log(`Found ${countyUsers.totalUsers} users in ${countyUsers.county.name}`)
```

### **React Component Example:**
```jsx
import { useState, useEffect } from 'react'

function CountyUsers({ countyId }) {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/counties/${countyId}/users`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch users')
        }
        
        const result = await response.json()
        setData(result)
      } catch (err) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    if (countyId) {
      fetchUsers()
    }
  }, [countyId])

  if (loading) return <div>Loading users...</div>
  if (error) return <div>Error: {error}</div>
  if (!data) return <div>No data</div>

  return (
    <div>
      <h2>Users in {data.county.name}</h2>
      <p>Total: {data.totalUsers} users</p>
      
      <ul>
        {data.users.map(user => (
          <li key={user.id}>
            {user.name} ({user.email})
          </li>
        ))}
      </ul>
      
      {data.hasNextPage && (
        <button onClick={() => {/* Load next page */}}>
          Load More
        </button>
      )}
    </div>
  )
}
```

## ✅ **Benefits of Standard REST Approach**

1. **Standard REST Convention**: `/counties/:id/users` follows REST best practices
2. **Clean URLs**: No complex query parameters in the URL
3. **Intuitive**: Easy to understand and use
4. **Cacheable**: Standard URLs are easier to cache
5. **Documentation Friendly**: Clear endpoint structure
6. **Frontend Friendly**: Easy to integrate with frontend frameworks

## 🧪 **Testing**

### **Quick Test:**
```bash
# Test the endpoint
curl -v "http://localhost:3000/api/counties/1/users"

# Test with pagination
curl -v "http://localhost:3000/api/counties/1/users?limit=5&page=1"

# Test non-existent county
curl -v "http://localhost:3000/api/counties/999/users"
```

### **Test Script:**
```bash
#!/bin/bash

echo "Testing County Users Endpoint"

# Test valid county
echo "1. Testing valid county:"
curl -s "http://localhost:3000/api/counties/1/users" | jq '.totalUsers'

# Test pagination
echo "2. Testing pagination:"
curl -s "http://localhost:3000/api/counties/1/users?limit=2&page=1" | jq '{totalUsers, page, limit}'

# Test invalid county
echo "3. Testing invalid county:"
curl -s "http://localhost:3000/api/counties/999/users" | jq '.error'
```

This standard REST endpoint provides a clean, intuitive way to get users by county! 🎉
