import { NextRequest, NextResponse } from 'next/server'
import payload from 'payload'

export async function GET(request: NextRequest) {
  try {
    // Test database connection by attempting to count users
    const result = await payload.find({
      collection: 'users',
      limit: 1,
    })

    // Get database connection info (without sensitive details)
    const dbUri = process.env.DATABASE_URI || ''
    const dbType = dbUri.startsWith('postgresql') ? 'PostgreSQL' : 'Unknown'
    const dbHost = dbUri.includes('@') ? dbUri.split('@')[1]?.split('/')[0] : 'localhost'

    return NextResponse.json({
      success: true,
      message: 'CMS is healthy',
      timestamp: new Date().toISOString(),
      database: {
        type: dbType,
        host: dbHost,
        connected: true,
        userCount: result.totalDocs,
      },
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    })
  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      success: false,
      message: 'CMS health check failed',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      database: {
        connected: false,
      },
    }, { status: 500 })
  }
}
