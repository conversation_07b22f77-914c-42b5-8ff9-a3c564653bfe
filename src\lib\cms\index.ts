// Export all CMS utilities, types, and hooks

// Types
export type * from './types'

// API Client
export { cmsAPI, CMSApiError } from './api'

// React Hooks
export {
  // Projects
  useProjects,
  useProject,
  useFeaturedProjects,
  useProjectsByCategory,
  
  // Success Stories
  useSuccessStories,
  useSuccessStory,
  useFeaturedSuccessStories,
  useSuccessStoriesByCategory,
  
  // Resources
  useResources,
  useResource,
  useFeaturedResources,
  useResourcesByType,
  useResourcesByCategory,
  
  // News
  useNews,
  useNewsArticle,
  useFeaturedNews,
  useUrgentNews,
  useNewsByCategory,
  
  // Media Gallery
  useMediaGallery,
  useMediaItem,
  useFeaturedMedia,
  useMediaByType,
  useMediaByCategory,
  
  // Investment Opportunities
  useInvestmentOpportunities,
  useInvestmentOpportunity,
  useFeaturedInvestmentOpportunities,
  useUrgentInvestmentOpportunities,
  useInvestmentOpportunitiesBySector,
  useInvestmentOpportunitiesByType,
  
  // Contact Form
  useContactForm,
} from './hooks'

// Utility Functions
export {
  // Media utilities
  getMediaUrl,
  getOptimizedImageUrl,
  getImageSrcSet,
  formatFileSize,
  
  // Date utilities
  formatDate,
  formatRelativeDate,
  isDateInFuture,
  isDateInPast,
  
  // Currency utilities
  formatCurrency,
  formatNumber,
  
  // Text utilities
  truncateText,
  stripHtml,
  extractExcerpt,
  slugify,
  
  // Content utilities
  getReadingTime,
  highlightSearchTerms,
  
  // Status utilities
  getStatusColor,
  getPriorityColor,
  
  // Category utilities
  getCategoryIcon,
  
  // Validation utilities
  isValidEmail,
  isValidPhone,
  isValidUrl,
  
  // Search utilities
  createSearchParams,
  parseSearchParams,
  
  // Content filtering utilities
  filterBySearchTerm,
  sortByDate,
  groupByCategory,
} from './utils'
