# County Users - Standard REST Curl Commands

## 🔗 **Standard REST Endpoint**

**GET /api/counties/:id/users**

Get users in a specific county using standard REST conventions.

## 📋 **Basic Commands**

### **Get All Users in County:**
```bash
# Get all users in county 1
curl "http://localhost:3000/api/counties/1/users"

# Get all users in county 2
curl "http://localhost:3000/api/counties/2/users"

# Get all users in county 3
curl "http://localhost:3000/api/counties/3/users"
```

### **With Pagination:**
```bash
# Get first 10 users in county 1
curl "http://localhost:3000/api/counties/1/users?limit=10&page=1"

# Get next 10 users in county 1
curl "http://localhost:3000/api/counties/1/users?limit=10&page=2"

# Get 5 users per page, page 3
curl "http://localhost:3000/api/counties/1/users?limit=5&page=3"
```

### **With Sorting:**
```bash
# Sort by name (default, ascending)
curl "http://localhost:3000/api/counties/1/users?sort=name"

# Sort by name (descending)
curl "http://localhost:3000/api/counties/1/users?sort=-name"

# Sort by email
curl "http://localhost:3000/api/counties/1/users?sort=email"

# Sort by creation date (newest first)
curl "http://localhost:3000/api/counties/1/users?sort=-createdAt"

# Sort by creation date (oldest first)
curl "http://localhost:3000/api/counties/1/users?sort=createdAt"
```

### **Combined Parameters:**
```bash
# 5 users per page, page 2, sorted by email
curl "http://localhost:3000/api/counties/1/users?limit=5&page=2&sort=email"

# 10 users per page, page 1, sorted by name descending
curl "http://localhost:3000/api/counties/1/users?limit=10&page=1&sort=-name"
```

## 📊 **Response Examples**

### **Success Response:**
```json
{
  "county": {
    "id": "1",
    "name": "Nairobi",
    "code": "KE-047"
  },
  "users": [
    {
      "id": "1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "county": {
        "id": "1",
        "name": "Nairobi",
        "code": "KE-047"
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalUsers": 25,
  "page": 1,
  "limit": 50,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPrevPage": false
}
```

### **Error Responses:**
```bash
# County not found (404)
curl "http://localhost:3000/api/counties/999/users"
# Response: {"error": "County not found"}

# Invalid county ID (400)
curl "http://localhost:3000/api/counties/abc/users"
# Response: {"error": "County ID is required"}
```

## 🧮 **Analytics Commands**

### **Get County Statistics:**
```bash
# Get user count and names for county 1
curl -s "http://localhost:3000/api/counties/1/users" | jq '{
  countyName: .county.name,
  countyCode: .county.code,
  totalUsers: .totalUsers,
  userNames: [.users[].name]
}'

# Get just the user count
curl -s "http://localhost:3000/api/counties/1/users" | jq '.totalUsers'

# Get county info
curl -s "http://localhost:3000/api/counties/1/users" | jq '.county'
```

### **Export Users to CSV:**
```bash
# Export all users in county 1 to CSV format
curl -s "http://localhost:3000/api/counties/1/users?limit=1000" | \
jq -r '.users[] | [.name, .email, .county.name, .county.code] | @csv'

# Export with headers
echo "Name,Email,County,Code"
curl -s "http://localhost:3000/api/counties/1/users?limit=1000" | \
jq -r '.users[] | [.name, .email, .county.name, .county.code] | @csv'
```

### **Multiple Counties Analysis:**
```bash
# Get user counts for multiple counties
for county_id in 1 2 3; do
  count=$(curl -s "http://localhost:3000/api/counties/$county_id/users" | jq '.totalUsers // 0')
  name=$(curl -s "http://localhost:3000/api/counties/$county_id/users" | jq -r '.county.name // "Unknown"')
  echo "County $county_id ($name): $count users"
done
```

## 🔄 **Complete Workflow Example**

```bash
#!/bin/bash

echo "County Users Workflow Example"
echo "=============================="

# Step 1: Login to get token
echo "1. Logging in..."
TOKEN=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  | jq -r '.token')

if [ "$TOKEN" = "null" ]; then
  echo "❌ Login failed"
  exit 1
fi

echo "✅ Login successful"

# Step 2: Create a county
echo "2. Creating county..."
COUNTY_RESPONSE=$(curl -s -X POST http://localhost:3000/api/counties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "Test County",
    "code": "TEST-001",
    "coordinates": {"latitude": 0, "longitude": 0},
    "description": "A test county"
  }')

COUNTY_ID=$(echo $COUNTY_RESPONSE | jq -r '.county.id')
echo "✅ Created county ID: $COUNTY_ID"

# Step 3: Create users in the county
echo "3. Creating users..."
for i in {1..3}; do
  curl -s -X POST http://localhost:3000/api/users \
    -H "Content-Type: application/json" \
    -d "{
      \"email\": \"user$<EMAIL>\",
      \"password\": \"password123\",
      \"name\": \"Test User $i\",
      \"county\": $COUNTY_ID
    }" > /dev/null
  echo "✅ Created user $i"
done

# Step 4: Get users in the county
echo "4. Getting users in county..."
curl -s "http://localhost:3000/api/counties/$COUNTY_ID/users" | jq '{
  county: .county.name,
  totalUsers: .totalUsers,
  users: [.users[].name]
}'

# Step 5: Test pagination
echo "5. Testing pagination (limit=2)..."
curl -s "http://localhost:3000/api/counties/$COUNTY_ID/users?limit=2&page=1" | jq '{
  page: .page,
  limit: .limit,
  totalUsers: .totalUsers,
  hasNextPage: .hasNextPage,
  users: [.users[].name]
}'

# Step 6: Cleanup
echo "6. Cleaning up..."
# Delete users (you'd need to get their IDs first)
# Delete county
curl -s -X DELETE "http://localhost:3000/api/counties/$COUNTY_ID" \
  -H "Authorization: Bearer $TOKEN" > /dev/null

echo "✅ Cleanup completed"
echo "🏁 Workflow completed successfully!"
```

## 🧪 **Testing Commands**

### **Quick Test:**
```bash
# Test if endpoint works
curl -v "http://localhost:3000/api/counties/1/users"

# Test with verbose output to see headers
curl -v "http://localhost:3000/api/counties/1/users?limit=5"
```

### **Performance Test:**
```bash
# Time the request
time curl -s "http://localhost:3000/api/counties/1/users" > /dev/null

# Test with large limit
curl -s "http://localhost:3000/api/counties/1/users?limit=1000" | jq '.users | length'
```

### **Error Testing:**
```bash
# Test various error conditions
echo "Testing error conditions:"

# Non-existent county
echo "1. Non-existent county:"
curl -s "http://localhost:3000/api/counties/999999/users" | jq '.error'

# Invalid county ID
echo "2. Invalid county ID:"
curl -s "http://localhost:3000/api/counties/abc/users" | jq '.error'

# Invalid parameters
echo "3. Invalid page number:"
curl -s "http://localhost:3000/api/counties/1/users?page=0" | jq '.error // "No error"'
```

## 📱 **Frontend Integration Examples**

### **JavaScript Fetch:**
```javascript
// Simple fetch
const getCountyUsers = async (countyId) => {
  const response = await fetch(`/api/counties/${countyId}/users`)
  return response.json()
}

// With pagination
const getCountyUsersPage = async (countyId, page = 1, limit = 10) => {
  const response = await fetch(
    `/api/counties/${countyId}/users?page=${page}&limit=${limit}`
  )
  return response.json()
}

// Usage
const users = await getCountyUsers(1)
console.log(`Found ${users.totalUsers} users in ${users.county.name}`)
```

### **Curl with jq Processing:**
```bash
# Get user emails only
curl -s "http://localhost:3000/api/counties/1/users" | jq -r '.users[].email'

# Get user names only
curl -s "http://localhost:3000/api/counties/1/users" | jq -r '.users[].name'

# Get formatted list
curl -s "http://localhost:3000/api/counties/1/users" | \
jq -r '.users[] | "\(.name) <\(.email)>"'
```

## ✅ **Benefits**

1. **Standard REST**: Follows `/resource/:id/subresource` pattern
2. **Clean URLs**: No complex query parameters for basic functionality
3. **Intuitive**: Easy to understand and remember
4. **Cacheable**: Standard URL structure is cache-friendly
5. **Pagination**: Built-in pagination support
6. **Sorting**: Flexible sorting options
7. **Error Handling**: Proper HTTP status codes and error messages

This standard REST endpoint provides a clean, professional API for accessing county users! 🎉
