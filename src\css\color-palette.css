/* NPI Platform Color Palette - Global Implementation */
:root {
  /* Primary 6-Color Palette */
  --npi-white: #FFFFFF;
  --npi-cream: #EFE3BA;
  --npi-brown: #725242;
  --npi-blue: #25718A;
  --npi-reddish-brown: #8A3E25;
  --npi-black: #000000;
  
  /* Extended Palette for UI Elements */
  --npi-cream-light: #F8F6F0;
  --npi-cream-medium: #F2EDE4;
  --npi-brown-light: #8A6240;
  --npi-brown-dark: #3E2A1B;
  --npi-blue-light: #3498DB;
  --npi-blue-dark: #1A5A6F;
  
  /* Background Alternation */
  --bg-white: var(--npi-white);
  --bg-cream: var(--npi-cream);
  --bg-cream-light: var(--npi-cream-light);
  
  /* Text Colors */
  --text-primary: var(--npi-black);
  --text-secondary: var(--npi-brown);
  --text-accent: var(--npi-blue);
  --text-muted: var(--npi-brown-light);
  
  /* Border Colors */
  --border-primary: var(--npi-brown);
  --border-secondary: var(--npi-cream);
  --border-accent: var(--npi-blue);
}

/* Light theme implementation */
[data-theme="light"] {
  --bg-primary: var(--npi-white);
  --bg-secondary: var(--npi-cream);
  --text-primary: var(--npi-black);
  --text-secondary: var(--npi-brown);
}

/* Component-specific color classes */
.npi-bg-white { background-color: var(--npi-white); }
.npi-bg-cream { background-color: var(--npi-cream); }
.npi-bg-brown { background-color: var(--npi-brown); }
.npi-bg-blue { background-color: var(--npi-blue); }
.npi-bg-reddish-brown { background-color: var(--npi-reddish-brown); }

.npi-text-white { color: var(--npi-white); }
.npi-text-cream { color: var(--npi-cream); }
.npi-text-brown { color: var(--npi-brown); }
.npi-text-blue { color: var(--npi-blue); }
.npi-text-reddish-brown { color: var(--npi-reddish-brown); }
.npi-text-black { color: var(--npi-black); }

/* Background alternation utilities */
.npi-section-white { background-color: var(--bg-white); }
.npi-section-cream { background-color: var(--bg-cream); }
.npi-section-cream-light { background-color: var(--bg-cream-light); }

/* Button styles with new palette */
.npi-button-primary {
  background-color: var(--npi-reddish-brown);
  color: var(--npi-white);
  border: 2px solid var(--npi-reddish-brown);
  transition: all 0.3s ease;
}

.npi-button-primary:hover {
  background-color: var(--npi-brown);
  border-color: var(--npi-brown);
}

.npi-button-secondary {
  background-color: var(--npi-brown);
  color: var(--npi-white);
  border: 2px solid var(--npi-brown);
  transition: all 0.3s ease;
}

.npi-button-secondary:hover {
  background-color: var(--npi-reddish-brown);
  border-color: var(--npi-reddish-brown);
}

.npi-button-outline {
  background-color: transparent;
  color: var(--npi-brown);
  border: 2px solid var(--npi-brown);
  transition: all 0.3s ease;
}

.npi-button-outline:hover {
  background-color: var(--npi-brown);
  color: var(--npi-white);
}

/* Card styles */
.npi-card {
  background-color: var(--npi-white);
  border: 1px solid var(--npi-cream);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.npi-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: var(--npi-cream-light);
}

/* Hero section styles */
.npi-hero-white {
  background: linear-gradient(135deg, var(--npi-white) 0%, var(--npi-cream-light) 100%);
}

.npi-hero-cream {
  background: linear-gradient(135deg, var(--npi-cream) 0%, var(--npi-white) 100%);
}

.npi-hero-brown {
  background: linear-gradient(135deg, var(--npi-brown) 0%, var(--npi-reddish-brown) 100%);
}

/* Responsive design */
@media (max-width: 768px) {
  .npi-section {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .npi-section {
    padding: 1.5rem 0.5rem;
  }
}
