# Create Admin User Scripts

This directory contains scripts to create admin users for the PayloadCMS application.

## 📋 Admin User Details

The scripts will create an admin user with the following credentials:

- **Name**: <PERSON>
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: admin

## 🚀 Usage Options

### Option 1: Node.js <PERSON>ript (Recommended)

```bash
# Make sure your development server is running
npm run dev

# In another terminal, run the script
node scripts/create-admin-user.js
```

### Option 2: <PERSON>ript

```bash
# Make the script executable (Linux/Mac)
chmod +x scripts/create-admin-user.sh

# Run the script
./scripts/create-admin-user.sh
```

### Option 3: Manual API Call

```bash
# First, login to get a token (replace with your existing admin credentials)
TOKEN=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' \
  | grep -o '"token":"[^"]*' | cut -d'"' -f4)

# Then create the admin user
curl -X POST http://localhost:3000/api/users \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Ivy Njoroge",
    "email": "<EMAIL>",
    "password": "admin123",
    "role": "admin",
    "_verified": true,
    "isActive": true,
    "preferences": {
      "newsletter": false,
      "notifications": true,
      "language": "en"
    }
  }'
```

## ⚙️ Prerequisites

1. **Development server must be running**:
   ```bash
   npm run dev
   ```

2. **Existing admin access**: You need existing admin credentials to create new admin users. The scripts use these default credentials:
   - Email: `<EMAIL>`
   - Password: `admin123`

3. **Update credentials if needed**: If your existing admin credentials are different, update them in the script files:
   - In `create-admin-user.js`: Update `EXISTING_ADMIN_EMAIL` and `EXISTING_ADMIN_PASSWORD`
   - In `create-admin-user.sh`: Update `EXISTING_ADMIN_EMAIL` and `EXISTING_ADMIN_PASSWORD`

## 🔍 What the Scripts Do

1. **Authentication**: Login using existing admin credentials to get an auth token
2. **Duplicate Check**: Verify that the user doesn't already exist
3. **User Creation**: Create the new admin user with the specified details
4. **Verification**: Confirm successful creation and display login details

## ✅ Expected Output

```
🚀 Admin User Creation Script
==============================
Creating admin user: Ivy Njoroge (<EMAIL>)

🔐 Logging in as existing admin...
✅ Login successful
🔍 Checking <NAME_EMAIL> already exists...
✅ User does not exist, proceeding with creation...
👤 Creating admin user: <EMAIL>...
✅ Admin user created successfully!
📧 Email: <EMAIL>
👤 Name: Ivy Njoroge
🔑 Role: admin
🆔 ID: 123

🎉 Admin user creation completed successfully!

📋 Login Details:
   Email: <EMAIL>
   Password: admin123
   Role: admin

🔗 You can now login at: http://localhost:3000/admin
```

## 🚨 Troubleshooting

### "Login failed" Error
- Verify your development server is running on `http://localhost:3000`
- Check that the existing admin credentials in the script are correct
- Ensure the existing admin user exists and has proper permissions

### "User already exists" Message
- This is normal if you've already run the script
- The script will skip creation and exit gracefully

### "Failed to create admin user" Error
- Check that the authenticated user has permission to create other users
- Verify the user data format matches the expected schema
- Check the server logs for more detailed error information

## 🔐 Security Notes

- **Change the password immediately** after first login
- The password `admin123` is only for initial setup
- Consider using environment variables for sensitive credentials in production
- These scripts are intended for development/setup purposes only

## 🔗 Next Steps

After creating the admin user:

1. Login at `http://localhost:3000/admin`
2. Use the credentials: `<EMAIL>` / `admin123`
3. **Change the password immediately** for security
4. Configure additional user settings as needed
