#!/usr/bin/env node

/**
 * Comprehensive Database Setup Verification Script
 * 
 * This script performs a complete verification of the database setup,
 * including MongoDB connection, PayloadCMS integration, and API endpoints.
 */

import { MongoClient } from 'mongodb'
import fetch from 'node-fetch'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

dotenv.config({ path: join(__dirname, '..', '.env.local') })

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green)
}

function logError(message) {
  log(`❌ ${message}`, colors.red)
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow)
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue)
}

function logHeader(message) {
  log(`\n${'='.repeat(60)}`, colors.bright)
  log(message, colors.bright)
  log('='.repeat(60), colors.bright)
}

function logSubHeader(message) {
  log(`\n${'-'.repeat(40)}`, colors.cyan)
  log(message, colors.cyan)
  log('-'.repeat(40), colors.cyan)
}

async function testMongoDBConnection() {
  logSubHeader('Testing MongoDB Connection')
  
  const uri = process.env.DATABASE_URI
  
  if (!uri) {
    logError('DATABASE_URI environment variable is not set')
    return false
  }

  logInfo(`Connection URI: ${uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`)

  const client = new MongoClient(uri)
  
  try {
    await client.connect()
    logSuccess('Connected to MongoDB successfully!')
    
    const db = client.db()
    const dbName = db.databaseName
    logInfo(`Database name: ${dbName}`)
    
    // List collections
    const collections = await db.listCollections().toArray()
    if (collections.length > 0) {
      logSuccess(`Found ${collections.length} collections:`)
      collections.forEach(collection => {
        log(`  📁 ${collection.name}`, colors.cyan)
      })
    } else {
      logWarning('No collections found (database may be empty)')
    }
    
    // Test write/read operations
    const testCollection = db.collection('connection_test')
    const testDoc = {
      timestamp: new Date(),
      test: 'Database verification test',
      success: true
    }
    
    await testCollection.insertOne(testDoc)
    const retrievedDoc = await testCollection.findOne({ test: 'Database verification test' })
    await testCollection.deleteOne({ _id: testDoc._id })
    
    if (retrievedDoc) {
      logSuccess('Read/Write operations working correctly')
    }
    
    return true
    
  } catch (error) {
    logError(`MongoDB connection failed: ${error.message}`)
    return false
  } finally {
    await client.close()
  }
}

async function testAPIEndpoints() {
  logSubHeader('Testing API Endpoints')
  
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'
  logInfo(`Testing API at: ${baseUrl}`)
  
  try {
    // Test health endpoint
    logInfo('Testing health endpoint...')
    const healthResponse = await fetch(`${baseUrl}/api/health`)
    
    if (!healthResponse.ok) {
      logError(`Health endpoint failed: ${healthResponse.status}`)
      logWarning('Make sure the development server is running (npm run dev)')
      return false
    }
    
    const healthData = await healthResponse.json()
    if (healthData.success) {
      logSuccess('Health endpoint working')
      logInfo(`Database connected: ${healthData.database.connected}`)
      logInfo(`User count: ${healthData.database.userCount}`)
    }
    
    // Test contact form endpoint
    logInfo('Testing contact form submission...')
    const contactData = {
      name: 'Database Verification Test',
      email: '<EMAIL>',
      subject: 'Database Setup Verification',
      category: 'general',
      message: 'This is a test message to verify the contact form API is working.',
      priority: 'medium'
    }
    
    const contactResponse = await fetch(`${baseUrl}/api/contact-submissions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(contactData),
    })
    
    const contactResult = await contactResponse.json()
    
    if (contactResponse.ok && contactResult.success) {
      logSuccess('Contact form API working')
      logInfo(`Submission ID: ${contactResult.submissionId}`)
    } else {
      logError(`Contact form API failed: ${contactResult.error || contactResult.message}`)
      return false
    }
    
    // Test other public endpoints
    const endpoints = [
      '/api/projects',
      '/api/success-stories',
      '/api/resources',
      '/api/news',
      '/api/counties',
    ]
    
    logInfo('Testing other public endpoints...')
    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${baseUrl}${endpoint}`)
        if (response.ok) {
          const data = await response.json()
          log(`  ✅ ${endpoint}: ${data.docs?.length || 0} items`, colors.green)
        } else {
          log(`  ❌ ${endpoint}: ${response.status}`, colors.red)
        }
      } catch (error) {
        log(`  ❌ ${endpoint}: Network error`, colors.red)
      }
    }
    
    return true
    
  } catch (error) {
    logError(`API test failed: ${error.message}`)
    return false
  }
}

async function checkEnvironmentSetup() {
  logSubHeader('Checking Environment Setup')
  
  const requiredEnvVars = [
    'DATABASE_URI',
    'PAYLOAD_SECRET',
    'NEXT_PUBLIC_API_URL'
  ]
  
  let allEnvVarsSet = true
  
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      logSuccess(`${envVar} is set`)
    } else {
      logError(`${envVar} is missing`)
      allEnvVarsSet = false
    }
  }
  
  return allEnvVarsSet
}

async function main() {
  logHeader('NPI Website Database Setup Verification')
  
  let allTestsPassed = true
  
  // Check environment setup
  const envSetup = await checkEnvironmentSetup()
  if (!envSetup) {
    allTestsPassed = false
  }
  
  // Test MongoDB connection
  const mongoTest = await testMongoDBConnection()
  if (!mongoTest) {
    allTestsPassed = false
  }
  
  // Test API endpoints (only if MongoDB is working)
  if (mongoTest) {
    const apiTest = await testAPIEndpoints()
    if (!apiTest) {
      allTestsPassed = false
    }
  }
  
  // Final summary
  logHeader('Verification Summary')
  
  if (allTestsPassed) {
    logSuccess('🎉 All tests passed! Database setup is working correctly.')
    log('\nYour NPI website is ready with:', colors.green)
    log('  ✅ MongoDB connection working', colors.green)
    log('  ✅ PayloadCMS integration working', colors.green)
    log('  ✅ Contact form API working', colors.green)
    log('  ✅ Public API endpoints accessible', colors.green)
    
    log('\nNext steps:', colors.cyan)
    log('  1. Visit http://localhost:3000/admin to access the CMS', colors.cyan)
    log('  2. Create content through the admin interface', colors.cyan)
    log('  3. Test the contact form on your website', colors.cyan)
    
  } else {
    logError('❌ Some tests failed. Please check the errors above.')
    log('\nTroubleshooting tips:', colors.yellow)
    log('  1. Ensure .env.local file has correct DATABASE_URI', colors.yellow)
    log('  2. Check MongoDB Atlas cluster is running and accessible', colors.yellow)
    log('  3. Verify IP address is whitelisted in MongoDB Atlas', colors.yellow)
    log('  4. Make sure development server is running (npm run dev)', colors.yellow)
    log('  5. Check server logs for detailed error messages', colors.yellow)
    
    process.exit(1)
  }
}

// Run the verification
main().catch(error => {
  logError(`Verification failed: ${error.message}`)
  process.exit(1)
})
