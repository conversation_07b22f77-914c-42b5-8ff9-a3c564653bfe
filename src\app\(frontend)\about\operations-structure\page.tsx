import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import { NPIScrollToTop } from '@/components/ui/npi-scroll-to-top'
import PageClient from './page.client'

// Operations structure sub-page components
// Uses: NPIOperationsHeroBlock, NPIOperationsStructureBlock from about/operations-structure

export const metadata: Metadata = {
  title: 'Operations & Structure - About NPI',
  description:
    "Learn about NPI's operational structure, multi-agency framework, and implementing partners working together to drive sustainable development.",
}

const operationsStructurePageLayout = [
  {
    blockType: 'npiOperationsHero' as const,
  },
  {
    blockType: 'npiOperationsStructure' as const,
  },
]

export default function OperationsStructurePage() {
  return (
    <>
      <PageClient />
      <article className="pb-12" style={{ scrollBehavior: 'smooth' }}>
        {operationsStructurePageLayout.map((block, index) => (
          <section
            key={index}
            className={`
              ${index === 0 ? '' : '-mt-1'}
              relative
              ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
            `}
          >
            <RenderBlocks blocks={[block]} />
          </section>
        ))}
      </article>
      <NPIScrollToTop showAfter={400} />
    </>
  )
}
