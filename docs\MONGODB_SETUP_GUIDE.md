# MongoDB Setup Guide for NPI Website

## Overview

This guide covers the complete setup of MongoDB for the Natural Products Institute website, including local development, MongoDB Atlas cloud setup, and production deployment.

## 🚀 Quick Start

### 1. Environment Configuration

Update your `.env.local` file with MongoDB connection string:

```env
# MongoDB Configuration
DATABASE_URI=mongodb://localhost:27017/npi-cms

# For MongoDB Atlas (recommended for production)
# DATABASE_URI=mongodb+srv://username:<EMAIL>/npi-cms?retryWrites=true&w=majority

# PayloadCMS Configuration
PAYLOAD_SECRET=your-super-secure-secret-key-min-32-chars
JWT_SECRET=your-jwt-secret-key-min-32-chars

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:3000
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3000

# File Storage
BLOB_READ_WRITE_TOKEN=your-vercel-blob-token
```

### 2. Install Dependencies

The MongoDB adapter has been added to your project:

```bash
pnpm install
```

### 3. Start Development

```bash
pnpm dev
```

## 🗄️ Local MongoDB Setup

### Option 1: MongoDB Community Server (Recommended for Development)

#### Windows Installation

1. Download MongoDB Community Server from [MongoDB Download Center](https://www.mongodb.com/try/download/community)
2. Run the installer and follow the setup wizard
3. Choose "Complete" installation
4. Install MongoDB as a Windows Service
5. Install MongoDB Compass (GUI tool) when prompted

#### macOS Installation

```bash
# Using Homebrew
brew tap mongodb/brew
brew install mongodb-community

# Start MongoDB service
brew services start mongodb/brew/mongodb-community
```

#### Linux (Ubuntu/Debian) Installation

```bash
# Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list

# Update package database
sudo apt update

# Install MongoDB
sudo apt install -y mongodb-org

# Start MongoDB service
sudo systemctl start mongod
sudo systemctl enable mongod

# Verify installation
sudo systemctl status mongod
```

### Option 2: Docker Setup

Use the existing `docker-compose.yml` file:

```bash
# Start MongoDB with Docker
docker-compose up -d mongo

# View logs
docker-compose logs mongo
```

### Verify Local Installation

```bash
# Connect to MongoDB shell
mongosh

# Show databases
show dbs

# Exit
exit
```

## ☁️ MongoDB Atlas Cloud Setup

### 1. Create Atlas Account

1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Sign up for a free account
3. Verify your email address

### 2. Create a Cluster

1. Click "Create a New Cluster"
2. Choose "M0 Sandbox" (Free tier)
3. Select your preferred cloud provider and region
4. Name your cluster (e.g., "npi-website-cluster")
5. Click "Create Cluster"

### 3. Configure Database Access

1. Go to "Database Access" in the left sidebar
2. Click "Add New Database User"
3. Choose "Password" authentication
4. Create a username and strong password
5. Set database user privileges to "Read and write to any database"
6. Click "Add User"

### 4. Configure Network Access

1. Go to "Network Access" in the left sidebar
2. Click "Add IP Address"
3. For development: Click "Allow Access from Anywhere" (0.0.0.0/0)
4. For production: Add your server's specific IP addresses
5. Click "Confirm"

### 5. Get Connection String

1. Go to "Clusters" and click "Connect" on your cluster
2. Choose "Connect your application"
3. Select "Node.js" and version "4.1 or later"
4. Copy the connection string
5. Replace `<password>` with your database user password
6. Replace `<dbname>` with your database name (e.g., "npi-cms")

Example connection string:
```
mongodb+srv://npi-user:<EMAIL>/npi-cms?retryWrites=true&w=majority
```

## 🔧 Configuration Details

### PayloadCMS Configuration

The MongoDB adapter is configured in `src/payload.config.ts`:

```typescript
import { mongooseAdapter } from '@payloadcms/db-mongodb'

export default buildConfig({
  // ... other config
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
  }),
  // ... rest of config
})
```

### Environment Variables

Create different environment files for different stages:

#### `.env.local` (Development)
```env
DATABASE_URI=mongodb://localhost:27017/npi-cms-dev
```

#### `.env.production` (Production)
```env
DATABASE_URI=mongodb+srv://username:<EMAIL>/npi-cms?retryWrites=true&w=majority
```

#### `.env.test` (Testing)
```env
DATABASE_URI=mongodb://localhost:27017/npi-cms-test
```

## 📊 Database Collections

PayloadCMS will automatically create these collections:

### Core Collections
- `users` - User accounts and authentication
- `media` - File uploads and media assets
- `pages` - Website pages content
- `posts` - Blog posts and articles

### CMS Collections
- `projects` - Project data and metadata
- `success_stories` - Success story content
- `resources` - Resource files and metadata
- `news` - News articles and updates
- `media_gallery` - Media gallery items
- `partnerships` - Partnership information
- `investment_opportunities` - Investment data
- `partners` - Partner information
- `contact_submissions` - Contact form submissions

### System Collections
- `payload_preferences` - User preferences
- `payload_migrations` - Schema migration history

## 🛠️ Development Tools

### MongoDB Compass

MongoDB Compass is a GUI tool for MongoDB:

1. Download from [MongoDB Compass](https://www.mongodb.com/products/compass)
2. Connect using your connection string
3. Browse collections, run queries, and analyze data

### MongoDB Shell (mongosh)

Command-line interface for MongoDB:

```bash
# Connect to local MongoDB
mongosh

# Connect to Atlas
mongosh "mongodb+srv://cluster.mongodb.net/npi-cms" --username your-username

# Common commands
show dbs                    # List databases
use npi-cms                # Switch to database
show collections           # List collections
db.users.find()           # Query users collection
db.projects.countDocuments() # Count documents
```

## 🚀 Deployment

### Vercel Deployment

1. Add your MongoDB Atlas connection string to Vercel environment variables:
   ```
   DATABASE_URI=mongodb+srv://username:<EMAIL>/npi-cms?retryWrites=true&w=majority
   ```

2. Deploy your application:
   ```bash
   vercel --prod
   ```

### Other Platforms

For other deployment platforms, ensure you:
1. Set the `DATABASE_URI` environment variable
2. Whitelist your server's IP address in MongoDB Atlas
3. Use connection pooling for production workloads

## 🔍 Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check network access settings in Atlas
   - Verify IP whitelist includes your current IP
   - Ensure connection string is correct

2. **Authentication Failed**
   - Verify username and password
   - Check database user permissions
   - Ensure special characters in password are URL-encoded

3. **Database Not Found**
   - MongoDB creates databases automatically when first document is inserted
   - Verify database name in connection string matches your application

### Debug Connection

Create a test script to verify connection:

```javascript
// test-connection.js
const { MongoClient } = require('mongodb');

async function testConnection() {
  const uri = process.env.DATABASE_URI;
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB successfully!');
    
    const db = client.db();
    const collections = await db.listCollections().toArray();
    console.log('📁 Collections:', collections.map(c => c.name));
    
  } catch (error) {
    console.error('❌ Connection failed:', error);
  } finally {
    await client.close();
  }
}

testConnection();
```

Run with: `node test-connection.js`

## 📈 Performance Optimization

### Indexing

MongoDB automatically creates indexes for PayloadCMS collections. For custom queries, consider adding indexes:

```javascript
// In MongoDB shell
db.projects.createIndex({ "title": "text", "description": "text" })
db.news.createIndex({ "publishedDate": -1 })
db.users.createIndex({ "email": 1 }, { unique: true })
```

### Connection Pooling

For production, configure connection pooling in your MongoDB connection:

```typescript
db: mongooseAdapter({
  url: process.env.DATABASE_URI || '',
  connectOptions: {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
  },
}),
```

## 🔐 Security Best Practices

1. **Use Strong Passwords**: Generate complex passwords for database users
2. **Limit Network Access**: Only whitelist necessary IP addresses
3. **Use SSL/TLS**: Always use encrypted connections (Atlas enforces this)
4. **Regular Backups**: Enable automated backups in Atlas
5. **Monitor Access**: Review database access logs regularly
6. **Rotate Credentials**: Periodically update database passwords

## 📚 Additional Resources

- [MongoDB Documentation](https://docs.mongodb.com/)
- [PayloadCMS MongoDB Adapter](https://payloadcms.com/docs/database/mongodb)
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)
- [Mongoose Documentation](https://mongoosejs.com/docs/)
