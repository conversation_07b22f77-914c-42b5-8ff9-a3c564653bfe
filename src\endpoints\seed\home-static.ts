import type { RequiredDataFromCollectionSlug } from 'payload'

// Used for pre-seeded content so that the homepage is not empty
export const homeStatic: RequiredDataFromCollectionSlug<'pages'> = {
  slug: 'home',
  _status: 'published',
  hero: {
    type: 'npiHero',
  },
  meta: {
    description:
      'Harnessing Indigenous Wealth for Sustainable Growth - Natural Products Industry Initiative Kenya',
    title: 'Natural Products Industry Initiative - Kenya',
  },
  title: 'Home',
  layout: [
    {
      blockType: 'npiFeaturedProjects',
    },
    {
      blockType: 'npiSuccessStories',
    },
    {
      blockType: 'npiLatestUpdates',
    },
  ],
}
