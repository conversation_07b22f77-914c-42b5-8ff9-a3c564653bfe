# NPI CMS Testing Guide

## Overview

This guide covers testing strategies and practices for the Natural Products Institute CMS. The testing suite includes unit tests, integration tests, and API tests to ensure system reliability and maintainability.

## Testing Stack

- **Test Runner**: Jest
- **Testing Library**: @testing-library/react (for React components)
- **API Testing**: Supertest with in-memory MongoDB
- **Mocking**: Jest mocks and MongoDB Memory Server
- **Coverage**: Jest coverage reports

## Test Structure

```
__tests__/
├── api/                    # API endpoint tests
│   ├── projects.test.ts
│   ├── success-stories.test.ts
│   └── ...
├── lib/                    # Library and utility tests
│   ├── cms/
│   │   ├── utils.test.ts
│   │   └── hooks.test.ts
│   └── validation.test.ts
├── components/             # React component tests
├── helpers/                # Test helper functions
│   └── initPayloadTest.ts
└── setup/                  # Test setup files
```

## Running Tests

### All Tests
```bash
npm test
```

### Watch Mode
```bash
npm run test:watch
```

### Coverage Report
```bash
npm run test:coverage
```

### Specific Test Files
```bash
# Run API tests only
npm test -- __tests__/api/

# Run specific test file
npm test -- __tests__/api/projects.test.ts

# Run tests matching pattern
npm test -- --testNamePattern="should create project"
```

## Test Categories

### 1. Unit Tests

Test individual functions and utilities in isolation.

**Example: Utility Function Test**
```typescript
import { formatCurrency, slugify } from '@/lib/cms/utils'

describe('formatCurrency', () => {
  it('should format KES currency correctly', () => {
    expect(formatCurrency(1500000, 'KES')).toContain('1,500,000')
  })
  
  it('should handle undefined amounts', () => {
    expect(formatCurrency(undefined)).toBe('')
  })
})

describe('slugify', () => {
  it('should create valid slug from title', () => {
    expect(slugify('Test Project Title!')).toBe('test-project-title')
  })
})
```

### 2. Integration Tests

Test API endpoints with database operations.

**Example: API Integration Test**
```typescript
import { initPayloadTest, createTestProject } from '../helpers/initPayloadTest'

describe('Projects API Integration', () => {
  let payload

  beforeAll(async () => {
    payload = await initPayloadTest({ __dirname })
  })

  it('should create and retrieve project', async () => {
    const projectData = {
      title: 'Integration Test Project',
      summary: 'Test project for integration testing',
      category: 'community-empowerment',
      // ... other required fields
    }

    const created = await payload.create({
      collection: 'projects',
      data: projectData,
    })

    expect(created.title).toBe(projectData.title)
    expect(created.id).toBeDefined()

    const retrieved = await payload.findByID({
      collection: 'projects',
      id: created.id,
    })

    expect(retrieved.title).toBe(projectData.title)
  })
})
```

### 3. Validation Tests

Test input validation and sanitization.

**Example: Validation Test**
```typescript
import { projectSchema, ValidationError, validateInput } from '@/lib/validation'

describe('Project Validation', () => {
  it('should validate correct project data', () => {
    const validData = {
      title: 'Valid Project',
      summary: 'Valid summary',
      category: 'community-empowerment',
      // ... other required fields
    }

    expect(() => validateInput(projectSchema, validData)).not.toThrow()
  })

  it('should reject invalid category', () => {
    const invalidData = {
      title: 'Invalid Project',
      category: 'invalid-category',
    }

    expect(() => validateInput(projectSchema, invalidData))
      .toThrow(ValidationError)
  })
})
```

### 4. Component Tests

Test React components and hooks.

**Example: Hook Test**
```typescript
import { renderHook, waitFor } from '@testing-library/react'
import { useProjects } from '@/lib/cms/hooks'

// Mock the API
jest.mock('@/lib/cms/api', () => ({
  cmsAPI: {
    projects: {
      getAll: jest.fn().mockResolvedValue({
        data: [testUtils.createMockProject()],
        totalDocs: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
        hasNextPage: false,
        hasPrevPage: false,
      }),
    },
  },
}))

describe('useProjects hook', () => {
  it('should fetch projects successfully', async () => {
    const { result } = renderHook(() => useProjects())

    expect(result.current.loading).toBe(true)

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toHaveLength(1)
    expect(result.current.error).toBeNull()
  })
})
```

## Test Helpers

### Database Setup

Use `initPayloadTest` helper for database operations:

```typescript
import { initPayloadTest, cleanupPayloadTest } from '../helpers/initPayloadTest'

describe('Database Tests', () => {
  let payload

  beforeAll(async () => {
    payload = await initPayloadTest({
      __dirname,
      mongoURL: 'mongodb://localhost:27017/test-db',
    })
  })

  afterAll(async () => {
    await cleanupPayloadTest(payload)
  })

  beforeEach(async () => {
    // Clean collections before each test
    await payload.delete({
      collection: 'projects',
      where: {},
    })
  })
})
```

### Mock Data Creation

Use helper functions to create consistent test data:

```typescript
import { 
  createTestProject, 
  createTestUser, 
  createTestSuccessStory 
} from '../helpers/initPayloadTest'

describe('Data Creation Tests', () => {
  it('should create test project with defaults', async () => {
    const project = await createTestProject(payload)
    expect(project.title).toBe('Test Project')
    expect(project.category).toBe('community-empowerment')
  })

  it('should create test project with overrides', async () => {
    const project = await createTestProject(payload, {
      title: 'Custom Project',
      featured: true,
    })
    expect(project.title).toBe('Custom Project')
    expect(project.featured).toBe(true)
  })
})
```

## Mocking Strategies

### API Mocking

Mock external API calls:

```typescript
// Mock fetch globally
global.fetch = jest.fn()

beforeEach(() => {
  fetch.mockClear()
})

it('should handle API errors', async () => {
  fetch.mockRejectedValueOnce(new Error('Network error'))
  
  const { result } = renderHook(() => useProjects())
  
  await waitFor(() => {
    expect(result.current.error).toBe('Network error')
  })
})
```

### PayloadCMS Mocking

Mock Payload operations:

```typescript
import payload from 'payload'

jest.mock('payload')
const mockPayload = payload as jest.Mocked<typeof payload>

beforeEach(() => {
  mockPayload.find.mockClear()
  mockPayload.create.mockClear()
})

it('should call payload.find with correct parameters', async () => {
  mockPayload.find.mockResolvedValue({
    docs: [testUtils.createMockProject()],
    totalDocs: 1,
  })

  await projectsAPI.getAll({ featured: true })

  expect(mockPayload.find).toHaveBeenCalledWith({
    collection: 'projects',
    where: { featured: { equals: true } },
    limit: 20,
    page: 1,
  })
})
```

## Test Data Management

### Fixtures

Create reusable test data:

```typescript
// __tests__/fixtures/projects.ts
export const validProjectData = {
  title: 'Test Project',
  description: {
    root: {
      children: [],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  },
  summary: 'Test project summary',
  category: 'community-empowerment',
  pillar: 'community-innovation',
  status: 'active',
  timeline: {
    startDate: '2024-01-01T00:00:00.000Z',
  },
  slug: 'test-project',
}

export const invalidProjectData = {
  summary: 'Missing required fields',
}
```

### Factory Functions

Create data factories for complex objects:

```typescript
// __tests__/factories/projectFactory.ts
export const createProject = (overrides = {}) => ({
  id: `project-${Date.now()}`,
  title: 'Test Project',
  summary: 'Test summary',
  category: 'community-empowerment',
  status: 'active',
  featured: false,
  published: true,
  slug: `test-project-${Date.now()}`,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})
```

## Coverage Requirements

### Minimum Coverage Targets

- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

### Coverage Reports

Generate coverage reports:

```bash
# HTML report (opens in browser)
npm run test:coverage

# Text report in terminal
npm test -- --coverage --coverageReporters=text

# JSON report for CI/CD
npm test -- --coverage --coverageReporters=json
```

### Excluding Files from Coverage

Update `jest.config.js`:

```javascript
collectCoverageFrom: [
  'src/**/*.{js,jsx,ts,tsx}',
  '!src/**/*.d.ts',
  '!src/**/*.stories.{js,jsx,ts,tsx}',
  '!src/**/index.{js,jsx,ts,tsx}',
  '!src/payload.config.ts',
]
```

## Continuous Integration

### GitHub Actions

Example workflow for automated testing:

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:6
        ports:
          - 27017:27017
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test -- --coverage --watchAll=false
        env:
          DATABASE_URI: mongodb://localhost:27017/test-db
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## Best Practices

### 1. Test Organization

- Group related tests using `describe` blocks
- Use descriptive test names that explain the expected behavior
- Follow the AAA pattern: Arrange, Act, Assert

### 2. Test Independence

- Each test should be independent and not rely on other tests
- Clean up data between tests
- Use `beforeEach` and `afterEach` for setup and teardown

### 3. Mocking Guidelines

- Mock external dependencies (APIs, databases, file systems)
- Don't mock the code you're testing
- Use real implementations for integration tests

### 4. Assertion Quality

- Use specific assertions rather than generic ones
- Test both positive and negative cases
- Include edge cases and error conditions

### 5. Performance

- Use `beforeAll` for expensive setup operations
- Clean up resources in `afterAll`
- Consider using `test.concurrent` for independent tests

## Debugging Tests

### Running Single Tests

```bash
# Run specific test file
npm test -- projects.test.ts

# Run specific test case
npm test -- --testNamePattern="should create project"

# Run in debug mode
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Debugging Tips

1. Use `console.log` for debugging (remove before committing)
2. Use `fit` or `fdescribe` to focus on specific tests
3. Use `xit` or `xdescribe` to skip tests temporarily
4. Check test output for detailed error messages

## Common Issues

### 1. Async Test Issues

```typescript
// ❌ Wrong - missing await
it('should fetch data', () => {
  const result = fetchData()
  expect(result).toBeDefined()
})

// ✅ Correct - with await
it('should fetch data', async () => {
  const result = await fetchData()
  expect(result).toBeDefined()
})
```

### 2. Mock Cleanup

```typescript
// ❌ Wrong - mocks persist between tests
describe('API Tests', () => {
  it('should handle success', () => {
    fetch.mockResolvedValue({ ok: true })
    // test code
  })
  
  it('should handle error', () => {
    // Previous mock still active!
  })
})

// ✅ Correct - clean mocks
describe('API Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })
})
```

### 3. Database State

```typescript
// ❌ Wrong - tests affect each other
describe('Database Tests', () => {
  it('should create project', async () => {
    await payload.create({ collection: 'projects', data: projectData })
    // Project remains in database
  })
  
  it('should find projects', async () => {
    // Previous project still exists!
  })
})

// ✅ Correct - clean state
describe('Database Tests', () => {
  beforeEach(async () => {
    await payload.delete({ collection: 'projects', where: {} })
  })
})
```

---

For more information on testing specific components or features, refer to the individual test files in the `__tests__` directory.
