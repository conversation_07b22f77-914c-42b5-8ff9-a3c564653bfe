import type { CollectionBeforeValidateHook, CollectionBeforeChangeHook } from 'payload'

/**
 * Enhanced validation hook that provides comprehensive data validation
 * and error handling for all collections
 */
export const enhancedValidation: CollectionBeforeValidateHook = ({ data, operation, req, collection }) => {
  try {
    const collectionSlug = collection?.config?.slug

    // Common validation rules
    if (operation === 'create' || operation === 'update') {
      
      // Validate required fields based on collection type
      if (collectionSlug === 'projects') {
        validateProjectData(data)
      } else if (collectionSlug === 'news') {
        validateNewsData(data)
      } else if (collectionSlug === 'success-stories') {
        validateSuccessStoryData(data)
      } else if (collectionSlug === 'resources') {
        validateResourceData(data)
      } else if (collectionSlug === 'events') {
        validateEventData(data)
      } else if (collectionSlug === 'contact-submissions') {
        validateContactSubmissionData(data)
      }

      // Common validations for all collections
      validateCommonFields(data)
    }

    req.payload.logger.info(`Validation passed for ${collectionSlug}: ${operation}`)
    
  } catch (error) {
    req.payload.logger.error(`Validation failed for ${collection?.config?.slug}:`, error)
    throw error
  }

  return data
}

/**
 * Enhanced error handling hook
 */
export const enhancedErrorHandling: CollectionBeforeChangeHook = ({ data, operation, req, collection }) => {
  try {
    // Sanitize data to prevent XSS and injection attacks
    sanitizeData(data)
    
    // Log the operation for audit purposes
    req.payload.logger.info(`${operation} operation on ${collection?.config?.slug}`, {
      operation,
      collection: collection?.config?.slug,
      user: req.user?.email || 'anonymous',
      timestamp: new Date().toISOString(),
    })

  } catch (error) {
    req.payload.logger.error(`Error handling failed for ${collection?.config?.slug}:`, error)
    throw new Error(`Failed to process ${operation} operation: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }

  return data
}

// Collection-specific validation functions
function validateProjectData(data: any) {
  if (!data.title || data.title.trim().length === 0) {
    throw new Error('Project title is required')
  }
  
  if (!data.description) {
    throw new Error('Project description is required')
  }
  
  if (!data.category) {
    throw new Error('Project category is required')
  }
  
  if (!data.status) {
    throw new Error('Project status is required')
  }
  
  if (data.timeline?.startDate && data.timeline?.endDate) {
    const startDate = new Date(data.timeline.startDate)
    const endDate = new Date(data.timeline.endDate)
    if (endDate < startDate) {
      throw new Error('Project end date cannot be before start date')
    }
  }
  
  if (data.budget?.totalBudget && data.budget.totalBudget < 0) {
    throw new Error('Project budget cannot be negative')
  }
}

function validateNewsData(data: any) {
  if (!data.title || data.title.trim().length === 0) {
    throw new Error('News title is required')
  }
  
  if (!data.content) {
    throw new Error('News content is required')
  }
  
  if (!data.category) {
    throw new Error('News category is required')
  }
  
  if (data.publishDate && new Date(data.publishDate) > new Date()) {
    // Allow future dates for scheduled publishing
    data.status = 'scheduled'
  }
}

function validateSuccessStoryData(data: any) {
  if (!data.title || data.title.trim().length === 0) {
    throw new Error('Success story title is required')
  }
  
  if (!data.content) {
    throw new Error('Success story content is required')
  }
  
  if (!data.summary || data.summary.length > 300) {
    throw new Error('Success story summary is required and must be 300 characters or less')
  }
}

function validateResourceData(data: any) {
  if (!data.title || data.title.trim().length === 0) {
    throw new Error('Resource title is required')
  }
  
  if (!data.type) {
    throw new Error('Resource type is required')
  }
  
  if (data.type === 'file' && !data.file) {
    throw new Error('File is required for file-type resources')
  }
  
  if (data.type === 'link' && !data.externalUrl) {
    throw new Error('External URL is required for link-type resources')
  }
}

function validateEventData(data: any) {
  if (!data.title || data.title.trim().length === 0) {
    throw new Error('Event title is required')
  }
  
  if (!data.date) {
    throw new Error('Event date is required')
  }
  
  if (!data.type) {
    throw new Error('Event type is required')
  }
  
  // Validate date is not in the past for new events
  if (new Date(data.date) < new Date() && !data.allowPastDate) {
    throw new Error('Event date cannot be in the past')
  }
}

function validateContactSubmissionData(data: any) {
  if (!data.name || data.name.trim().length === 0) {
    throw new Error('Name is required')
  }
  
  if (!data.email) {
    throw new Error('Email is required')
  }
  
  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(data.email)) {
    throw new Error('Invalid email format')
  }
  
  if (!data.message || data.message.trim().length === 0) {
    throw new Error('Message is required')
  }
  
  if (!data.category) {
    throw new Error('Category is required')
  }
}

function validateCommonFields(data: any) {
  // Validate URLs if present
  if (data.url && !isValidUrl(data.url)) {
    throw new Error('Invalid URL format')
  }
  
  if (data.externalUrl && !isValidUrl(data.externalUrl)) {
    throw new Error('Invalid external URL format')
  }
  
  // Validate email fields
  if (data.email && !isValidEmail(data.email)) {
    throw new Error('Invalid email format')
  }
  
  // Validate phone numbers if present
  if (data.phone && !isValidPhone(data.phone)) {
    throw new Error('Invalid phone number format')
  }
}

function sanitizeData(data: any) {
  // Recursively sanitize all string fields to prevent XSS
  Object.keys(data).forEach(key => {
    if (typeof data[key] === 'string') {
      // Basic XSS prevention - remove script tags and dangerous attributes
      data[key] = data[key]
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
    } else if (typeof data[key] === 'object' && data[key] !== null) {
      sanitizeData(data[key])
    }
  })
}

// Utility validation functions
function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function isValidPhone(phone: string): boolean {
  // Basic phone validation - allows various formats
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}
