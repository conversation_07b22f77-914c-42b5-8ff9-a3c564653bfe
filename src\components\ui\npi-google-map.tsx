'use client'

import React, { useEffect, useRef, useState } from 'react'
import { Loader } from '@googlemaps/js-api-loader'
import { MapPin, ExternalLink } from 'lucide-react'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from './npi-card'

interface NPIGoogleMapProps {
  title?: string
  description?: string
  className?: string
  height?: string
  apiKey?: string
}

export const NPIGoogleMap: React.FC<NPIGoogleMapProps> = ({
  title = 'Find Us',
  description = 'Visit our office at the National Museums of Kenya, Museum Hill Road, Nairobi.',
  className = '',
  height = 'h-96',
  apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
}) => {
  const mapRef = useRef<HTMLDivElement>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)

  // NPI Office coordinates (National Museums of Kenya, Museum Hill Road, Nairobi)
  const npiLocation = {
    lat: -1.2741,
    lng: 36.8155,
  }

  useEffect(() => {
    if (!apiKey) {
      setMapError('Google Maps API key not configured')
      return
    }

    const initMap = async () => {
      try {
        const loader = new Loader({
          apiKey: apiKey,
          version: 'weekly',
          libraries: ['places'],
        })

        const { Map } = await loader.importLibrary('maps')
        const { AdvancedMarkerElement } = await loader.importLibrary('marker')

        if (mapRef.current) {
          const map = new Map(mapRef.current, {
            center: npiLocation,
            zoom: 15,
            mapId: 'npi-office-map',
            styles: [
              {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'on' }],
              },
              {
                featureType: 'road',
                elementType: 'geometry',
                stylers: [{ color: '#f5f1eb' }],
              },
              {
                featureType: 'water',
                elementType: 'geometry',
                stylers: [{ color: '#c9c2bc' }],
              },
            ],
          })

          // Create custom marker
          const marker = new AdvancedMarkerElement({
            map: map,
            position: npiLocation,
            title: 'Natural Products Industry Initiative - NPI Office',
          })

          // Create info window
          const infoWindow = new google.maps.InfoWindow({
            content: `
              <div style="padding: 12px; font-family: 'Inter', sans-serif;">
                <h3 style="margin: 0 0 8px 0; color: #725242; font-size: 16px; font-weight: 600;">
                  Natural Products Industry Initiative
                </h3>
                <p style="margin: 0 0 8px 0; color: #725242; font-size: 14px;">
                  National Museums of Kenya<br>
                  Museum Hill Road<br>
                  P.O. Box 40658-00100<br>
                  Nairobi, Kenya
                </p>
                <div style="margin-top: 12px;">
                  <a 
                    href="https://maps.google.com/?q=${npiLocation.lat},${npiLocation.lng}" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    style="color: #A8422D; text-decoration: none; font-size: 14px; font-weight: 500;"
                  >
                    Get Directions →
                  </a>
                </div>
              </div>
            `,
          })

          // Open info window on marker click
          marker.addListener('click', () => {
            infoWindow.open(map, marker)
          })

          setMapLoaded(true)
        }
      } catch (error) {
        console.error('Error loading Google Maps:', error)
        setMapError('Failed to load map. Please try again later.')
      }
    }

    initMap()
  }, [apiKey])

  if (mapError) {
    return (
      <NPICard
        className={`bg-gradient-to-br from-white to-[#E5E1DC] border-2 border-[#25718A]/30 ${className}`}
      >
        <NPICardHeader>
          <div className="flex items-center gap-2">
            <MapPin className="w-5 h-5 text-[#25718A]" />
            <NPICardTitle className="text-lg font-bold text-[#141311] font-npi">
              {title}
            </NPICardTitle>
          </div>
          {description && <p className="text-[#46372A] font-npi mt-2 text-sm">{description}</p>}
        </NPICardHeader>
        <NPICardContent>
          <div
            className={`${height} bg-[#EFE3BA] border-2 border-[#25718A]/30 flex items-center justify-center`}
          >
            <div className="text-center p-4">
              <MapPin className="w-10 h-10 text-[#25718A] mx-auto mb-3" />
              <p className="text-[#8A3E25] font-npi font-semibold mb-2 text-sm">Map Unavailable</p>
              <p className="text-sm text-[#725242] font-npi mb-3">{mapError}</p>
              <a
                href={`https://maps.google.com/?q=${npiLocation.lat},${npiLocation.lng}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-[#25718A] hover:text-[#1e5a6b] transition-colors duration-200 font-npi font-medium text-sm"
              >
                <ExternalLink className="w-4 h-4" />
                Open in Google Maps
              </a>
            </div>
          </div>
        </NPICardContent>
      </NPICard>
    )
  }

  return (
    <NPICard className={`bg-white border-2 border-[#725242]/30 ${className}`}>
      <NPICardHeader>
        <div className="flex items-center gap-2">
          <MapPin className="w-5 h-5 text-[#725242]" />
          <NPICardTitle className="text-lg font-bold text-[#8A3E25] font-npi">{title}</NPICardTitle>
        </div>
        {description && <p className="text-[#725242] font-npi mt-2 text-sm">{description}</p>}
      </NPICardHeader>
      <NPICardContent>
        <div className={`${height} bg-[#EFE3BA] border-2 border-[#725242]/30 overflow-hidden`}>
          {!mapLoaded && (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center">
                <div className="w-6 h-6 border-2 border-[#725242] border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
                <p className="text-[#8A3E25] font-npi text-sm">Loading map...</p>
              </div>
            </div>
          )}
          <div ref={mapRef} className="w-full h-full" />
        </div>
        <div className="mt-3 flex justify-between items-center">
          <div className="text-sm text-[#725242] font-npi">
            National Museums of Kenya, Museum Hill Road
          </div>
          <a
            href={`https://maps.google.com/?q=${npiLocation.lat},${npiLocation.lng}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 text-[#25718A] hover:text-[#1e5a6b] transition-colors duration-200 font-npi font-medium text-sm"
          >
            <ExternalLink className="w-4 h-4" />
            Get Directions
          </a>
        </div>
      </NPICardContent>
    </NPICard>
  )
}
