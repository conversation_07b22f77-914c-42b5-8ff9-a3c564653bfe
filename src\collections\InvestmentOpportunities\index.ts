import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const InvestmentOpportunities: CollectionConfig = {
  slug: 'investment-opportunities',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'sector', 'investmentType', 'status', 'fundingRequired'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Investment Opportunity',
    plural: 'Investment Opportunities',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The investment opportunity title',
      },
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      admin: {
        description: 'Detailed opportunity description',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief opportunity summary for cards and previews (max 300 characters)',
      },
    },
    {
      name: 'sector',
      type: 'select',
      required: true,
      options: [
        { label: 'Natural Products Processing', value: 'natural-products-processing' },
        { label: 'Traditional Medicine', value: 'traditional-medicine' },
        { label: 'Cosmetics & Personal Care', value: 'cosmetics-personal-care' },
        { label: 'Food & Beverages', value: 'food-beverages' },
        { label: 'Agriculture & Farming', value: 'agriculture-farming' },
        { label: 'Research & Development', value: 'research-development' },
        { label: 'Technology & Innovation', value: 'technology-innovation' },
        { label: 'Manufacturing', value: 'manufacturing' },
        { label: 'Distribution & Retail', value: 'distribution-retail' },
        { label: 'Education & Training', value: 'education-training' },
      ],
    },
    {
      name: 'investmentType',
      type: 'select',
      required: true,
      options: [
        { label: 'Equity Investment', value: 'equity' },
        { label: 'Debt Financing', value: 'debt' },
        { label: 'Grant Funding', value: 'grant' },
        { label: 'Joint Venture', value: 'joint-venture' },
        { label: 'Public-Private Partnership', value: 'ppp' },
        { label: 'Impact Investment', value: 'impact' },
        { label: 'Crowdfunding', value: 'crowdfunding' },
        { label: 'Blended Finance', value: 'blended-finance' },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'open',
      options: [
        { label: 'Open', value: 'open' },
        { label: 'Under Review', value: 'under-review' },
        { label: 'Funded', value: 'funded' },
        { label: 'Closed', value: 'closed' },
        { label: 'On Hold', value: 'on-hold' },
      ],
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
      required: true,
      admin: {
        description: 'Main opportunity image',
      },
    },
    {
      name: 'gallery',
      type: 'array',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
        },
      ],
      admin: {
        description: 'Additional opportunity images',
      },
    },
    {
      name: 'financial',
      type: 'group',
      dbName: 'financial',
      fields: [
        {
          name: 'fundingRequired',
          type: 'number',
          dbName: 'funding_req',
          required: true,
          admin: {
            description: 'Total funding required',
          },
        },
        {
          name: 'currency',
          type: 'select',
          dbName: 'currency',
          defaultValue: 'KES',
          options: [
            { label: 'KES', value: 'KES' },
            { label: 'USD', value: 'USD' },
            { label: 'EUR', value: 'EUR' },
          ],
        },
        {
          name: 'fundingStages',
          type: 'array',
          dbName: 'fund_stages',
          fields: [
            {
              name: 'stage',
              type: 'text',
              dbName: 'stage',
              required: true,
            },
            {
              name: 'amount',
              type: 'number',
              dbName: 'amount',
              required: true,
            },
            {
              name: 'timeline',
              type: 'text',
              dbName: 'timeline',
            },
            {
              name: 'milestones',
              type: 'textarea',
              dbName: 'milestones',
            },
          ],
        },
        {
          name: 'useOfFunds',
          type: 'array',
          dbName: 'use_funds',
          fields: [
            {
              name: 'category',
              type: 'text',
              dbName: 'category',
              required: true,
            },
            {
              name: 'amount',
              type: 'number',
              dbName: 'amount',
              required: true,
            },
            {
              name: 'percentage',
              type: 'number',
              dbName: 'percentage',
              min: 0,
              max: 100,
            },
            {
              name: 'description',
              type: 'textarea',
              dbName: 'description',
            },
          ],
        },
        {
          name: 'expectedReturns',
          type: 'group',
          dbName: 'exp_returns',
          fields: [
            {
              name: 'roi',
              type: 'number',
              dbName: 'roi',
              admin: {
                description: 'Expected Return on Investment (%)',
              },
            },
            {
              name: 'paybackPeriod',
              type: 'text',
              dbName: 'payback_period',
              admin: {
                description: 'Expected payback period (e.g., "3-5 years")',
              },
            },
            {
              name: 'revenueProjections',
              type: 'array',
              dbName: 'rev_proj',
              fields: [
                {
                  name: 'year',
                  type: 'number',
                  dbName: 'year',
                  required: true,
                },
                {
                  name: 'revenue',
                  type: 'number',
                  dbName: 'revenue',
                  required: true,
                },
                {
                  name: 'profit',
                  type: 'number',
                  dbName: 'profit',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'businessModel',
      type: 'group',
      dbName: 'biz_model',
      fields: [
        {
          name: 'valueProposition',
          type: 'richText',
          dbName: 'value_prop',
          required: true,
        },
        {
          name: 'targetMarket',
          type: 'richText',
          dbName: 'target_market',
          required: true,
        },
        {
          name: 'competitiveAdvantage',
          type: 'richText',
          dbName: 'comp_advantage',
        },
        {
          name: 'revenueStreams',
          type: 'array',
          dbName: 'rev_streams',
          fields: [
            {
              name: 'stream',
              type: 'text',
              dbName: 'stream',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
              dbName: 'description',
            },
            {
              name: 'projectedRevenue',
              type: 'number',
              dbName: 'proj_revenue',
            },
          ],
        },
        {
          name: 'keyPartners',
          type: 'array',
          fields: [
            {
              name: 'partner',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'contribution',
              type: 'textarea',
            },
          ],
        },
      ],
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'counties',
          type: 'relationship',
          relationTo: 'counties',
          hasMany: true,
          required: true,
        },
        {
          name: 'specificLocation',
          type: 'text',
        },
        {
          name: 'coordinates',
          type: 'group',
          fields: [
            {
              name: 'latitude',
              type: 'number',
            },
            {
              name: 'longitude',
              type: 'number',
            },
          ],
        },
      ],
    },
    {
      name: 'timeline',
      type: 'group',
      fields: [
        {
          name: 'applicationDeadline',
          type: 'date',
        },
        {
          name: 'expectedStartDate',
          type: 'date',
        },
        {
          name: 'projectDuration',
          type: 'text',
          admin: {
            description: 'Expected project duration (e.g., "2-3 years")',
          },
        },
        {
          name: 'milestones',
          type: 'array',
          fields: [
            {
              name: 'milestone',
              type: 'text',
              required: true,
            },
            {
              name: 'targetDate',
              type: 'date',
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
      ],
    },
    {
      name: 'requirements',
      type: 'group',
      dbName: 'requirements',
      fields: [
        {
          name: 'investorCriteria',
          type: 'array',
          dbName: 'investor_crit',
          fields: [
            {
              name: 'criterion',
              type: 'text',
              dbName: 'criterion',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
              dbName: 'description',
            },
            {
              name: 'mandatory',
              type: 'checkbox',
              dbName: 'mandatory',
              defaultValue: false,
            },
          ],
        },
        {
          name: 'minimumInvestment',
          type: 'number',
          dbName: 'min_investment',
        },
        {
          name: 'maximumInvestment',
          type: 'number',
          dbName: 'max_investment',
        },
        {
          name: 'investorType',
          type: 'select',
          hasMany: true,
          options: [
            { label: 'Individual Investor', value: 'individual' },
            { label: 'Institutional Investor', value: 'institutional' },
            { label: 'Impact Investor', value: 'impact' },
            { label: 'Government Agency', value: 'government' },
            { label: 'Development Finance Institution', value: 'dfi' },
            { label: 'Private Equity', value: 'private-equity' },
            { label: 'Venture Capital', value: 'venture-capital' },
          ],
        },
        {
          name: 'documentation',
          type: 'array',
          fields: [
            {
              name: 'document',
              type: 'text',
              required: true,
            },
            {
              name: 'required',
              type: 'checkbox',
              defaultValue: true,
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
      ],
    },
    {
      name: 'impact',
      type: 'group',
      fields: [
        {
          name: 'socialImpact',
          type: 'richText',
        },
        {
          name: 'environmentalImpact',
          type: 'richText',
        },
        {
          name: 'economicImpact',
          type: 'richText',
        },
        {
          name: 'beneficiaries',
          type: 'number',
          admin: {
            description: 'Expected number of direct beneficiaries',
          },
        },
        {
          name: 'jobsCreated',
          type: 'number',
          admin: {
            description: 'Expected number of jobs to be created',
          },
        },
        {
          name: 'sdgAlignment',
          type: 'array',
          fields: [
            {
              name: 'sdg',
              type: 'select',
              options: [
                { label: 'SDG 1: No Poverty', value: 'sdg-1' },
                { label: 'SDG 2: Zero Hunger', value: 'sdg-2' },
                { label: 'SDG 3: Good Health and Well-being', value: 'sdg-3' },
                { label: 'SDG 4: Quality Education', value: 'sdg-4' },
                { label: 'SDG 5: Gender Equality', value: 'sdg-5' },
                { label: 'SDG 8: Decent Work and Economic Growth', value: 'sdg-8' },
                { label: 'SDG 9: Industry, Innovation and Infrastructure', value: 'sdg-9' },
                { label: 'SDG 10: Reduced Inequalities', value: 'sdg-10' },
                { label: 'SDG 11: Sustainable Cities and Communities', value: 'sdg-11' },
                { label: 'SDG 12: Responsible Consumption and Production', value: 'sdg-12' },
                { label: 'SDG 13: Climate Action', value: 'sdg-13' },
                { label: 'SDG 15: Life on Land', value: 'sdg-15' },
                { label: 'SDG 17: Partnerships for the Goals', value: 'sdg-17' },
              ],
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
      ],
    },
    {
      name: 'team',
      type: 'group',
      fields: [
        {
          name: 'projectLead',
          type: 'group',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'bio',
              type: 'textarea',
            },
            {
              name: 'photo',
              type: 'upload',
              relationTo: 'media',
            },
            {
              name: 'email',
              type: 'email',
            },
          ],
        },
        {
          name: 'keyPersonnel',
          type: 'array',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'expertise',
              type: 'text',
            },
            {
              name: 'bio',
              type: 'textarea',
            },
          ],
        },
      ],
    },
    {
      name: 'documents',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Business Plan', value: 'business-plan' },
            { label: 'Financial Projections', value: 'financial-projections' },
            { label: 'Market Analysis', value: 'market-analysis' },
            { label: 'Technical Specifications', value: 'technical-specs' },
            { label: 'Legal Documents', value: 'legal-documents' },
            { label: 'Presentation', value: 'presentation' },
            { label: 'Other', value: 'other' },
          ],
        },
        {
          name: 'confidential',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'applicationProcess',
      type: 'group',
      dbName: 'app_process',
      fields: [
        {
          name: 'steps',
          type: 'array',
          dbName: 'steps',
          fields: [
            {
              name: 'step',
              type: 'text',
              dbName: 'step',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
              dbName: 'description',
            },
            {
              name: 'duration',
              type: 'text',
              dbName: 'duration',
            },
          ],
        },
        {
          name: 'contactPerson',
          type: 'group',
          dbName: 'contact_person',
          fields: [
            {
              name: 'name',
              type: 'text',
              dbName: 'name',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              dbName: 'role',
              required: true,
            },
            {
              name: 'email',
              type: 'email',
              dbName: 'email',
              required: true,
            },
            {
              name: 'phone',
              type: 'text',
              dbName: 'phone',
            },
          ],
        },
        {
          name: 'applicationForm',
          type: 'upload',
          dbName: 'app_form',
          relationTo: 'media',
          admin: {
            description: 'Application form document',
          },
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this opportunity on homepage and key sections',
      },
    },
    {
      name: 'urgent',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Mark as urgent opportunity',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    ...slugField(),
  ],
}

export default InvestmentOpportunities
