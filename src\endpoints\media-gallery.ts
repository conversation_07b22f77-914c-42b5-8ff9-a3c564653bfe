import type { PayloadRequest } from 'payload'

interface TransformedMediaItem {
  id: string
  title: string
  description?: string
  caption?: string
  type: string
  media?: TransformedMedia
  thumbnail?: TransformedMedia
  category: string
  tags?: string[]
  location?: {
    county?: TransformedCounty
    specificLocation?: string
    coordinates?: {
      latitude?: number
      longitude?: number
    }
  }
  dateCreated: string
  event?: any
  project?: any
  credits?: {
    photographer?: string
    videographer?: string
    organization?: string
    copyright?: string
    license: string
  }
  technical?: {
    dimensions?: {
      width?: number
      height?: number
    }
    duration?: string
    fileSize?: string
    format?: string
    quality?: string
  }
  accessibility?: {
    altText?: string
    transcript?: string
    subtitles?: TransformedMedia
  }
  usage: {
    allowDownload: boolean
    allowEmbedding: boolean
    commercialUse: boolean
    attribution?: string
  }
  analytics: {
    viewCount: number
    downloadCount: number
    shareCount: number
    lastViewed?: string
  }
  featured: boolean
  published: boolean
  slug: string
  createdAt: string
  updatedAt: string
}

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
  mimeType?: string
  filesize?: number
}

interface TransformedCounty {
  id: string
  name: string
  code?: string
}

interface MediaGalleryResponse {
  mediaItems: TransformedMediaItem[]
  totalItems: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Utility functions
const extractTextFromLexical = (richTextData: any): string => {
  if (!richTextData || typeof richTextData === 'string') {
    return richTextData || ''
  }

  if (richTextData.root && richTextData.root.children) {
    return extractTextFromChildren(richTextData.root.children)
  }

  return ''
}

const extractTextFromChildren = (children: any[]): string => {
  if (!Array.isArray(children)) return ''

  return children
    .map((child) => {
      if (child.type === 'text') {
        return child.text || ''
      }
      if (child.children && Array.isArray(child.children)) {
        return extractTextFromChildren(child.children)
      }
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()
}

const transformMedia = (media: any): TransformedMedia | undefined => {
  if (!media || typeof media === 'string') return undefined

  return {
    id: media.id,
    filename: media.filename,
    url: media.url || `/api/media/file/${media.filename}`,
    alt: media.alt,
    width: media.width,
    height: media.height,
    mimeType: media.mimeType,
    filesize: media.filesize,
  }
}

const transformCounty = (county: any): TransformedCounty | undefined => {
  if (!county || typeof county === 'string') return undefined

  return {
    id: county.id,
    name: county.name,
    code: county.code,
  }
}

const transformMediaItem = (item: any): TransformedMediaItem => {
  return {
    id: item.id,
    title: item.title,
    description: extractTextFromLexical(item.description),
    caption: item.caption,
    type: item.type,
    media: transformMedia(item.media),
    thumbnail: transformMedia(item.thumbnail),
    category: item.category,
    tags: Array.isArray(item.tags) 
      ? item.tags.map((tag: any) => tag.tag).filter(Boolean)
      : [],
    location: item.location ? {
      county: transformCounty(item.location.county),
      specificLocation: item.location.specificLocation,
      coordinates: item.location.coordinates,
    } : undefined,
    dateCreated: item.dateCreated,
    event: item.event,
    project: item.project,
    credits: item.credits ? {
      photographer: item.credits.photographer,
      videographer: item.credits.videographer,
      organization: item.credits.organization,
      copyright: item.credits.copyright,
      license: item.credits.license || 'all-rights-reserved',
    } : undefined,
    technical: item.technical ? {
      dimensions: item.technical.dimensions,
      duration: item.technical.duration,
      fileSize: item.technical.fileSize,
      format: item.technical.format,
      quality: item.technical.quality,
    } : undefined,
    accessibility: item.accessibility ? {
      altText: item.accessibility.altText,
      transcript: extractTextFromLexical(item.accessibility.transcript),
      subtitles: transformMedia(item.accessibility.subtitles),
    } : undefined,
    usage: {
      allowDownload: item.usage?.allowDownload !== false,
      allowEmbedding: item.usage?.allowEmbedding !== false,
      commercialUse: item.usage?.commercialUse || false,
      attribution: item.usage?.attribution,
    },
    analytics: {
      viewCount: item.analytics?.viewCount || 0,
      downloadCount: item.analytics?.downloadCount || 0,
      shareCount: item.analytics?.shareCount || 0,
      lastViewed: item.analytics?.lastViewed,
    },
    featured: item.featured || false,
    published: item.published !== false,
    slug: item.slug,
    createdAt: item.createdAt,
    updatedAt: item.updatedAt,
  }
}

// Main Media Gallery Handler
export const mediaGalleryHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req

    // Parse query parameters
    const {
      type,
      category,
      featured,
      county,
      event,
      project,
      license,
      limit = '20',
      page = '1',
      sort = '-dateCreated',
      search,
    } = req.query as Record<string, string>

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = Math.min(parseInt(limit) || 20, 100) // Cap at 100

    // Build where clause
    const where: any = {
      published: { equals: true },
    }

    if (type) where.type = { equals: type }
    if (category) where.category = { equals: category }
    if (featured === 'true') where.featured = { equals: true }
    if (county) where['location.county'] = { equals: county }
    if (event) where.event = { equals: event }
    if (project) where.project = { equals: project }
    if (license) where['credits.license'] = { equals: license }
    if (search) {
      where.or = [
        { title: { contains: search } },
        { caption: { contains: search } },
        { 'tags.tag': { contains: search } },
        { 'credits.photographer': { contains: search } },
        { 'credits.organization': { contains: search } },
      ]
    }

    // Fetch media items with populated relationships
    const mediaResult = await payload.find({
      collection: 'media-gallery',
      where,
      limit: parsedLimit,
      page: parsedPage,
      sort: sort as any,
      depth: 2, // Populate counties, events, projects, etc.
    })

    // Transform media items
    const transformedMediaItems: TransformedMediaItem[] = mediaResult.docs.map(transformMediaItem)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(mediaResult.totalDocs / currentLimit)

    const response: MediaGalleryResponse = {
      mediaItems: transformedMediaItems,
      totalItems: mediaResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in media gallery endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}

// Get single media item by ID or slug
export const mediaItemByIdHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { id } = req.params

    // Try to find by ID first, then by slug
    let mediaItem
    try {
      mediaItem = await payload.findByID({
        collection: 'media-gallery',
        id,
        depth: 2,
      })
    } catch {
      // If ID lookup fails, try slug
      const result = await payload.find({
        collection: 'media-gallery',
        where: { slug: { equals: id } },
        limit: 1,
        depth: 2,
      })
      mediaItem = result.docs[0]
    }

    if (!mediaItem) {
      return res.status(404).json({
        error: 'Media item not found',
        message: `No media item found with ID or slug: ${id}`,
      })
    }

    // Check if published (unless user is authenticated)
    if (!mediaItem.published && !req.user) {
      return res.status(404).json({
        error: 'Media item not found',
        message: 'Media item is not published',
      })
    }

    const transformedMediaItem = transformMediaItem(mediaItem)

    // Increment view count (in a real implementation, you might want to do this asynchronously)
    try {
      await payload.update({
        collection: 'media-gallery',
        id: mediaItem.id,
        data: {
          analytics: {
            ...mediaItem.analytics,
            viewCount: (mediaItem.analytics?.viewCount || 0) + 1,
            lastViewed: new Date().toISOString(),
          },
        },
      })
    } catch (updateError) {
      console.warn('Failed to update view count:', updateError)
    }

    res.status(200).json({
      mediaItem: transformedMediaItem,
    })
  } catch (error) {
    console.error('Error in media item by ID endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
