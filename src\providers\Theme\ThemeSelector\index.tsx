'use client'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import React, { useState } from 'react'

import { useTheme } from '..'

export const ThemeSelector: React.FC = () => {
  const { setTheme } = useTheme()
  const [value, setValue] = useState('')

  const onThemeChange = (themeToSet: string) => {
    // NPI uses light theme only, but we keep the selector for consistency
    if (themeToSet === 'auto' || themeToSet === 'light') {
      setTheme('light')
      setValue('light')
    }
  }

  React.useEffect(() => {
    // NPI uses light theme only
    setValue('light')
  }, [])

  return (
    <Select onValueChange={onThemeChange} value={value}>
      <SelectTrigger
        aria-label="Select a theme"
        className="w-auto bg-transparent gap-2 pl-0 md:pl-3 border-none"
      >
        <SelectValue placeholder="Theme" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="light">Light Theme</SelectItem>
      </SelectContent>
    </Select>
  )
}
