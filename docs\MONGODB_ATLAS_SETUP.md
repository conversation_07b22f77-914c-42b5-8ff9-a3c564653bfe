# MongoDB Atlas Setup Guide

## Quick Setup for NPI Website

Follow these steps to set up MongoDB Atlas (cloud database) for your NPI website.

## Step 1: Create MongoDB Atlas Account

1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Click "Try Free"
3. Sign up with your email or Google account
4. Verify your email address

## Step 2: Create Your First Cluster

1. After logging in, you'll see "Create a deployment"
2. Choose **M0 Sandbox** (Free tier - perfect for development)
3. Select your preferred cloud provider:
   - **AWS** (recommended)
   - Google Cloud
   - Azure
4. Choose a region close to your location
5. Name your cluster: `npi-website-cluster`
6. Click **"Create Deployment"**

⏱️ **Wait 3-5 minutes** for your cluster to be created.

## Step 3: Create Database User

1. You'll see a "Security Quickstart" popup
2. Choose **"Username and Password"**
3. Create a database user:
   - **Username**: `npi-admin`
   - **Password**: Generate a secure password (save this!)
4. Click **"Create User"**

## Step 4: Add Your IP Address

1. In the same popup, you'll see "Where would you like to connect from?"
2. Choose **"My Local Environment"**
3. Click **"Add My Current IP Address"**
4. For development, you can also click **"Allow Access from Anywhere"** (0.0.0.0/0)
   - ⚠️ **Note**: Only use "Allow Access from Anywhere" for development
5. Click **"Finish and Close"**

## Step 5: Get Your Connection String

1. Click **"Connect"** on your cluster
2. Choose **"Drivers"**
3. Select **"Node.js"** and version **"4.1 or later"**
4. Copy the connection string (it looks like this):
   ```
   mongodb+srv://npi-admin:<password>@npi-website-cluster.xxxxx.mongodb.net/?retryWrites=true&w=majority
   ```

## Step 6: Update Your Environment File

1. Open your `.env.local` file
2. Replace the DATABASE_URI with your Atlas connection string:
   ```env
   DATABASE_URI=mongodb+srv://npi-admin:<EMAIL>/npi-cms?retryWrites=true&w=majority
   ```
3. Replace `YOUR_PASSWORD` with the password you created
4. Add `/npi-cms` before the `?` to specify your database name

## Step 7: Test Your Connection

1. Stop your development server (Ctrl+C)
2. Test the database connection:
   ```bash
   pnpm test:db
   ```
3. If successful, start your development server:
   ```bash
   pnpm dev
   ```

## Step 8: Access Your Admin Panel

1. Open your browser to: http://localhost:3000/admin
2. Create your first admin user
3. Start building your content!

## Example Complete .env.local File

```env
# MongoDB Atlas Configuration
DATABASE_URI=mongodb+srv://npi-admin:<EMAIL>/npi-cms?retryWrites=true&w=majority

# PayloadCMS Configuration
PAYLOAD_SECRET=your-super-secure-secret-key-min-32-chars-here
JWT_SECRET=your-jwt-secret-key-min-32-chars-here

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:3000
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3000

# File Storage (Optional - for production)
# BLOB_READ_WRITE_TOKEN=your-vercel-blob-token
```

## Troubleshooting

### Connection Issues

If you get connection errors:

1. **Check your password**: Make sure special characters are URL-encoded
2. **Check IP whitelist**: Ensure your IP is allowed in Network Access
3. **Check connection string**: Ensure it includes the database name (`/npi-cms`)

### URL Encoding Special Characters

If your password contains special characters, encode them:
- `@` becomes `%40`
- `#` becomes `%23`
- `$` becomes `%24`
- `%` becomes `%25`
- `^` becomes `%5E`
- `&` becomes `%26`

Example:
```
Password: myP@ssw0rd#123
Encoded:  myP%40ssw0rd%23123
```

### Test Connection Script

Run this to verify your connection:
```bash
pnpm test:db
```

## Production Setup

For production deployment:

1. **Create a separate cluster** for production
2. **Use a dedicated database user** with limited permissions
3. **Restrict IP access** to your server's IP addresses only
4. **Enable backup** in Atlas settings
5. **Set up monitoring** and alerts

## Atlas Features You Get

✅ **Free Tier Includes**:
- 512 MB storage
- Shared RAM and vCPU
- No time limit
- Basic monitoring
- Community support

✅ **Paid Tiers Add**:
- Dedicated clusters
- Advanced security
- Automated backups
- 24/7 support
- Performance optimization
- Global clusters

## Next Steps

1. **Explore your data**: Use MongoDB Compass or Atlas Data Explorer
2. **Set up monitoring**: Configure alerts for your cluster
3. **Plan for production**: Consider upgrading when you're ready to launch
4. **Learn MongoDB**: Explore MongoDB University for free courses

## Support

- **MongoDB Atlas Docs**: https://docs.atlas.mongodb.com/
- **PayloadCMS MongoDB Guide**: https://payloadcms.com/docs/database/mongodb
- **Community Support**: MongoDB Community Forums

---

🎉 **Congratulations!** Your NPI website is now powered by MongoDB Atlas!
