'use client'

import React, { useState, useEffect } from 'react'
import { ApiTester } from './ApiTester'

interface CollectionStats {
  collection: string
  count: number
  lastUpdated?: string
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'error'
  database: boolean
  api: boolean
  storage: boolean
}

export const Dashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'api-tester' | 'collections'>('overview')
  const [collectionStats, setCollectionStats] = useState<CollectionStats[]>([])
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    status: 'healthy',
    database: true,
    api: true,
    storage: true,
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // Load collection statistics
      const collections = [
        'projects', 'success-stories', 'news', 'resources', 
        'events', 'contact-submissions', 'users', 'media'
      ]
      
      const stats: CollectionStats[] = []
      
      for (const collection of collections) {
        try {
          const response = await fetch(`/api/${collection}?limit=1`)
          if (response.ok) {
            const data = await response.json()
            stats.push({
              collection,
              count: data.totalItems || 0,
              lastUpdated: new Date().toISOString(),
            })
          }
        } catch (error) {
          console.warn(`Failed to load stats for ${collection}:`, error)
          stats.push({
            collection,
            count: 0,
          })
        }
      }
      
      setCollectionStats(stats)
      
      // Check system health
      try {
        const healthResponse = await fetch('/api/health')
        if (healthResponse.ok) {
          const healthData = await healthResponse.json()
          setSystemHealth({
            status: healthData.success ? 'healthy' : 'warning',
            database: healthData.database?.connected || false,
            api: true,
            storage: true,
          })
        }
      } catch (error) {
        setSystemHealth({
          status: 'error',
          database: false,
          api: false,
          storage: false,
        })
      }
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getCollectionDisplayName = (slug: string) => {
    const names: Record<string, string> = {
      'projects': 'Projects',
      'success-stories': 'Success Stories',
      'news': 'News & Updates',
      'resources': 'Resources',
      'events': 'Events',
      'contact-submissions': 'Contact Submissions',
      'users': 'Users',
      'media': 'Media Files',
    }
    return names[slug] || slug
  }

  const getHealthStatusColor = (status: SystemHealth['status']) => {
    switch (status) {
      case 'healthy': return '#10b981'
      case 'warning': return '#f59e0b'
      case 'error': return '#ef4444'
      default: return '#6b7280'
    }
  }

  if (isLoading) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <div>Loading dashboard...</div>
      </div>
    )
  }

  return (
    <div style={{ padding: '2rem' }}>
      <h1 style={{ fontSize: '2rem', fontWeight: '700', marginBottom: '2rem' }}>
        NPI CMS Dashboard
      </h1>

      {/* Tab Navigation */}
      <div style={{ 
        display: 'flex', 
        gap: '1rem', 
        marginBottom: '2rem',
        borderBottom: '1px solid #e5e7eb',
      }}>
        {[
          { key: 'overview', label: 'Overview' },
          { key: 'api-tester', label: 'API Tester' },
          { key: 'collections', label: 'Collections' },
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            style={{
              padding: '0.75rem 1.5rem',
              border: 'none',
              background: 'none',
              borderBottom: activeTab === tab.key ? '2px solid #3b82f6' : '2px solid transparent',
              color: activeTab === tab.key ? '#3b82f6' : '#6b7280',
              fontWeight: activeTab === tab.key ? '600' : '400',
              cursor: 'pointer',
              transition: 'all 0.2s',
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div>
          {/* System Health */}
          <div style={{ marginBottom: '2rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '1rem' }}>
              System Health
            </h2>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1rem',
            }}>
              <div className="collection-card">
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    backgroundColor: getHealthStatusColor(systemHealth.status),
                  }} />
                  <span style={{ fontWeight: '600' }}>Overall Status</span>
                </div>
                <div style={{ textTransform: 'capitalize', marginTop: '0.5rem' }}>
                  {systemHealth.status}
                </div>
              </div>
              
              <div className="collection-card">
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    backgroundColor: systemHealth.database ? '#10b981' : '#ef4444',
                  }} />
                  <span style={{ fontWeight: '600' }}>Database</span>
                </div>
                <div style={{ marginTop: '0.5rem' }}>
                  {systemHealth.database ? 'Connected' : 'Disconnected'}
                </div>
              </div>
              
              <div className="collection-card">
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    backgroundColor: systemHealth.api ? '#10b981' : '#ef4444',
                  }} />
                  <span style={{ fontWeight: '600' }}>API</span>
                </div>
                <div style={{ marginTop: '0.5rem' }}>
                  {systemHealth.api ? 'Operational' : 'Error'}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div>
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '1rem' }}>
              Content Overview
            </h2>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1rem',
            }}>
              {collectionStats.map(stat => (
                <div key={stat.collection} className="collection-card">
                  <div className="collection-card__title">
                    {getCollectionDisplayName(stat.collection)}
                  </div>
                  <div style={{ 
                    fontSize: '2rem', 
                    fontWeight: '700', 
                    color: '#3b82f6',
                    marginBottom: '0.5rem',
                  }}>
                    {stat.count.toLocaleString()}
                  </div>
                  <div className="collection-card__actions">
                    <a 
                      href={`/admin/collections/${stat.collection}`}
                      className="crud-button crud-button--edit"
                      style={{ textDecoration: 'none' }}
                    >
                      Manage
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'api-tester' && (
        <div>
          <ApiTester />
        </div>
      )}

      {activeTab === 'collections' && (
        <div>
          <h2 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '1rem' }}>
            Collection Management
          </h2>
          <div className="collection-list">
            {collectionStats.map(stat => (
              <div key={stat.collection} className="collection-card">
                <div className="collection-card__title">
                  {getCollectionDisplayName(stat.collection)}
                </div>
                <div className="collection-card__description">
                  {stat.count} items • Last updated: {stat.lastUpdated ? new Date(stat.lastUpdated).toLocaleDateString() : 'Unknown'}
                </div>
                <div className="collection-card__actions">
                  <a 
                    href={`/admin/collections/${stat.collection}`}
                    className="crud-button crud-button--edit"
                    style={{ textDecoration: 'none' }}
                  >
                    View All
                  </a>
                  <a 
                    href={`/admin/collections/${stat.collection}/create`}
                    className="crud-button crud-button--create"
                    style={{ textDecoration: 'none' }}
                  >
                    Create New
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
