import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'

// Strategic pillars page components
// Uses: NPIPillarsHeroBlock, NPIStrategicPillarsBlock from strategic-pillars
// Plus shared: NPIStatisticsBlock, NPIFeaturedProjectsBlock

export const metadata: Metadata = {
  title: 'Strategic Pillars - Natural Products Industry Initiative',
  description:
    "Explore NPI's four strategic pillars: Indigenous Knowledge Documentation, Product Development & Commercialization, Capacity Building & Empowerment, and Intellectual Property Protection.",
}

const strategicPillarsLayout = [
  {
    blockType: 'npiPillarsHero' as const,
  },
  {
    blockType: 'npiStrategicPillars' as const,
    id: 'pillars-detail',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Pillar Implementation Progress',
    variant: 'secondary',
    size: 'tight',
  },
  {
    blockType: 'npiFeaturedProjects' as const,
    title: 'Programs by Strategic Pillar',
    description: 'Discover how our programs align with and support each strategic pillar.',
  },
]

export default function StrategicPillarsPage() {
  return (
    <article>
      <PageClient />
      {strategicPillarsLayout.map((block, index) => (
        <section
          key={index}
          className={`
            ${index === 0 ? '' : '-mt-1'}
            relative
            ${index % 2 === 0 ? 'bg-[#FFFFFF]' : 'bg-[#EFE3BA]'}
          `}
        >
          <RenderBlocks blocks={[block]} />
        </section>
      ))}
    </article>
  )
}
