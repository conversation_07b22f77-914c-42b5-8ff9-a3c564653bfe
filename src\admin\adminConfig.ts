import type { AdminConfig } from 'payload'

/**
 * Enhanced admin configuration for comprehensive CMS management
 */
export const enhancedAdminConfig: Partial<AdminConfig> = {
  // Custom admin components and styling
  components: {
    // Custom dashboard component
    // dashboard: '@/admin/components/Dashboard',
    
    // Custom navigation
    // nav: '@/admin/components/Navigation',
    
    // Custom views for collections
    views: {
      // Custom collection views can be added here
      // collections: {
      //   projects: {
      //     list: '@/admin/views/ProjectsList',
      //     edit: '@/admin/views/ProjectsEdit',
      //   },
      // },
    },
  },

  // Enhanced user experience settings
  user: 'users',
  
  // Custom CSS for better admin interface
  css: `
    /* Enhanced admin styling */
    .payload-admin {
      --theme-elevation-0: #ffffff;
      --theme-elevation-50: #fafafa;
      --theme-elevation-100: #f5f5f5;
      --theme-elevation-200: #eeeeee;
      --theme-elevation-300: #e0e0e0;
      --theme-elevation-400: #bdbdbd;
      --theme-elevation-500: #9e9e9e;
      --theme-elevation-600: #757575;
      --theme-elevation-700: #616161;
      --theme-elevation-800: #424242;
      --theme-elevation-900: #212121;
    }
    
    /* Custom button styles for CRUD operations */
    .crud-button {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 0.375rem;
      font-weight: 500;
      transition: all 0.2s;
      cursor: pointer;
      border: none;
    }
    
    .crud-button--create {
      background-color: #10b981;
      color: white;
    }
    
    .crud-button--create:hover {
      background-color: #059669;
    }
    
    .crud-button--edit {
      background-color: #3b82f6;
      color: white;
    }
    
    .crud-button--edit:hover {
      background-color: #2563eb;
    }
    
    .crud-button--delete {
      background-color: #ef4444;
      color: white;
    }
    
    .crud-button--delete:hover {
      background-color: #dc2626;
    }
    
    .crud-button--test {
      background-color: #8b5cf6;
      color: white;
    }
    
    .crud-button--test:hover {
      background-color: #7c3aed;
    }
    
    /* Enhanced form styling */
    .payload-form {
      max-width: none;
    }
    
    .payload-field {
      margin-bottom: 1.5rem;
    }
    
    .payload-field__label {
      font-weight: 600;
      margin-bottom: 0.5rem;
      display: block;
    }
    
    .payload-field__description {
      font-size: 0.875rem;
      color: #6b7280;
      margin-top: 0.25rem;
    }
    
    /* Collection list enhancements */
    .collection-list {
      display: grid;
      gap: 1rem;
    }
    
    .collection-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      padding: 1.5rem;
      transition: all 0.2s;
    }
    
    .collection-card:hover {
      border-color: #3b82f6;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .collection-card__title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    .collection-card__description {
      color: #6b7280;
      margin-bottom: 1rem;
    }
    
    .collection-card__actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }
    
    /* API testing interface */
    .api-tester {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin: 1rem 0;
    }
    
    .api-tester__title {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }
    
    .api-endpoint {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
      padding: 0.75rem;
      background: white;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
    }
    
    .api-method {
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-weight: 600;
      font-size: 0.75rem;
      text-transform: uppercase;
    }
    
    .api-method--get {
      background-color: #10b981;
      color: white;
    }
    
    .api-method--post {
      background-color: #3b82f6;
      color: white;
    }
    
    .api-method--put {
      background-color: #f59e0b;
      color: white;
    }
    
    .api-method--delete {
      background-color: #ef4444;
      color: white;
    }
    
    .api-path {
      font-family: monospace;
      font-size: 0.875rem;
      color: #374151;
    }
    
    /* Status indicators */
    .status-indicator {
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
      font-weight: 500;
    }
    
    .status-indicator--success {
      background-color: #d1fae5;
      color: #065f46;
    }
    
    .status-indicator--error {
      background-color: #fee2e2;
      color: #991b1b;
    }
    
    .status-indicator--warning {
      background-color: #fef3c7;
      color: #92400e;
    }
    
    .status-indicator--info {
      background-color: #dbeafe;
      color: #1e40af;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
      .collection-card__actions {
        flex-direction: column;
      }
      
      .api-endpoint {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }
  `,

  // Meta information
  meta: {
    titleSuffix: '- NPI CMS Admin',
    favicon: '/favicon.ico',
    ogImage: '/api/og-image',
  },

  // Date format
  dateFormat: 'MMMM do, yyyy h:mm a',

  // Disable telemetry for privacy
  telemetry: false,
}

/**
 * Collection-specific admin configurations
 */
export const collectionAdminConfigs = {
  projects: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'status', 'location', 'updatedAt'],
    group: 'Content Management',
    description: 'Manage NPI projects and initiatives',
    pagination: {
      defaultLimit: 20,
      limits: [10, 20, 50, 100],
    },
  },
  
  'success-stories': {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'featured', 'updatedAt'],
    group: 'Content Management',
    description: 'Manage success stories and case studies',
    pagination: {
      defaultLimit: 20,
      limits: [10, 20, 50, 100],
    },
  },
  
  news: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'status', 'publishDate', 'author'],
    group: 'Content Management',
    description: 'Manage news articles and updates',
    pagination: {
      defaultLimit: 20,
      limits: [10, 20, 50, 100],
    },
  },
  
  resources: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'type', 'category', 'downloadCount', 'updatedAt'],
    group: 'Content Management',
    description: 'Manage resources and publications',
    pagination: {
      defaultLimit: 20,
      limits: [10, 20, 50, 100],
    },
  },
  
  events: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'date', 'type', 'location'],
    group: 'Events & Activities',
    description: 'Manage events and activities',
    pagination: {
      defaultLimit: 20,
      limits: [10, 20, 50, 100],
    },
  },
  
  'contact-submissions': {
    useAsTitle: 'subject',
    defaultColumns: ['subject', 'name', 'email', 'status', 'createdAt'],
    group: 'Communications',
    description: 'Manage contact form submissions',
    pagination: {
      defaultLimit: 20,
      limits: [10, 20, 50, 100],
    },
  },
  
  users: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'email', 'role', 'county', 'createdAt'],
    group: 'Administration',
    description: 'Manage system users and permissions',
    pagination: {
      defaultLimit: 20,
      limits: [10, 20, 50, 100],
    },
  },
}
