import * as React from 'react'
import { cn } from '@/utilities/ui'

interface NPISectionProps extends React.HTMLAttributes<HTMLElement> {
  variant?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'accent'
    | 'pattern'
    | 'dots'
    | 'grid'
    | 'diagonal'
    | 'organic'
    | 'gradient-subtle'
    | 'gradient-warm'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'tight'
  container?: boolean
}

const NPISection = React.forwardRef<HTMLElement, NPISectionProps>(
  ({ className, variant = 'default', size = 'md', container = true, children, ...props }, ref) => {
    const sizeClasses = {
      xs: 'py-1 lg:py-2',
      sm: 'py-2 lg:py-4',
      md: 'py-4 lg:py-6',
      lg: 'py-6 lg:py-8',
      xl: 'py-8 lg:py-12',
      tight: 'py-3 lg:py-4',
    }

    const variantClasses = {
      default: 'bg-background',
      primary: 'bg-primary text-primary-foreground',
      secondary: 'bg-secondary text-secondary-foreground',
      accent: 'bg-accent text-accent-foreground',
      pattern: 'npi-pattern-bg',
      dots: 'bg-background npi-pattern-dots',
      grid: 'bg-background npi-pattern-grid',
      diagonal: 'bg-background npi-pattern-diagonal',
      organic: 'bg-background npi-pattern-organic',
      'gradient-subtle': 'npi-gradient-subtle',
      'gradient-warm': 'npi-gradient-warm',
    }

    return (
      <section
        ref={ref}
        className={cn(sizeClasses[size], variantClasses[variant], className)}
        {...props}
      >
        {container ? (
          <div className="npi-container">
            <div className="w-full max-w-none">{children}</div>
          </div>
        ) : (
          children
        )}
      </section>
    )
  },
)
NPISection.displayName = 'NPISection'

const NPISectionHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('text-center mb-12 lg:mb-16', className)} {...props} />
  ),
)
NPISectionHeader.displayName = 'NPISectionHeader'

const NPISectionTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h2 ref={ref} className={cn('npi-heading-2 mb-4 font-npi', className)} {...props} />
))
NPISectionTitle.displayName = 'NPISectionTitle'

const NPISectionDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p ref={ref} className={cn('npi-text-large max-w-3xl mx-auto font-npi', className)} {...props} />
))
NPISectionDescription.displayName = 'NPISectionDescription'

export { NPISection, NPISectionHeader, NPISectionTitle, NPISectionDescription }
