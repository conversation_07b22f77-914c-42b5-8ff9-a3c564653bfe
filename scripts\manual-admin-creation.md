# Manual Admin User Creation Guide

If the automated scripts don't work, here are manual methods to create the admin user.

## Method 1: Try Different Existing Admin Credentials

First, let's test if any of these common admin accounts exist:

```bash
# Test <EMAIL> with password SuperAdmin123!
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"SuperAdmin123!"}'

# Test <EMAIL> with password Admin123!
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123!"}'

# Test <EMAIL> with password password
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Test <EMAIL> with password admin123
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

If any of these return a token, use that token to create the new admin user:

```bash
# Replace YOUR_TOKEN_HERE with the actual token from above
curl -X POST http://localhost:3000/api/users \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Ivy Njoroge",
    "email": "<EMAIL>",
    "password": "admin123",
    "role": "admin",
    "_verified": true,
    "isActive": true,
    "preferences": {
      "newsletter": false,
      "notifications": true,
      "language": "en"
    }
  }'
```

## Method 2: Create First Admin (No Authentication Required)

If no admin users exist yet, try creating the first admin without authentication:

```bash
# Try direct user creation
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Ivy Njoroge",
    "email": "<EMAIL>",
    "password": "admin123",
    "role": "admin"
  }'

# Or try the first-user endpoint
curl -X POST http://localhost:3000/api/users/first-user \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Ivy Njoroge",
    "email": "<EMAIL>",
    "password": "admin123",
    "role": "admin"
  }'
```

## Method 3: Use Admin Panel

1. Visit `http://localhost:3000/admin`
2. If no admin users exist, you should see a "Create First Admin" form
3. Fill in the details:
   - **Name**: Ivy Njoroge
   - **Email**: <EMAIL>
   - **Password**: admin123
   - **Confirm Password**: admin123
4. Click "Create Admin User"

## Method 4: Run Seed Script

If your project has a seed script, it might create default admin users:

```bash
# Check if seed script exists
npm run seed

# Or try
npm run db:seed

# Or check package.json for seed-related scripts
cat package.json | grep -i seed
```

## Method 5: Check Database Directly

If you have database access, you can check what users exist:

### For MongoDB:
```bash
# Connect to MongoDB
mongo your-database-name

# Check existing users
db.users.find({}, {email: 1, role: 1, name: 1})

# Check for admin users specifically
db.users.find({role: "admin"}, {email: 1, role: 1, name: 1})
```

### For PostgreSQL:
```sql
-- Connect to your database
psql -d your-database-name

-- Check existing users
SELECT id, email, role, name FROM users;

-- Check for admin users specifically
SELECT id, email, role, name FROM users WHERE role = 'admin';
```

## Method 6: Update Existing User to Admin

If you find any existing user, you can update their role to admin:

```bash
# First, get a token by logging in as any existing user
TOKEN=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"their-password"}' \
  | grep -o '"token":"[^"]*' | cut -d'"' -f4)

# Then update their role to admin (replace USER_ID with actual ID)
curl -X PUT http://localhost:3000/api/users/USER_ID \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"role": "admin"}'
```

## Troubleshooting

### "401 Unauthorized" Error
- No admin users exist yet, or the credentials are wrong
- Try Method 2 or Method 3 above

### "403 Forbidden" Error
- The user exists but doesn't have permission to create other users
- Try logging in as a different user or use Method 3

### "422 Validation Error" Error
- Check that all required fields are provided
- Ensure the email format is valid
- Make sure the password meets any requirements

### Server Not Responding
- Ensure your development server is running: `npm run dev`
- Check that it's accessible at `http://localhost:3000`
- Look at the server logs for any errors

## Success Indicators

You'll know the admin user was created successfully when you see:
- A response with user details including an `id` field
- No error messages in the response
- Ability to login at `http://localhost:3000/admin` with the new credentials

## Next Steps

After successfully creating the admin user:

1. **Login**: Visit `http://localhost:3000/admin`
2. **Use credentials**: <EMAIL> / admin123
3. **Change password**: Immediately change the password for security
4. **Configure settings**: Set up any additional user preferences or system settings
