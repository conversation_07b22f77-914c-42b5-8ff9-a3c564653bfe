// <PERSON>ript to create a super-admin user
// Run with: node scripts/create-super-admin.js

import { getPayload } from 'payload'
import config from '../src/payload.config.ts'

const SUPER_ADMIN_USER = {
  name: 'Super Administrator',
  email: '<EMAIL>',
  password: 'SuperAdmin123!',
  role: 'super-admin'
}

const createSuperAdmin = async () => {
  try {
    console.log('🚀 Creating Super Admin User...')
    console.log('====================================')
    
    // Initialize Payload
    const payload = await getPayload({ config })
    console.log('✅ Payload initialized successfully')
    
    // Check if user already exists
    const existingUser = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: SUPER_ADMIN_USER.email,
        },
      },
      limit: 1,
    })
    
    if (existingUser.docs.length > 0) {
      console.log(`⚠️  User with email ${SUPER_ADMIN_USER.email} already exists`)
      console.log('Updating existing user to super-admin role...')
      
      const updatedUser = await payload.update({
        collection: 'users',
        id: existingUser.docs[0].id,
        data: {
          role: 'super-admin',
          name: SUPER_ADMIN_USER.name,
        },
      })
      
      console.log('✅ User updated successfully!')
      console.log(`📧 Email: ${updatedUser.email}`)
      console.log(`👤 Name: ${updatedUser.name}`)
      console.log(`🔑 Role: ${updatedUser.role}`)
      console.log(`🆔 ID: ${updatedUser.id}`)
      
    } else {
      // Create new super admin user
      const newUser = await payload.create({
        collection: 'users',
        data: SUPER_ADMIN_USER,
      })
      
      console.log('✅ Super admin user created successfully!')
      console.log(`📧 Email: ${newUser.email}`)
      console.log(`👤 Name: ${newUser.name}`)
      console.log(`🔑 Role: ${newUser.role}`)
      console.log(`🆔 ID: ${newUser.id}`)
    }
    
    console.log('\n🎉 Super admin setup completed!')
    console.log(`You can now login at: http://localhost:3000/admin`)
    console.log(`Email: ${SUPER_ADMIN_USER.email}`)
    console.log(`Password: ${SUPER_ADMIN_USER.password}`)
    
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error creating super admin user:', error)
    process.exit(1)
  }
}

// Run the script
createSuperAdmin()
