import { createCRUDHandlers } from './universal-crud'

// Create CRUD handlers for all collections that need them

// Projects CRUD (enhance existing)
export const projectsCRUD = createCRUDHandlers('projects')

// Success Stories CRUD
export const successStoriesCRUD = createCRUDHandlers('success-stories')

// Resources CRUD
export const resourcesCRUD = createCRUDHandlers('resources')

// News CRUD
export const newsCRUD = createCRUDHandlers('news')

// Media Gallery CRUD
export const mediaGalleryCRUD = createCRUDHandlers('media-gallery')

// Partnerships CRUD
export const partnershipsCRUD = createCRUDHandlers('partnerships')

// Investment Opportunities CRUD
export const investmentOpportunitiesCRUD = createCRUDHandlers('investment-opportunities')

// Partners CRUD
export const partnersCRUD = createCRUDHandlers('partners')

// Events CRUD
export const eventsCRUD = createCRUDHandlers('events')

// Speakers CRUD
export const speakersCRUD = createCRUDHandlers('speakers')

// Users CRUD (with special handling for authentication)
export const usersCRUD = createCRUDHandlers('users')

// Posts CRUD
export const postsCRUD = createCRUDHandlers('posts')

// Pages CRUD
export const pagesCRUD = createCRUDHandlers('pages')

// Categories CRUD
export const categoriesCRUD = createCRUDHandlers('categories')

// Media CRUD
export const mediaCRUD = createCRUDHandlers('media')

// Export all handlers for easy import
export const allCRUDHandlers = {
  projects: projectsCRUD,
  'success-stories': successStoriesCRUD,
  resources: resourcesCRUD,
  news: newsCRUD,
  'media-gallery': mediaGalleryCRUD,
  partnerships: partnershipsCRUD,
  'investment-opportunities': investmentOpportunitiesCRUD,
  partners: partnersCRUD,
  events: eventsCRUD,
  speakers: speakersCRUD,
  users: usersCRUD,
  posts: postsCRUD,
  pages: pagesCRUD,
  categories: categoriesCRUD,
  media: mediaCRUD,
}

// Helper function to get all endpoint configurations
export const getAllEndpointConfigs = () => {
  const endpoints: any[] = []
  
  Object.entries(allCRUDHandlers).forEach(([collectionSlug, handlers]) => {
    // GET all items
    endpoints.push({
      path: `/${collectionSlug}`,
      method: 'get',
      handler: handlers.getAll,
    })
    
    // POST create item
    endpoints.push({
      path: `/${collectionSlug}`,
      method: 'post',
      handler: handlers.create,
    })
    
    // GET item by ID
    endpoints.push({
      path: `/${collectionSlug}/:id`,
      method: 'get',
      handler: handlers.getById,
    })
    
    // PUT update item
    endpoints.push({
      path: `/${collectionSlug}/:id`,
      method: 'put',
      handler: handlers.update,
    })
    
    // DELETE item
    endpoints.push({
      path: `/${collectionSlug}/:id`,
      method: 'delete',
      handler: handlers.delete,
    })
  })
  
  return endpoints
}
