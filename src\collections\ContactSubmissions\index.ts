import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { generateUniversalId, getIdFieldConfig } from '../../hooks/generateUniversalId'

export const ContactSubmissions: CollectionConfig = {
  slug: 'contact-submissions',
  access: {
    create: () => true, // Allow public submissions
    delete: authenticated,
    read: authenticated,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'subject',
    defaultColumns: ['subject', 'name', 'email', 'status', 'createdAt'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Contact Submission',
    plural: 'Contact Submissions',
  },
  fields: [
    getIdFieldConfig('contact-submissions'),
    {
      name: 'name',
      type: 'text',
      required: true,
      admin: {
        description: 'Full name of the person submitting',
      },
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      admin: {
        description: 'Email address for response',
      },
    },
    {
      name: 'phone',
      type: 'text',
      admin: {
        description: 'Phone number (optional)',
      },
    },
    {
      name: 'organization',
      type: 'text',
      admin: {
        description: 'Organization or company name (optional)',
      },
    },
    {
      name: 'role',
      type: 'text',
      admin: {
        description: 'Role or position (optional)',
      },
    },
    {
      name: 'subject',
      type: 'text',
      required: true,
      admin: {
        description: 'Subject of the inquiry',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'General Inquiry', value: 'general' },
        { label: 'Partnership Opportunity', value: 'partnership' },
        { label: 'Investment Inquiry', value: 'investment' },
        { label: 'Project Collaboration', value: 'project-collaboration' },
        { label: 'Media & Press', value: 'media-press' },
        { label: 'Research Collaboration', value: 'research' },
        { label: 'Training & Capacity Building', value: 'training' },
        { label: 'Technical Support', value: 'technical-support' },
        { label: 'Policy & Advocacy', value: 'policy-advocacy' },
        { label: 'Community Engagement', value: 'community-engagement' },
        { label: 'Feedback & Suggestions', value: 'feedback' },
        { label: 'Complaint', value: 'complaint' },
        { label: 'Other', value: 'other' },
      ],
    },
    {
      name: 'priority',
      type: 'select',
      required: true,
      defaultValue: 'medium',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
        { label: 'Urgent', value: 'urgent' },
      ],
    },
    {
      name: 'message',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Detailed message or inquiry',
      },
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'county',
          type: 'relationship',
          relationTo: 'counties',
        },
        {
          name: 'city',
          type: 'text',
        },
        {
          name: 'country',
          type: 'text',
          defaultValue: 'Kenya',
        },
      ],
    },
    {
      name: 'attachments',
      type: 'array',
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'description',
          type: 'text',
        },
      ],
      admin: {
        description: 'Supporting documents or files',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'new',
      options: [
        { label: 'New', value: 'new' },
        { label: 'In Progress', value: 'in-progress' },
        { label: 'Pending Response', value: 'pending-response' },
        { label: 'Resolved', value: 'resolved' },
        { label: 'Closed', value: 'closed' },
        { label: 'Escalated', value: 'escalated' },
      ],
      admin: {
        description: 'Current status of the submission',
      },
    },
    {
      name: 'assignedTo',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        description: 'Staff member assigned to handle this submission',
      },
    },
    {
      name: 'department',
      type: 'select',
      options: [
        { label: 'General Management', value: 'general-management' },
        { label: 'Partnerships', value: 'partnerships' },
        { label: 'Projects', value: 'projects' },
        { label: 'Research & Development', value: 'research-development' },
        { label: 'Communications', value: 'communications' },
        { label: 'Finance', value: 'finance' },
        { label: 'Technical Support', value: 'technical-support' },
        { label: 'Community Engagement', value: 'community-engagement' },
      ],
      admin: {
        description: 'Department responsible for handling this submission',
      },
    },
    {
      name: 'responses',
      type: 'array',
      fields: [
        {
          name: 'respondent',
          type: 'relationship',
          relationTo: 'users',
          required: true,
        },
        {
          name: 'responseDate',
          type: 'date',
          required: true,
          defaultValue: () => new Date().toISOString(),
        },
        {
          name: 'responseMethod',
          type: 'select',
          options: [
            { label: 'Email', value: 'email' },
            { label: 'Phone Call', value: 'phone' },
            { label: 'In-Person Meeting', value: 'in-person' },
            { label: 'Video Call', value: 'video-call' },
            { label: 'Written Letter', value: 'letter' },
          ],
        },
        {
          name: 'response',
          type: 'richText',
          required: true,
        },
        {
          name: 'followUpRequired',
          type: 'checkbox',
          dbName: 'followup_req',
          defaultValue: false,
        },
        {
          name: 'followUpDate',
          type: 'date',
        },
      ],
      admin: {
        description: 'Responses and communications log',
      },
    },
    {
      name: 'internalNotes',
      type: 'array',
      fields: [
        {
          name: 'author',
          type: 'relationship',
          relationTo: 'users',
          required: true,
        },
        {
          name: 'date',
          type: 'date',
          required: true,
          defaultValue: () => new Date().toISOString(),
        },
        {
          name: 'note',
          type: 'richText',
          required: true,
        },
        {
          name: 'confidential',
          type: 'checkbox',
          defaultValue: false,
        },
      ],
      admin: {
        description: 'Internal notes and comments (not visible to submitter)',
      },
    },
    {
      name: 'followUp',
      type: 'group',
      fields: [
        {
          name: 'required',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'dueDate',
          type: 'date',
        },
        {
          name: 'assignedTo',
          type: 'relationship',
          relationTo: 'users',
        },
        {
          name: 'notes',
          type: 'textarea',
        },
        {
          name: 'completed',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'completedDate',
          type: 'date',
        },
      ],
    },
    {
      name: 'satisfaction',
      type: 'group',
      fields: [
        {
          name: 'rating',
          type: 'number',
          min: 1,
          max: 5,
          admin: {
            description: 'Satisfaction rating (1-5 stars)',
          },
        },
        {
          name: 'feedback',
          type: 'textarea',
          admin: {
            description: 'Feedback on service quality',
          },
        },
        {
          name: 'surveyDate',
          type: 'date',
        },
      ],
    },
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'source',
          type: 'select',
          options: [
            { label: 'Website Contact Form', value: 'website-form' },
            { label: 'Email', value: 'email' },
            { label: 'Phone Call', value: 'phone' },
            { label: 'In-Person', value: 'in-person' },
            { label: 'Social Media', value: 'social-media' },
            { label: 'Event', value: 'event' },
            { label: 'Referral', value: 'referral' },
            { label: 'Other', value: 'other' },
          ],
          defaultValue: 'website-form',
        },
        {
          name: 'ipAddress',
          type: 'text',
          admin: {
            readOnly: true,
            description: 'IP address of submitter (auto-captured)',
          },
        },
        {
          name: 'userAgent',
          type: 'text',
          admin: {
            readOnly: true,
            description: 'Browser/device information (auto-captured)',
          },
        },
        {
          name: 'referrer',
          type: 'text',
          admin: {
            readOnly: true,
            description: 'Referring page URL (auto-captured)',
          },
        },
      ],
    },
    {
      name: 'archived',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Archive this submission',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for categorization and search',
      },
    },
  ],
  hooks: {
    beforeChange: [generateUniversalId],
  },
}

export default ContactSubmissions
