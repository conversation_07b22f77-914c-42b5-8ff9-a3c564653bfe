import React from 'react'
import { notFound } from 'next/navigation'
import type { Metadata } from 'next/types'
import NPISuccessStoryDetailsClient from './page.client'

interface SuccessStoryPageProps {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: SuccessStoryPageProps): Promise<Metadata> {
  try {
    const { slug } = await params
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/api/success-stories/${slug}`,
      {
        cache: 'no-store',
      },
    )

    if (!response.ok) {
      return {
        title: 'Success Story | NPI',
        description: 'Learn more about this inspiring success story.',
      }
    }

    const data = await response.json()
    const story = data.data

    return {
      title: `${story.title} | Success Stories | NPI`,
      description: story.summary || 'An inspiring success story from NPI.',
      openGraph: {
        title: story.title,
        description: story.summary,
        images: story.image?.url ? [{ url: story.image.url }] : [],
      },
    }
  } catch (error) {
    return {
      title: 'Success Story | NPI',
      description: 'Learn more about this inspiring success story.',
    }
  }
}

export default async function SuccessStoryPage({ params }: SuccessStoryPageProps) {
  try {
    const { slug } = await params
    
    // Fetch success story data
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/api/success-stories/${slug}`,
      {
        cache: 'no-store',
      },
    )

    if (!response.ok) {
      notFound()
    }

    const data = await response.json()
    const story = data.data

    return <NPISuccessStoryDetailsClient story={story} />
  } catch (error) {
    console.error('Error fetching success story:', error)
    notFound()
  }
}
