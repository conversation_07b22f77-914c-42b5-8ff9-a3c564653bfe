import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const Partners: CollectionConfig = {
  slug: 'partners',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'type', 'category', 'status', 'updatedAt'],
    group: 'Content Management',
  },
  labels: {
    singular: 'Partner',
    plural: 'Partners',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      admin: {
        description: 'Partner organization name',
      },
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      admin: {
        description: 'Detailed partner description',
      },
    },
    {
      name: 'summary',
      type: 'textarea',
      required: true,
      maxLength: 300,
      admin: {
        description: 'Brief partner summary for cards and previews (max 300 characters)',
      },
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      required: true,
      admin: {
        description: 'Partner logo',
      },
    },
    {
      name: 'coverImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Cover image for partner profile',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Government Agency', value: 'government' },
        { label: 'International Organization', value: 'international' },
        { label: 'NGO/Civil Society', value: 'ngo' },
        { label: 'Private Sector', value: 'private-sector' },
        { label: 'Academic Institution', value: 'academic' },
        { label: 'Research Institution', value: 'research' },
        { label: 'Community Organization', value: 'community' },
        { label: 'Development Partner', value: 'development' },
        { label: 'Financial Institution', value: 'financial' },
        { label: 'Technology Partner', value: 'technology' },
      ],
    },
    {
      name: 'category',
      type: 'select',
      hasMany: true,
      required: true,
      options: [
        { label: 'Strategic Partner', value: 'strategic' },
        { label: 'Implementation Partner', value: 'implementation' },
        { label: 'Funding Partner', value: 'funding' },
        { label: 'Technical Partner', value: 'technical' },
        { label: 'Community Partner', value: 'community' },
        { label: 'Media Partner', value: 'media' },
        { label: 'Knowledge Partner', value: 'knowledge' },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'active',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Prospective', value: 'prospective' },
        { label: 'Former', value: 'former' },
        { label: 'On Hold', value: 'on-hold' },
      ],
    },
    {
      name: 'contact',
      type: 'group',
      fields: [
        {
          name: 'primaryContact',
          type: 'group',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'email',
              type: 'email',
              required: true,
            },
            {
              name: 'phone',
              type: 'text',
            },
          ],
        },
        {
          name: 'alternateContacts',
          type: 'array',
          dbName: 'alt_contacts',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
              required: true,
            },
            {
              name: 'email',
              type: 'email',
            },
            {
              name: 'phone',
              type: 'text',
            },
          ],
        },
        {
          name: 'address',
          type: 'group',
          fields: [
            {
              name: 'street',
              type: 'text',
            },
            {
              name: 'city',
              type: 'text',
            },
            {
              name: 'county',
              type: 'relationship',
              relationTo: 'counties',
            },
            {
              name: 'country',
              type: 'text',
              defaultValue: 'Kenya',
            },
            {
              name: 'postalCode',
              type: 'text',
            },
          ],
        },
        {
          name: 'website',
          type: 'text',
        },
        {
          name: 'socialMedia',
          type: 'array',
          fields: [
            {
              name: 'platform',
              type: 'select',
              options: [
                { label: 'Facebook', value: 'facebook' },
                { label: 'Twitter', value: 'twitter' },
                { label: 'LinkedIn', value: 'linkedin' },
                { label: 'Instagram', value: 'instagram' },
                { label: 'YouTube', value: 'youtube' },
                { label: 'Other', value: 'other' },
              ],
            },
            {
              name: 'url',
              type: 'text',
              required: true,
            },
          ],
        },
      ],
    },
    {
      name: 'expertise',
      type: 'group',
      fields: [
        {
          name: 'sectors',
          type: 'array',
          fields: [
            {
              name: 'sector',
              type: 'text',
              required: true,
            },
          ],
        },
        {
          name: 'services',
          type: 'array',
          fields: [
            {
              name: 'service',
              type: 'text',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
        {
          name: 'capabilities',
          type: 'array',
          fields: [
            {
              name: 'capability',
              type: 'text',
              required: true,
            },
            {
              name: 'level',
              type: 'select',
              options: [
                { label: 'Basic', value: 'basic' },
                { label: 'Intermediate', value: 'intermediate' },
                { label: 'Advanced', value: 'advanced' },
                { label: 'Expert', value: 'expert' },
              ],
            },
          ],
        },
      ],
    },
    {
      name: 'collaboration',
      type: 'group',
      fields: [
        {
          name: 'partnershipHistory',
          type: 'richText',
          dbName: 'history',
          admin: {
            description: 'History of collaboration with NPI',
          },
        },
        {
          name: 'currentProjects',
          type: 'relationship',
          dbName: 'current_proj',
          relationTo: 'projects',
          hasMany: true,
        },
        {
          name: 'pastProjects',
          type: 'relationship',
          relationTo: 'projects',
          hasMany: true,
        },
        {
          name: 'partnerships',
          type: 'relationship',
          relationTo: 'partnerships',
          hasMany: true,
        },
        {
          name: 'investmentOpportunities',
          type: 'relationship',
          dbName: 'investments',
          relationTo: 'investment-opportunities',
          hasMany: true,
        },
      ],
    },
    {
      name: 'impact',
      type: 'group',
      fields: [
        {
          name: 'contributions',
          type: 'array',
          fields: [
            {
              name: 'contribution',
              type: 'text',
              required: true,
            },
            {
              name: 'description',
              type: 'textarea',
            },
            {
              name: 'impact',
              type: 'textarea',
            },
          ],
        },
        {
          name: 'achievements',
          type: 'array',
          fields: [
            {
              name: 'achievement',
              type: 'text',
              required: true,
            },
            {
              name: 'date',
              type: 'date',
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
        {
          name: 'metrics',
          type: 'array',
          fields: [
            {
              name: 'metric',
              type: 'text',
              required: true,
            },
            {
              name: 'value',
              type: 'text',
              required: true,
            },
            {
              name: 'unit',
              type: 'text',
            },
            {
              name: 'period',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'resources',
      type: 'group',
      dbName: 'resources',
      fields: [
        {
          name: 'financialContribution',
          type: 'group',
          dbName: 'financial',
          fields: [
            {
              name: 'totalContributed',
              type: 'number',
              dbName: 'total',
            },
            {
              name: 'currency',
              type: 'select',
              dbName: 'currency',
              defaultValue: 'KES',
              options: [
                { label: 'KES', value: 'KES' },
                { label: 'USD', value: 'USD' },
                { label: 'EUR', value: 'EUR' },
              ],
            },
            {
              name: 'contributionType',
              type: 'select',
              dbName: 'type',
              hasMany: true,
              options: [
                { label: 'Grant', value: 'grant' },
                { label: 'Investment', value: 'investment' },
                { label: 'Loan', value: 'loan' },
                { label: 'In-kind', value: 'in-kind' },
                { label: 'Technical Assistance', value: 'technical-assistance' },
              ],
            },
          ],
        },
        {
          name: 'inKindContributions',
          type: 'array',
          dbName: 'inkind',
          fields: [
            {
              name: 'resource',
              type: 'text',
              dbName: 'resource',
              required: true,
            },
            {
              name: 'value',
              type: 'number',
              dbName: 'value',
            },
            {
              name: 'description',
              type: 'textarea',
              dbName: 'description',
            },
          ],
        },
        {
          name: 'expertise',
          type: 'array',
          dbName: 'expertise',
          fields: [
            {
              name: 'area',
              type: 'text',
              dbName: 'area',
              required: true,
            },
            {
              name: 'personnel',
              type: 'text',
              dbName: 'personnel',
            },
            {
              name: 'timeCommitment',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'recognition',
      type: 'group',
      fields: [
        {
          name: 'awards',
          type: 'array',
          fields: [
            {
              name: 'award',
              type: 'text',
              required: true,
            },
            {
              name: 'year',
              type: 'number',
            },
            {
              name: 'organization',
              type: 'text',
            },
            {
              name: 'description',
              type: 'textarea',
            },
          ],
        },
        {
          name: 'certifications',
          type: 'array',
          fields: [
            {
              name: 'certification',
              type: 'text',
              required: true,
            },
            {
              name: 'issuingBody',
              type: 'text',
            },
            {
              name: 'validUntil',
              type: 'date',
            },
          ],
        },
        {
          name: 'testimonials',
          type: 'array',
          fields: [
            {
              name: 'quote',
              type: 'textarea',
              required: true,
            },
            {
              name: 'author',
              type: 'text',
              required: true,
            },
            {
              name: 'role',
              type: 'text',
            },
            {
              name: 'organization',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'documents',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Profile', value: 'profile' },
            { label: 'Brochure', value: 'brochure' },
            { label: 'Annual Report', value: 'annual-report' },
            { label: 'Case Study', value: 'case-study' },
            { label: 'Agreement', value: 'agreement' },
            { label: 'Other', value: 'other' },
          ],
        },
        {
          name: 'public',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this partner on homepage and key sections',
      },
    },
    {
      name: 'verified',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Mark as verified partner',
      },
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
      admin: {
        description: 'Tags for search and filtering',
      },
    },
    ...slugField(),
  ],
}

export default Partners
