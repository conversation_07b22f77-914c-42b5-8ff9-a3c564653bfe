# Database Testing Guide

This guide explains how to test and verify that your MongoDB database is properly configured and working with the NPI website.

## Quick Database Verification

Run this single command to verify everything is working:

```bash
npm run verify:database
```

This comprehensive test will check:
- ✅ Environment variables are set correctly
- ✅ MongoDB connection is working
- ✅ PayloadCMS can read/write to the database
- ✅ Contact form API is functional
- ✅ All public API endpoints are accessible

## Individual Test Commands

### 1. Test MongoDB Connection Only
```bash
npm run test:db
```
Tests the raw MongoDB connection and basic read/write operations.

### 2. Test PayloadCMS Database Integration
```bash
npm run test:database
```
Tests PayloadCMS collections, CRUD operations, and data seeding.

### 3. Test Contact Form API
```bash
npm run test:contact-api
```
Tests the contact form submission endpoint and validation.

## Manual Testing Steps

### 1. Test Contact Form from UI

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Visit the contact page: http://localhost:3000/contact

3. Fill out and submit the contact form

4. Check for success/error messages

5. Verify submission in admin panel: http://localhost:3000/admin

### 2. Test CMS Admin Interface

1. Visit: http://localhost:3000/admin

2. Log in with your admin credentials

3. Navigate to "Contact Submissions" collection

4. Verify you can see submitted forms

5. Test creating/editing other content types

### 3. Test API Endpoints Directly

You can test API endpoints using curl or a tool like Postman:

```bash
# Test health endpoint
curl http://localhost:3000/api/health

# Test contact form submission
curl -X POST http://localhost:3000/api/contact-submissions \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "API Test",
    "category": "general",
    "message": "Testing the API endpoint"
  }'

# Test public endpoints
curl http://localhost:3000/api/projects
curl http://localhost:3000/api/counties
```

## Common Issues and Solutions

### Database Connection Issues

**Error: "DATABASE_URI environment variable is not set"**
- Check your `.env.local` file exists
- Verify `DATABASE_URI` is set correctly
- Make sure there are no typos in the variable name

**Error: "Authentication failed"**
- Check username and password in connection string
- Verify database user has proper permissions
- Ensure special characters in password are URL-encoded

**Error: "Connection timed out"**
- Check network connectivity
- Verify IP address is whitelisted in MongoDB Atlas
- Check firewall settings

### API Issues

**Error: "Cannot connect to API"**
- Make sure development server is running (`npm run dev`)
- Check the server is accessible at http://localhost:3000
- Verify no other process is using port 3000

**Error: "Contact form submission failed"**
- Check database connection is working
- Verify PayloadCMS is properly initialized
- Check server logs for detailed error messages

### PayloadCMS Issues

**Error: "Collection not found"**
- Run database seeding: Visit http://localhost:3000/admin
- Check PayloadCMS configuration in `src/payload.config.ts`
- Verify collections are properly defined

## Environment Variables Checklist

Make sure these variables are set in your `.env.local` file:

```env
# Required
DATABASE_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
PAYLOAD_SECRET=your-super-secure-secret-key-min-32-chars-here
NEXT_PUBLIC_API_URL=http://localhost:3000

# Optional but recommended
JWT_SECRET=your-jwt-secret-key-min-32-chars-here
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3000
```

## Database Collections

The following collections should be created automatically:

### Core Collections
- `users` - Admin users and authentication
- `media` - File uploads and media assets
- `pages` - Website pages content

### CMS Collections
- `contact-submissions` - Contact form submissions
- `projects` - Project information
- `success-stories` - Success story content
- `resources` - Downloadable resources
- `news` - News articles
- `partnerships` - Partnership information
- `counties` - County/location data

### System Collections
- `payload-preferences` - User preferences
- `payload-migrations` - Database migrations

## Troubleshooting Commands

```bash
# Check if MongoDB service is running (local installation)
mongosh --eval "db.adminCommand('ping')"

# Test connection with MongoDB URI directly
mongosh "your-connection-string-here" --eval "db.adminCommand('ping')"

# Check PayloadCMS configuration
npm run generate:types

# Rebuild the application
npm run build

# Clear Next.js cache
rm -rf .next
npm run build
```

## Getting Help

If you're still having issues:

1. Check the server logs when running `npm run dev`
2. Review the MongoDB Atlas logs in your cluster dashboard
3. Verify your network connection and firewall settings
4. Check the PayloadCMS documentation: https://payloadcms.com/docs
5. Review the MongoDB connection troubleshooting guide
