# CRUD Operations & Authentication Guide

## Overview

This guide provides detailed examples of CRUD (Create, Read, Update, Delete) operations for the NPI CMS with authentication and access control.

## Authentication System

### User Roles and Permissions

```typescript
// User roles hierarchy
const USER_ROLES = {
  'super-admin': {
    level: 5,
    permissions: ['*'], // All permissions
    description: 'Full system access'
  },
  'admin': {
    level: 4,
    permissions: ['create', 'read', 'update', 'delete', 'manage-users'],
    description: 'Content and user management'
  },
  'content-manager': {
    level: 3,
    permissions: ['create', 'read', 'update', 'publish'],
    description: 'Content creation and publishing'
  },
  'editor': {
    level: 2,
    permissions: ['read', 'update'],
    description: 'Content editing only'
  },
  'user': {
    level: 1,
    permissions: ['read'],
    description: 'Read-only access'
  }
}
```

### Authentication Flow

#### 1. Login Process

```typescript
// Frontend login function
export async function loginUser(email: string, password: string) {
  try {
    const response = await fetch('/api/users/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    })

    if (!response.ok) {
      throw new Error('Login failed')
    }

    const data = await response.json()
    
    // Store token in localStorage or secure cookie
    localStorage.setItem('auth-token', data.token)
    localStorage.setItem('user', JSON.stringify(data.user))
    
    return data
  } catch (error) {
    console.error('Login error:', error)
    throw error
  }
}

// Usage example
const handleLogin = async (formData) => {
  try {
    const result = await loginUser(formData.email, formData.password)
    console.log('Login successful:', result.user)
    // Redirect to dashboard
    router.push('/admin')
  } catch (error) {
    setError('Invalid credentials')
  }
}
```

#### 2. Token Management

```typescript
// Get authentication token
export function getAuthToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth-token')
  }
  return null
}

// Create authenticated request headers
export function createAuthHeaders(): HeadersInit {
  const token = getAuthToken()
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
  }
}

// Check if user is authenticated
export function isAuthenticated(): boolean {
  const token = getAuthToken()
  if (!token) return false
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload.exp > Date.now() / 1000
  } catch {
    return false
  }
}
```

## CRUD Operations by Collection

### 1. Projects Collection

#### Create Project

```typescript
// Frontend: Create new project
export async function createProject(projectData: ProjectInput) {
  const response = await fetch('/api/projects', {
    method: 'POST',
    headers: createAuthHeaders(),
    body: JSON.stringify(projectData),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message || 'Failed to create project')
  }

  return response.json()
}

// Usage example with form
const handleCreateProject = async (formData) => {
  try {
    const projectData = {
      title: formData.title,
      description: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: formData.description,
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      summary: formData.summary,
      category: formData.category,
      pillar: formData.pillar,
      status: formData.status || 'active',
      timeline: {
        startDate: formData.startDate,
        endDate: formData.endDate,
      },
      budget: formData.budget ? {
        totalBudget: parseFloat(formData.budget),
        currency: formData.currency || 'KES',
      } : undefined,
      location: formData.counties ? {
        counties: formData.counties,
        specificLocation: formData.location,
      } : undefined,
      featured: formData.featured || false,
      published: formData.published !== false,
      tags: formData.tags?.map(tag => ({ tag })) || [],
      slug: formData.slug || slugify(formData.title),
    }

    const result = await createProject(projectData)
    console.log('Project created:', result)
    
    // Redirect or update UI
    router.push(`/admin/projects/${result.id}`)
  } catch (error) {
    console.error('Create project error:', error)
    setError(error.message)
  }
}
```

#### Read Projects

```typescript
// Get all projects with filtering
export async function getProjects(params: {
  page?: number
  limit?: number
  category?: string
  status?: string
  featured?: boolean
  search?: string
  county?: string
}) {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value))
    }
  })

  const response = await fetch(`/api/projects?${searchParams.toString()}`)
  
  if (!response.ok) {
    throw new Error('Failed to fetch projects')
  }

  return response.json()
}

// Get single project
export async function getProject(id: string) {
  const response = await fetch(`/api/projects/${id}`)
  
  if (!response.ok) {
    if (response.status === 404) {
      throw new Error('Project not found')
    }
    throw new Error('Failed to fetch project')
  }

  return response.json()
}

// Usage examples
const ProjectsList = () => {
  const [projects, setProjects] = useState([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    featured: false,
    search: '',
  })

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true)
        const result = await getProjects(filters)
        setProjects(result.data)
      } catch (error) {
        console.error('Fetch projects error:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchProjects()
  }, [filters])

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  if (loading) return <div>Loading projects...</div>

  return (
    <div>
      {/* Filter controls */}
      <div className="mb-6 space-y-4">
        <input
          type="text"
          placeholder="Search projects..."
          value={filters.search}
          onChange={(e) => handleFilterChange({ search: e.target.value })}
          className="w-full p-2 border rounded"
        />
        
        <select
          value={filters.category}
          onChange={(e) => handleFilterChange({ category: e.target.value })}
          className="p-2 border rounded"
        >
          <option value="">All Categories</option>
          <option value="community-empowerment">Community Empowerment</option>
          <option value="research-development">Research & Development</option>
          <option value="capacity-building">Capacity Building</option>
        </select>

        <select
          value={filters.status}
          onChange={(e) => handleFilterChange({ status: e.target.value })}
          className="p-2 border rounded"
        >
          <option value="">All Statuses</option>
          <option value="active">Active</option>
          <option value="completed">Completed</option>
          <option value="planning">Planning</option>
        </select>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={filters.featured}
            onChange={(e) => handleFilterChange({ featured: e.target.checked })}
            className="mr-2"
          />
          Featured Only
        </label>
      </div>

      {/* Projects grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {projects.map(project => (
          <div key={project.id} className="border rounded-lg p-4">
            <h3 className="font-semibold text-lg mb-2">{project.title}</h3>
            <p className="text-gray-600 mb-3">{project.summary}</p>
            
            <div className="flex flex-wrap gap-2 mb-3">
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                {project.category}
              </span>
              <span className={`px-2 py-1 rounded text-sm ${getStatusColor(project.status)}`}>
                {project.status}
              </span>
              {project.featured && (
                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">
                  Featured
                </span>
              )}
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">
                {formatDate(project.createdAt)}
              </span>
              <button
                onClick={() => router.push(`/projects/${project.slug}`)}
                className="text-blue-600 hover:text-blue-800"
              >
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>

      {projects.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No projects found matching your criteria.
        </div>
      )}
    </div>
  )
}
```

#### Update Project

```typescript
// Update existing project
export async function updateProject(id: string, updates: Partial<ProjectInput>) {
  const response = await fetch(`/api/projects/${id}`, {
    method: 'PUT',
    headers: createAuthHeaders(),
    body: JSON.stringify(updates),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message || 'Failed to update project')
  }

  return response.json()
}

// Usage example
const handleUpdateProject = async (projectId, formData) => {
  try {
    const updates = {
      title: formData.title,
      summary: formData.summary,
      status: formData.status,
      featured: formData.featured,
      // Only include fields that have changed
      ...(formData.budget && {
        budget: {
          totalBudget: parseFloat(formData.budget),
          currency: formData.currency,
        }
      }),
    }

    const result = await updateProject(projectId, updates)
    console.log('Project updated:', result)
    
    // Update local state or refetch data
    setProject(result)
    setSuccess('Project updated successfully')
  } catch (error) {
    console.error('Update project error:', error)
    setError(error.message)
  }
}
```

#### Delete Project

```typescript
// Delete project
export async function deleteProject(id: string) {
  const response = await fetch(`/api/projects/${id}`, {
    method: 'DELETE',
    headers: createAuthHeaders(),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message || 'Failed to delete project')
  }

  return response.json()
}

// Usage example with confirmation
const handleDeleteProject = async (projectId, projectTitle) => {
  const confirmed = window.confirm(
    `Are you sure you want to delete "${projectTitle}"? This action cannot be undone.`
  )

  if (!confirmed) return

  try {
    await deleteProject(projectId)
    console.log('Project deleted successfully')
    
    // Remove from local state or redirect
    setProjects(prev => prev.filter(p => p.id !== projectId))
    setSuccess('Project deleted successfully')
  } catch (error) {
    console.error('Delete project error:', error)
    setError(error.message)
  }
}
```

### 2. Contact Submissions (Public API)

#### Create Contact Submission (No Auth Required)

```typescript
// Public contact form submission
export async function submitContactForm(formData: {
  name: string
  email: string
  phone?: string
  organization?: string
  role?: string
  subject: string
  category: string
  priority?: string
  message: string
  location?: {
    county?: string
    city?: string
    country?: string
  }
}) {
  const response = await fetch('/api/contact-submissions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(formData),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message || 'Failed to submit form')
  }

  return response.json()
}

// Contact form component
const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    organization: '',
    role: '',
    subject: '',
    category: 'general',
    priority: 'medium',
    message: '',
    location: {
      county: '',
      city: '',
      country: 'Kenya',
    },
  })
  const [submitting, setSubmitting] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e) => {
    e.preventDefault()
    setSubmitting(true)
    setError('')

    try {
      const result = await submitContactForm(formData)
      console.log('Form submitted:', result)
      setSuccess(true)
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        organization: '',
        role: '',
        subject: '',
        category: 'general',
        priority: 'medium',
        message: '',
        location: { county: '', city: '', country: 'Kenya' },
      })
    } catch (error) {
      console.error('Submit error:', error)
      setError(error.message)
    } finally {
      setSubmitting(false)
    }
  }

  if (success) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
        <h3 className="text-lg font-semibold text-green-800 mb-2">
          Thank You!
        </h3>
        <p className="text-green-700">
          Your message has been submitted successfully. We'll get back to you soon.
        </p>
        <button
          onClick={() => setSuccess(false)}
          className="mt-4 text-green-600 hover:text-green-800"
        >
          Submit Another Message
        </button>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">
            Name *
          </label>
          <input
            type="text"
            required
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Email *
          </label>
          <input
            type="email"
            required
            value={formData.email}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">
            Phone
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Organization
          </label>
          <input
            type="text"
            value={formData.organization}
            onChange={(e) => setFormData(prev => ({ ...prev, organization: e.target.value }))}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          Subject *
        </label>
        <input
          type="text"
          required
          value={formData.subject}
          onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">
            Category *
          </label>
          <select
            required
            value={formData.category}
            onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="general">General Inquiry</option>
            <option value="partnership">Partnership</option>
            <option value="investment">Investment</option>
            <option value="project-collaboration">Project Collaboration</option>
            <option value="media-press">Media & Press</option>
            <option value="research">Research</option>
            <option value="training">Training</option>
            <option value="technical-support">Technical Support</option>
            <option value="policy-advocacy">Policy Advocacy</option>
            <option value="community-engagement">Community Engagement</option>
            <option value="feedback">Feedback</option>
            <option value="complaint">Complaint</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Priority
          </label>
          <select
            value={formData.priority}
            onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          Message *
        </label>
        <textarea
          required
          rows={6}
          value={formData.message}
          onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Please provide details about your inquiry..."
        />
      </div>

      <button
        type="submit"
        disabled={submitting}
        className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {submitting ? 'Submitting...' : 'Submit Message'}
      </button>
    </form>
  )
}
```

This guide provides comprehensive examples of CRUD operations with authentication. The system supports role-based access control, proper error handling, and includes both public and protected endpoints. All operations include proper TypeScript typing and error handling for production use.
