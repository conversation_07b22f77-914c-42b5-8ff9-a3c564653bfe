// Utility functions for component organization

/**
 * Type definitions for page-based component imports
 */
export type PageComponents = {
  home: typeof import('./home')
  about: typeof import('./about')
  partnerships: typeof import('./partnerships')
  resources: typeof import('./resources')
  projects: typeof import('./projects')
  'strategic-pillars': typeof import('./strategic-pillars')
  'success-stories': typeof import('./success-stories')
  contact: typeof import('./contact')
  events: typeof import('./events')
  news: typeof import('./news')
  'get-involved': typeof import('./get-involved')
  posts: typeof import('./posts')
  search: typeof import('./search')
}

/**
 * Helper function to get all components for a specific page
 * @param pageName - The name of the page
 * @returns Object containing all components for that page
 */
export async function getPageComponents<T extends keyof PageComponents>(
  pageName: T
): Promise<PageComponents[T]> {
  switch (pageName) {
    case 'home':
      return import('./home') as Promise<PageComponents[T]>
    case 'about':
      return import('./about') as Promise<PageComponents[T]>
    case 'partnerships':
      return import('./partnerships') as Promise<PageComponents[T]>
    case 'resources':
      return import('./resources') as Promise<PageComponents[T]>
    case 'projects':
      return import('./projects') as Promise<PageComponents[T]>
    case 'strategic-pillars':
      return import('./strategic-pillars') as Promise<PageComponents[T]>
    case 'success-stories':
      return import('./success-stories') as Promise<PageComponents[T]>
    case 'contact':
      return import('./contact') as Promise<PageComponents[T]>
    case 'events':
      return import('./events') as Promise<PageComponents[T]>
    case 'news':
      return import('./news') as Promise<PageComponents[T]>
    case 'get-involved':
      return import('./get-involved') as Promise<PageComponents[T]>
    case 'posts':
      return import('./posts') as Promise<PageComponents[T]>
    case 'search':
      return import('./search') as Promise<PageComponents[T]>
    default:
      throw new Error(`Unknown page: ${pageName}`)
  }
}

/**
 * Page route to component mapping
 */
export const PAGE_COMPONENT_MAP = {
  '/': 'home',
  '/about': 'about',
  '/about/operations-structure': 'about',
  '/about/strategic-alignment': 'about',
  '/partnerships': 'partnerships',
  '/partnerships/investment-opportunities': 'partnerships',
  '/partnerships/partners': 'partnerships',
  '/resources': 'resources',
  '/resources/media-gallery': 'resources',
  '/projects': 'projects',
  '/strategic-pillars': 'strategic-pillars',
  '/success-stories': 'success-stories',
  '/contact': 'contact',
  '/events': 'events',
  '/news': 'news',
  '/get-involved': 'get-involved',
  '/posts': 'posts',
  '/search': 'search',
} as const
