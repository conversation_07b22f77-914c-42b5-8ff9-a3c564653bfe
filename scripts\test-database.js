#!/usr/bin/env node

/**
 * Database Connection Test Script
 * 
 * This script tests the MongoDB connection and verifies that data can be
 * read from and written to the database through PayloadCMS.
 */

const { getPayload } = require('payload')
const config = require('../dist/payload.config.js').default

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...\n')

  try {
    // Initialize Payload
    console.log('📦 Initializing PayloadCMS...')
    const payload = await getPayload({ config })
    console.log('✅ PayloadCMS initialized successfully\n')

    // Test 1: Check database connection by counting users
    console.log('👥 Testing user collection access...')
    const userCount = await payload.find({
      collection: 'users',
      limit: 1,
    })
    console.log(`✅ Found ${userCount.totalDocs} users in database\n`)

    // Test 2: Check contact submissions collection
    console.log('📧 Testing contact submissions collection...')
    const contactSubmissions = await payload.find({
      collection: 'contact-submissions',
      limit: 5,
    })
    console.log(`✅ Found ${contactSubmissions.totalDocs} contact submissions\n`)

    // Test 3: Create a test contact submission
    console.log('📝 Creating test contact submission...')
    const testSubmission = await payload.create({
      collection: 'contact-submissions',
      data: {
        name: 'Database Test User',
        email: '<EMAIL>',
        subject: 'Database Connection Test',
        category: 'general',
        priority: 'medium',
        message: 'This is a test message to verify database connectivity.',
        status: 'new',
        metadata: {
          source: 'database-test-script',
          ipAddress: '127.0.0.1',
          userAgent: 'Node.js Test Script',
        },
        archived: false,
      },
    })
    console.log(`✅ Test submission created with ID: ${testSubmission.id}\n`)

    // Test 4: Retrieve the test submission
    console.log('🔍 Retrieving test submission...')
    const retrievedSubmission = await payload.findByID({
      collection: 'contact-submissions',
      id: testSubmission.id,
    })
    console.log(`✅ Retrieved submission: ${retrievedSubmission.name} - ${retrievedSubmission.subject}\n`)

    // Test 5: Update the test submission
    console.log('✏️ Updating test submission...')
    const updatedSubmission = await payload.update({
      collection: 'contact-submissions',
      id: testSubmission.id,
      data: {
        status: 'in-progress',
        internalNotes: [
          {
            author: 'system',
            date: new Date().toISOString(),
            note: 'Updated by database test script',
            confidential: false,
          },
        ],
      },
    })
    console.log(`✅ Updated submission status to: ${updatedSubmission.status}\n`)

    // Test 6: Clean up - delete the test submission
    console.log('🗑️ Cleaning up test submission...')
    await payload.delete({
      collection: 'contact-submissions',
      id: testSubmission.id,
    })
    console.log('✅ Test submission deleted\n')

    // Test 7: Check other collections
    console.log('📊 Checking other collections...')
    const collections = [
      'projects',
      'success-stories',
      'resources',
      'news',
      'media-gallery',
      'partnerships',
      'investment-opportunities',
      'counties',
    ]

    for (const collectionName of collections) {
      try {
        const result = await payload.find({
          collection: collectionName,
          limit: 1,
        })
        console.log(`  ✅ ${collectionName}: ${result.totalDocs} documents`)
      } catch (error) {
        console.log(`  ❌ ${collectionName}: Error - ${error.message}`)
      }
    }

    console.log('\n🎉 Database connection test completed successfully!')
    console.log('\n📋 Summary:')
    console.log('  ✅ Database connection: Working')
    console.log('  ✅ Read operations: Working')
    console.log('  ✅ Write operations: Working')
    console.log('  ✅ Update operations: Working')
    console.log('  ✅ Delete operations: Working')
    console.log('  ✅ Contact submissions: Ready for use')

  } catch (error) {
    console.error('❌ Database test failed:', error)
    console.error('\n🔧 Troubleshooting tips:')
    console.error('  1. Check your DATABASE_URI in .env.local')
    console.error('  2. Ensure MongoDB Atlas cluster is running')
    console.error('  3. Verify network access and IP whitelist')
    console.error('  4. Check database credentials')
    console.error('  5. Run: npm run build (if payload.config.js is missing)')
    
    process.exit(1)
  }
}

// Run the test
testDatabaseConnection()
