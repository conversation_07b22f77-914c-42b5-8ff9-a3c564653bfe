# NPI CMS API Reference

## Base URL

```
Production: https://npi-website.vercel.app/api
Development: http://localhost:3000/api
```

## Database Configuration

The CMS supports PostgreSQL as the primary database. Configure your connection:

```env
# PostgreSQL Configuration
DATABASE_URI=postgresql://username:password@localhost:5432/npi_cms
# Alternative format: postgres://username:password@localhost:5432/npi_cms

# For cloud databases (e.g., Supabase, AWS RDS)
DATABASE_URI=********************************/dbname?sslmode=require
```

## Browser Testing

### Quick API Testing
Visit `/test-api` in your browser for an interactive API testing dashboard:
- **URL**: `http://localhost:3000/test-api`
- **Features**: Test all endpoints, authentication, real-time results
- **Authentication**: Login directly from the testing interface

### Manual Browser Testing
Use browser developer tools (F12) to test endpoints:

```javascript
// Test public endpoints
fetch('/api/projects?featured=true')
  .then(r => r.json())
  .then(console.log)

// Test with authentication
fetch('/api/admin/projects', {
  headers: { 'Authorization': 'Bearer ' + localStorage.getItem('auth-token') }
})
.then(r => r.json())
.then(console.log)
```

## Authentication

### Public Endpoints
Most read endpoints are public and don't require authentication.

### Protected Endpoints
Admin endpoints require JWT authentication:

```http
Authorization: Bearer <jwt_token>
```

## Common Query Parameters

All list endpoints support these parameters:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number |
| `limit` | integer | 20 | Items per page (max 100) |
| `sort` | string | `-updatedAt` | Sort field and direction |
| `search` | string | - | Text search |
| `featured` | boolean | - | Filter featured items |

## Response Format

### Success Response
```json
{
  "data": [...],
  "totalDocs": 150,
  "page": 1,
  "limit": 20,
  "totalPages": 8,
  "hasNextPage": true,
  "hasPrevPage": false
}
```

### Error Response
```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "code": "ERROR_CODE",
  "statusCode": 400,
  "issues": [...],
  "timestamp": "2024-01-15T10:30:00.000Z",
  "requestId": "req_123456"
}
```

## Projects API

### List Projects
```http
GET /projects
```

**Query Parameters:**
- `category`: Filter by category
- `pillar`: Filter by strategic pillar
- `status`: Filter by status
- `county`: Filter by county ID
- `featured`: Filter featured projects (`true`/`false`)
- `search`: Text search across title, summary, description
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `sort`: Sort field and direction (e.g., `-createdAt`, `title`)

**Example:**
```http
GET /projects?category=community-empowerment&featured=true&limit=10&sort=-createdAt
```

**Response:**
```json
{
  "data": [
    {
      "id": "project-id",
      "title": "Community Empowerment Initiative",
      "summary": "Empowering local communities th
    ]
  },
  "location": {
    "counties": ["nairobi", "kiambu"],
    "specificLocation": "Nairobi and surrounding areas",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    }
  },
  "impact": {
    "beneficiaries": 1000,
    "communities": 5,
    "jobsCreated": 50,
    "metrics": [
      {
        "metric": "Training Sessions",
        "target": "30",
        "unit": "sessions"
      }
    ]
  },
  "team": {
    "projectManager": {
      "name": "John Doe",
      "role": "Project Manager",
      "contact": "<EMAIL>"
    }
  },
  "tags": [
    { "tag": "community" },
    { "tag": "empowerment" },
    { "tag": "sustainable" }
  ],
  "featured": false,
  "published": true,
  "slug": "new-community-project"
}
```
**Response:**
```json
{
  "success": true,
  "data": {
    "id": "new-project-id",
    "title": "New Community Project",
    "slug": "new-community-project",
    "status": "active",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "Project created successfully"
}
```
### Update Project (Admin)
```http
PUT /projects/:id
Content-Type: application/json
Authorization: Bearer <token>
```
**Required Permissions:** `update` permission or `editor` role

**Request Body (partial update):**
```json
{
  "title": "Updated Project Title",
  "status": "completed",
  "impact": {
    "beneficiaries": 1200,
    "communities": 6,
    "jobsCreated": 75,
    "metrics": [
      {
        "metric": "Training Sessions Completed",
        "value": "35",
        "unit": "sessions"
      }
    ]
  },
  "timeline": {
    "endDate": "2024-11-30T00:00:00.000Z",
    "milestones": [
      {
        "title": "Project Completion",
        "date": "2024-11-30T00:00:00.000Z",
        "completed": true,
        "description": "Project successfully completed"
      }
    ]
  }
}
```
### Delete Project (Admin)
```http
DELETE /projects/:id
Authorization: Bearer <token>
```
**Required Permissions:** `delete` permission or `admin` role

**Response:**
```json
{
  "success": true,
  "message": "Project deleted successfully"rough skill development",
      "category": "community-empowerment",
      "pillar": "community-innovation",
      "status": "active",
      "featured": true,
      "published": true,
      "slug": "community-empowerment-initiative",
      "timeline": {
        "startDate": "2024-01-01T00:00:00.000Z",
        "endDate": "2024-12-31T00:00:00.000Z"
      },
      "budget": {
        "totalBudget": 500000,
        "currency": "KES"
      },
      "location": {
        "counties": ["nairobi", "kiambu"],
        "specificLocation": "Nairobi and surrounding areas"
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ],
  "totalDocs": 25,
  "page": 1,
  "limit": 10,
  "totalPages": 3,
  "hasNextPage": true,
  "hasPrevPage": false
}
```

### Get Project
```http
GET /projects/:id
```

**Parameters:**
- `id`: Project ID or slug

**Response:**
```json
{
  "data": {
    "id": "project-id",
    "title": "Community Empowerment Initiative",
    "description": {
      "root": {
        "children": [...],
        "direction": "ltr",
        "format": "",
        "indent": 0,
        "type": "root",
        "version": 1
      }
    },
    "summary": "Empowering local communities through skill development",
    "category": "community-empowerment",
    "pillar": "community-innovation",
    "status": "active",
    "timeline": {
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-12-31T00:00:00.000Z",
      "milestones": [
        {
          "title": "Project Launch",
          "date": "2024-01-01T00:00:00.000Z",
          "completed": true
        }
      ]
    },
    "budget": {
      "totalBudget": 500000,
      "currency": "KES",
      "fundingSources": [
        {
          "source": "Government Grant",
          "amount": 300000,
          "percentage": 60
        }
      ]
    },
    "impact": {
      "beneficiaries": 1000,
      "communities": 5,
      "jobsCreated": 50,
      "metrics": [
        {
          "metric": "Training Sessions Conducted",
          "value": "25",
          "unit": "sessions"
        }
      ]
    },
    "team": {
      "projectManager": {
        "name": "John Doe",
        "role": "Project Manager",
        "contact": "<EMAIL>"
      },
      "members": [
        {
          "name": "Jane Smith",
          "role": "Community Coordinator",
          "expertise": "Community Engagement"
        }
      ]
    },
    "media": [
      {
        "id": "media-id",
        "filename": "project-image.jpg",
        "url": "https://storage.url/project-image.jpg",
        "alt": "Project activities in progress"
      }
    ],
    "tags": [
      { "tag": "community" },
      { "tag": "empowerment" },
      { "tag": "skills" }
    ],
    "featured": true,
    "published": true,
    "slug": "community-empowerment-initiative",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### Create Project (Admin)
```http
POST /projects
Content-Type: application/json
Authorization: Bearer <token>
```

**Required Permissions:** `create` permission or `content-manager` role

**Request Body:**
```json
{
  "title": "New Community Project",
  "description": {
    "root": {
      "children": [
        {
          "children": [
            {
              "detail": 0,
              "format": 0,
              "mode": "normal",
              "style": "",
              "text": "This project aims to empower local communities through sustainable development initiatives.",
              "type": "text",
              "version": 1
            }
          ],
          "direction": "ltr",
          "format": "",
          "indent": 0,
          "type": "paragraph",
          "version": 1
        }
      ],
      "direction": "ltr",
      "format": "",
      "indent": 0,
      "type": "root",
      "version": 1
    }
  },
  "summary": "Empowering communities through sustainable development",
  "category": "community-empowerment",
  "pillar": "community-innovation",
  "status": "active",
  "timeline": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T00:00:00.000Z",
    "milestones": [
      {
        "title": "Project Launch",
        "date": "2024-01-01T00:00:00.000Z",
        "description": "Official project launch ceremony"
      }
    ]
  },
  "budget": {
    "totalBudget": 500000,
    "currency": "KES",
    "fundingSources": [
      {
        "source": "Government Grant",
        "amount": 300000,
        "percentage": 60
      },
      {
        "source": "Private Donors",
        "amount": 200000,
        "percentage": 40
      }
    ]
  },
  "location": {
    "counties": ["nairobi", "kiambu"],
    "specificLocation": "Nairobi and surrounding areas",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    }
  },
  "impact": {
    "beneficiaries": 1000,
    "communities": 5,
    "jobsCreated": 50,
    "metrics": [
      {
        "metric": "Training Sessions",
        "target": "30",
        "unit": "sessions"
      }
    ]
  },
  "team": {
    "projectManager": {
      "name": "John Doe",
      "role": "Project Manager",
      "contact": "<EMAIL>"
    }
  },
  "tags": [
    { "tag": "community" },
    { "tag": "empowerment" },
    { "tag": "sustainable" }
  ],
  "featured": false,
  "published": true,
  "slug": "new-community-project"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "new-project-id",
    "title": "New Community Project",
    "slug": "new-community-project",
    "status": "active",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "Project created successfully"
}
```

### Update Project (Admin)
```http
PUT /projects/:id
Content-Type: application/json
Authorization: Bearer <token>
```

**Required Permissions:** `update` permission or `editor` role

**Request Body (partial update):**
```json
{
  "title": "Updated Project Title",
  "status": "completed",
  "impact": {
    "beneficiaries": 1200,
    "communities": 6,
    "jobsCreated": 75,
    "metrics": [
      {
        "metric": "Training Sessions Completed",
        "value": "35",
        "unit": "sessions"
      }
    ]
  },
  "timeline": {
    "endDate": "2024-11-30T00:00:00.000Z",
    "milestones": [
      {
        "title": "Project Completion",
        "date": "2024-11-30T00:00:00.000Z",
        "completed": true,
        "description": "Project successfully completed"
      }
    ]
  }
}
```

### Delete Project (Admin)
```http
DELETE /projects/:id
Authorization: Bearer <token>
```

**Required Permissions:** `delete` permission or `admin` role

**Response:**
```json
{
  "success": true,
  "message": "Project deleted successfully"
}
```

## Success Stories API

### List Success Stories
```http
GET /success-stories
```

**Query Parameters:**
- `category`: Filter by category
- `county`: Filter by county ID

### Get Success Story
```http
GET /success-stories/:id
```

## Resources API

### List Resources
```http
GET /resources
```

**Query Parameters:**
- `type`: Filter by resource type
- `category`: Filter by category
- `language`: Filter by language
- `access`: Filter by access level

### Get Resource
```http
GET /resources/:id
```

### Download Resource
```http
GET /resources/:id/download
```

**Response:**
```json
{
  "downloadUrl": "https://...",
  "filename": "resource.pdf",
  "filesize": 1024000,
  "mimeType": "application/pdf"
}
```

## News API

### List News Articles
```http
GET /news
```

**Query Parameters:**
- `category`: Filter by category
- `status`: Filter by status (admin only)
- `urgent`: Filter urgent news
- `author`: Filter by author name

### Get News Article
```http
GET /news/:id
```

## Media Gallery API

### List Media Items
```http
GET /media-gallery
```

**Query Parameters:**
- `type`: Filter by media type
- `category`: Filter by category
- `event`: Filter by event ID
- `project`: Filter by project ID
- `license`: Filter by license type

### Get Media Item
```http
GET /media-gallery/:id
```

## Partnerships API

### List Partnerships
```http
GET /partnerships
```

**Query Parameters:**
- `type`: Filter by partnership type
- `status`: Filter by status
- `partner`: Filter by partner ID

### Get Partnership
```http
GET /partnerships/:id
```

## Investment Opportunities API

### List Investment Opportunities
```http
GET /investment-opportunities
```

**Query Parameters:**
- `sector`: Filter by sector
- `investmentType`: Filter by investment type
- `status`: Filter by status
- `urgent`: Filter urgent opportunities
- `minAmount`: Minimum funding amount
- `maxAmount`: Maximum funding amount

### Get Investment Opportunity
```http
GET /investment-opportunities/:id
```

## Contact API

### Submit Contact Form
```http
POST /contact-submissions
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+254700000000",
  "organization": "Example Org",
  "role": "Manager",
  "subject": "Partnership Inquiry",
  "category": "partnership",
  "priority": "medium",
  "message": "I am interested in partnering with NPI...",
  "location": {
    "county": "county-id",
    "city": "Nairobi",
    "country": "Kenya"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Contact submission received successfully",
  "submissionId": "submission-id",
  "status": "new"
}
```

### List Contact Submissions (Admin)
```http
GET /contact-submissions
Authorization: Bearer <token>
```

### Get Contact Submission (Admin)
```http
GET /contact-submissions/:id
Authorization: Bearer <token>
```

### Update Contact Submission (Admin)
```http
PUT /contact-submissions/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "in-progress",
  "assignedTo": "user-id",
  "department": "partnerships",
  "priority": "high"
}
```

## Counties API

### List Counties
```http
GET /counties
```

### Get County
```http
GET /counties/:id
```

### Get Counties in Bounds
```http
GET /counties/bounds?north=1.0&south=-5.0&east=42.0&west=33.0
```

## Events API

### List Events
```http
GET /events
```

**Query Parameters:**
- `type`: Filter by event type
- `status`: Filter by status
- `county`: Filter by county
- `upcoming`: Filter upcoming events
- `featured`: Filter featured events

### Get Event
```http
GET /events/:id
```

## Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `VALIDATION_ERROR` | 400 | Input validation failed |
| `AUTHENTICATION_ERROR` | 401 | Authentication required |
| `AUTHORIZATION_ERROR` | 403 | Insufficient permissions |
| `NOT_FOUND_ERROR` | 404 | Resource not found |
| `CONFLICT_ERROR` | 409 | Resource conflict |
| `RATE_LIMIT_ERROR` | 429 | Rate limit exceeded |
| `INTERNAL_SERVER_ERROR` | 500 | Server error |
| `SERVICE_UNAVAILABLE_ERROR` | 503 | Service unavailable |

## Rate Limiting

API endpoints are rate limited:
- **Authenticated users**: 1000 requests per 15 minutes
- **Anonymous users**: 100 requests per 15 minutes
- **Contact form**: 5 submissions per hour per IP

## Data Types

### Media Object
```json
{
  "id": "media-id",
  "filename": "image.jpg",
  "url": "https://...",
  "alt": "Alt text",
  "width": 1920,
  "height": 1080,
  "mimeType": "image/jpeg",
  "filesize": 1024000
}
```

### County Object
```json
{
  "id": "county-id",
  "name": "Nairobi",
  "code": "047"
}
```

### Rich Text Object
```json
{
  "root": {
    "children": [...],
    "direction": "ltr",
    "format": "",
    "indent": 0,
    "type": "root",
    "version": 1
  }
}
```

## Examples

### Fetch Featured Projects
```javascript
const response = await fetch('/api/projects?featured=true&limit=6')
const { data: projects } = await response.json()
```

### Submit Contact Form
```javascript
const response = await fetch('/api/contact-submissions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    subject: 'Partnership Inquiry',
    category: 'partnership',
    message: 'I am interested in partnering with NPI...'
  })
})

const result = await response.json()
```

### Search Resources
```javascript
const response = await fetch('/api/resources?search=traditional medicine&type=research-report')
const { data: resources } = await response.json()
```

### Get Project with Error Handling
```javascript
try {
  const response = await fetch('/api/projects/project-slug')
  
  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message)
  }
  
  const { data: project } = await response.json()
  console.log(project)
} catch (error) {
  console.error('Failed to fetch project:', error.message)
}
```

## SDK Usage

### JavaScript/TypeScript
```javascript
import { cmsAPI } from '@/lib/cms'

// Fetch projects
const projects = await cmsAPI.projects.getAll({
  category: 'community-empowerment',
  featured: true
})

// Submit contact form
await cmsAPI.contact.submit({
  name: 'John Doe',
  email: '<EMAIL>',
  subject: 'Partnership Inquiry',
  category: 'partnership',
  message: 'I am interested in partnering...'
})
```

### React Hooks
```javascript
import { useProjects, useContactForm } from '@/lib/cms'

function ProjectsList() {
  const { data: projects, loading, error } = useProjects({
    featured: true,
    limit: 6
  })

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>

  return (
    <div>
      {projects.map(project => (
        <div key={project.id}>{project.title}</div>
      ))}
    </div>
  )
}

function ContactForm() {
  const { submitForm, loading, error, success } = useContactForm()

  const handleSubmit = async (formData) => {
    await submitForm(formData)
  }

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      {error && <div>Error: {error}</div>}
      {success && <div>Form submitted successfully!</div>}
    </form>
  )
}
```

---

For more detailed information, see the [CMS Documentation](./CMS_DOCUMENTATION.md).
