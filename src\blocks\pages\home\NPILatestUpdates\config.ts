import type { Block } from 'payload'

export const NPILatestUpdates: Block = {
  slug: 'npiLatestUpdates',
  interfaceName: 'NPILatestUpdatesBlock',
  fields: [
    {
      name: 'title',
      type: 'text',
      defaultValue: 'Latest Updates',
      label: 'Title',
    },
    {
      name: 'description',
      type: 'text',
      defaultValue: 'Stay informed about our latest developments and achievements.',
      label: 'Description',
    },
  ],
  labels: {
    plural: 'NPI Latest Updates Blocks',
    singular: 'NPI Latest Updates Block',
  },
}
