import type { Payload } from 'payload'

export const seedCMSData = async (payload: Payload): Promise<void> => {
  payload.logger.info('Seeding CMS data...')

  try {
    // Create sample projects
    const sampleProjects = [
      {
        title: 'Indigenous Knowledge Documentation Initiative',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'A comprehensive project to document and preserve traditional knowledge systems of indigenous communities in Kenya, focusing on medicinal plants, agricultural practices, and cultural heritage.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Documenting and preserving traditional knowledge systems of indigenous communities in Kenya.',
        category: 'knowledge-preservation',
        pillar: 'indigenous-knowledge',
        status: 'active',
        timeline: {
          startDate: '2023-01-01',
          endDate: '2025-12-31',
          duration: '3 years',
        },
        budget: {
          totalBudget: 15000000,
          currency: 'KES',
        },
        impact: {
          beneficiaries: 5000,
          communities: 25,
          jobsCreated: 50,
        },
        featured: true,
        published: true,
        tags: [
          { tag: 'indigenous knowledge' },
          { tag: 'documentation' },
          { tag: 'preservation' },
          { tag: 'traditional medicine' },
        ],
        slug: 'indigenous-knowledge-documentation-initiative',
      },
      {
        title: 'Community-Led Natural Products Development',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Empowering local communities to develop sustainable natural products businesses based on traditional knowledge and local resources.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Empowering communities to develop sustainable natural products businesses.',
        category: 'community-empowerment',
        pillar: 'community-innovation',
        status: 'active',
        timeline: {
          startDate: '2023-06-01',
          endDate: '2026-05-31',
          duration: '3 years',
        },
        budget: {
          totalBudget: 20000000,
          currency: 'KES',
        },
        impact: {
          beneficiaries: 3000,
          communities: 15,
          jobsCreated: 75,
        },
        featured: true,
        published: true,
        tags: [
          { tag: 'community development' },
          { tag: 'natural products' },
          { tag: 'entrepreneurship' },
          { tag: 'sustainability' },
        ],
        slug: 'community-led-natural-products-development',
      },
    ]

    for (const project of sampleProjects) {
      await payload.create({
        collection: 'projects',
        data: project,
      })
    }

    // Create sample success stories
    const sampleSuccessStories = [
      {
        title: 'From Traditional Healer to Certified Entrepreneur',
        summary: 'Mary Wanjiku transformed her traditional healing practice into a certified natural products business.',
        content: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Mary Wanjiku, a traditional healer from Kiambu County, has successfully transformed her ancestral knowledge into a thriving natural products business. Through NPI\'s capacity building program, she learned modern business practices while preserving traditional knowledge.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        category: 'economic-empowerment',
        participants: {
          beneficiary: {
            name: 'Mary Wanjiku',
            role: 'Traditional Healer & Entrepreneur',
            organization: 'Wanjiku Natural Products',
          },
          knowledgeHolder: {
            name: 'Mary Wanjiku',
            title: 'Traditional Healer',
            expertise: 'Medicinal Plants & Traditional Medicine',
          },
        },
        impact: {
          beneficiaries: 200,
          jobsCreated: 5,
          incomeIncrease: {
            percentage: 300,
            currency: 'KES',
          },
        },
        timeline: {
          startDate: '2022-03-01',
          completionDate: '2023-12-31',
          duration: '22 months',
        },
        investment: {
          totalAmount: 500000,
          currency: 'KES',
        },
        featured: true,
        published: true,
        tags: [
          { tag: 'entrepreneurship' },
          { tag: 'traditional medicine' },
          { tag: 'women empowerment' },
          { tag: 'business development' },
        ],
        slug: 'traditional-healer-to-certified-entrepreneur',
      },
    ]

    for (const story of sampleSuccessStories) {
      await payload.create({
        collection: 'success-stories',
        data: story,
      })
    }

    // Create sample resources
    const sampleResources = [
      {
        title: 'Traditional Medicine Business Development Guide',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'A comprehensive guide for traditional healers and practitioners looking to develop sustainable businesses while preserving indigenous knowledge.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Comprehensive guide for traditional healers to develop sustainable businesses.',
        type: 'training-guide',
        category: 'indigenous-knowledge',
        metadata: {
          authors: [
            {
              name: 'Dr. Jane Muthoni',
              organization: 'Natural Products Institute',
              role: 'Senior Researcher',
            },
          ],
          publishDate: '2023-08-15',
          version: '2.0',
          language: 'en',
          pageCount: 45,
          fileSize: '2.3 MB',
        },
        access: {
          level: 'public',
          requiresRegistration: false,
        },
        analytics: {
          downloadCount: 0,
          viewCount: 0,
        },
        keywords: [
          { keyword: 'traditional medicine' },
          { keyword: 'business development' },
          { keyword: 'entrepreneurship' },
          { keyword: 'indigenous knowledge' },
        ],
        featured: true,
        published: true,
        slug: 'traditional-medicine-business-development-guide',
      },
    ]

    for (const resource of sampleResources) {
      await payload.create({
        collection: 'resources',
        data: resource,
      })
    }

    // Create sample news articles
    const sampleNews = [
      {
        title: 'NPI Launches New Indigenous Knowledge Documentation Program',
        subtitle: 'Preserving Traditional Wisdom for Future Generations',
        summary: 'The Natural Products Institute announces a groundbreaking initiative to document and preserve indigenous knowledge systems across Kenya.',
        content: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'NAIROBI, Kenya - The Natural Products Institute (NPI) today announced the launch of its comprehensive Indigenous Knowledge Documentation Program, a three-year initiative aimed at preserving traditional knowledge systems across Kenya\'s diverse communities.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        category: 'news',
        status: 'published',
        publishDate: '2024-01-15',
        author: {
          name: 'Communications Team',
          role: 'Communications Manager',
          organization: 'Natural Products Institute',
        },
        engagement: {
          allowComments: true,
          socialSharing: true,
          newsletter: true,
        },
        analytics: {
          viewCount: 0,
          shareCount: 0,
        },
        featured: true,
        urgent: false,
        tags: [
          { tag: 'indigenous knowledge' },
          { tag: 'documentation' },
          { tag: 'program launch' },
          { tag: 'preservation' },
        ],
        slug: 'npi-launches-indigenous-knowledge-documentation-program',
      },
    ]

    for (const article of sampleNews) {
      await payload.create({
        collection: 'news',
        data: article,
      })
    }

    // Create sample investment opportunities
    const sampleInvestmentOpportunities = [
      {
        title: 'Natural Cosmetics Manufacturing Facility',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Investment opportunity to establish a state-of-the-art natural cosmetics manufacturing facility utilizing indigenous plant-based ingredients.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        summary: 'Establish a natural cosmetics manufacturing facility using indigenous ingredients.',
        sector: 'cosmetics-personal-care',
        investmentType: 'equity',
        status: 'open',
        financial: {
          fundingRequired: 50000000,
          currency: 'KES',
          expectedReturns: {
            roi: 25,
            paybackPeriod: '4-5 years',
          },
        },
        businessModel: {
          valueProposition: {
            root: {
              children: [
                {
                  children: [
                    {
                      detail: 0,
                      format: 0,
                      mode: 'normal',
                      style: '',
                      text: 'Unique natural cosmetics products based on traditional Kenyan ingredients with proven efficacy and cultural significance.',
                      type: 'text',
                      version: 1,
                    },
                  ],
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  type: 'paragraph',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'root',
              version: 1,
            },
          },
          targetMarket: {
            root: {
              children: [
                {
                  children: [
                    {
                      detail: 0,
                      format: 0,
                      mode: 'normal',
                      style: '',
                      text: 'Growing natural and organic cosmetics market in East Africa and international export markets.',
                      type: 'text',
                      version: 1,
                    },
                  ],
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  type: 'paragraph',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'root',
              version: 1,
            },
          },
        },
        location: {
          counties: [], // Will be populated with actual county IDs
          specificLocation: 'Kiambu County Industrial Area',
        },
        impact: {
          beneficiaries: 1000,
          jobsCreated: 150,
        },
        featured: true,
        urgent: false,
        tags: [
          { tag: 'cosmetics' },
          { tag: 'manufacturing' },
          { tag: 'natural products' },
          { tag: 'investment' },
        ],
        slug: 'natural-cosmetics-manufacturing-facility',
      },
    ]

    for (const opportunity of sampleInvestmentOpportunities) {
      await payload.create({
        collection: 'investment-opportunities',
        data: opportunity,
      })
    }

    payload.logger.info('CMS data seeding completed successfully!')
  } catch (error) {
    payload.logger.error('Error seeding CMS data:', error)
    throw error
  }
}
