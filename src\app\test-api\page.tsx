'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth'

interface ApiTestResult {
  endpoint: string
  method: string
  status: number
  success: boolean
  data?: any
  error?: string
  duration: number
}

export default function ApiTestPage() {
  const { isAuthenticated, user, login } = useAuth()
  const [testResults, setTestResults] = useState<ApiTestResult[]>([])
  const [loading, setLoading] = useState(false)
  const [loginForm, setLoginForm] = useState({ email: '', password: '' })

  // Test endpoints configuration
  const testEndpoints = [
    // Public endpoints
    { endpoint: '/api/projects', method: 'GET', requiresAuth: false, description: 'Get all projects' },
    { endpoint: '/api/projects?featured=true', method: 'GET', requiresAuth: false, description: 'Get featured projects' },
    { endpoint: '/api/success-stories', method: 'GET', requiresAuth: false, description: 'Get success stories' },
    { endpoint: '/api/resources', method: 'GET', requiresAuth: false, description: 'Get resources' },
    { endpoint: '/api/news', method: 'GET', requiresAuth: false, description: 'Get news articles' },
    { endpoint: '/api/media-gallery', method: 'GET', requiresAuth: false, description: 'Get media items' },
    { endpoint: '/api/partnerships', method: 'GET', requiresAuth: false, description: 'Get partnerships' },
    { endpoint: '/api/investment-opportunities', method: 'GET', requiresAuth: false, description: 'Get investment opportunities' },
    { endpoint: '/api/counties', method: 'GET', requiresAuth: false, description: 'Get counties' },
    
    // Contact form (public POST)
    { 
      endpoint: '/api/contact-submissions', 
      method: 'POST', 
      requiresAuth: false, 
      description: 'Submit contact form',
      body: {
        name: 'Test User',
        email: '<EMAIL>',
        subject: 'API Test Submission',
        category: 'general',
        message: 'This is a test message from the API testing page.',
        priority: 'medium'
      }
    },
    
    // Protected endpoints (require authentication)
    { endpoint: '/api/admin/projects', method: 'GET', requiresAuth: true, description: 'Get all projects (admin)' },
    { endpoint: '/api/admin/contact-submissions', method: 'GET', requiresAuth: true, description: 'Get contact submissions (admin)' },
    { endpoint: '/api/admin/users', method: 'GET', requiresAuth: true, description: 'Get users (admin)' },
  ]

  const runApiTest = async (test: typeof testEndpoints[0]): Promise<ApiTestResult> => {
    const startTime = Date.now()
    
    try {
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      }
      
      // Add auth header if required and user is authenticated
      if (test.requiresAuth && isAuthenticated) {
        const token = localStorage.getItem('auth-token')
        if (token) {
          headers['Authorization'] = `Bearer ${token}`
        }
      }

      const response = await fetch(test.endpoint, {
        method: test.method,
        headers,
        ...(test.body && { body: JSON.stringify(test.body) }),
      })

      const duration = Date.now() - startTime
      const data = await response.json()

      return {
        endpoint: test.endpoint,
        method: test.method,
        status: response.status,
        success: response.ok,
        data,
        duration,
      }
    } catch (error) {
      const duration = Date.now() - startTime
      return {
        endpoint: test.endpoint,
        method: test.method,
        status: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
      }
    }
  }

  const runAllTests = async () => {
    setLoading(true)
    setTestResults([])
    
    const results: ApiTestResult[] = []
    
    for (const test of testEndpoints) {
      // Skip auth-required tests if not authenticated
      if (test.requiresAuth && !isAuthenticated) {
        results.push({
          endpoint: test.endpoint,
          method: test.method,
          status: 401,
          success: false,
          error: 'Authentication required',
          duration: 0,
        })
        continue
      }
      
      const result = await runApiTest(test)
      results.push(result)
      setTestResults([...results]) // Update UI progressively
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    setLoading(false)
  }

  const runSingleTest = async (test: typeof testEndpoints[0]) => {
    const result = await runApiTest(test)
    setTestResults(prev => {
      const filtered = prev.filter(r => r.endpoint !== test.endpoint || r.method !== test.method)
      return [...filtered, result]
    })
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await login(loginForm.email, loginForm.password)
    } catch (error) {
      console.error('Login failed:', error)
      alert('Login failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }

  const getStatusColor = (status: number, success: boolean) => {
    if (status === 0) return 'text-gray-500'
    if (success) return 'text-green-600'
    if (status >= 400 && status < 500) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getStatusBadgeColor = (status: number, success: boolean) => {
    if (status === 0) return 'bg-gray-100 text-gray-800'
    if (success) return 'bg-green-100 text-green-800'
    if (status >= 400 && status < 500) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">CMS API Testing Dashboard</h1>
        
        {/* Authentication Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          
          {isAuthenticated ? (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-green-700 font-medium">Authenticated</span>
                <span className="text-gray-600">
                  User: {user?.name} ({user?.email}) - Role: {user?.role}
                </span>
              </div>
              <button
                onClick={() => window.location.href = '/admin'}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Go to Admin Panel
              </button>
            </div>
          ) : (
            <div>
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-red-700 font-medium">Not Authenticated</span>
              </div>
              
              <form onSubmit={handleLogin} className="flex space-x-4">
                <input
                  type="email"
                  placeholder="Email"
                  value={loginForm.email}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, email: e.target.value }))}
                  className="px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                  required
                />
                <input
                  type="password"
                  placeholder="Password"
                  value={loginForm.password}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                  className="px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                  required
                />
                <button
                  type="submit"
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                >
                  Login
                </button>
              </form>
              
              <p className="text-sm text-gray-600 mt-2">
                Default admin: <EMAIL> / admin123
              </p>
            </div>
          )}
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">API Tests</h2>
            <button
              onClick={runAllTests}
              disabled={loading}
              className="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600 disabled:opacity-50"
            >
              {loading ? 'Running Tests...' : 'Run All Tests'}
            </button>
          </div>
          
          <p className="text-gray-600 mb-4">
            Test all API endpoints to verify functionality. Protected endpoints require authentication.
          </p>
        </div>

        {/* Test Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-6">Test Results</h2>
          
          {testResults.length === 0 && !loading && (
            <p className="text-gray-500 text-center py-8">
              No tests run yet. Click "Run All Tests" to start testing.
            </p>
          )}
          
          <div className="space-y-4">
            {testEndpoints.map((test, index) => {
              const result = testResults.find(r => r.endpoint === test.endpoint && r.method === test.method)
              
              return (
                <div key={`${test.endpoint}-${test.method}`} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-4">
                      <span className={`font-mono text-sm px-2 py-1 rounded ${
                        test.method === 'GET' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {test.method}
                      </span>
                      <span className="font-mono text-sm">{test.endpoint}</span>
                      {test.requiresAuth && (
                        <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">
                          Auth Required
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      {result && (
                        <>
                          <span className={`px-2 py-1 rounded text-sm ${getStatusBadgeColor(result.status, result.success)}`}>
                            {result.status || 'Error'}
                          </span>
                          <span className="text-sm text-gray-500">
                            {result.duration}ms
                          </span>
                        </>
                      )}
                      <button
                        onClick={() => runSingleTest(test)}
                        className="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600"
                      >
                        Test
                      </button>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-2">{test.description}</p>
                  
                  {result && (
                    <div className="mt-3 p-3 bg-gray-50 rounded">
                      {result.success ? (
                        <div>
                          <p className="text-green-600 font-medium mb-2">✓ Success</p>
                          {result.data && (
                            <details className="text-sm">
                              <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                                View Response Data
                              </summary>
                              <pre className="mt-2 p-2 bg-white rounded border overflow-x-auto text-xs">
                                {JSON.stringify(result.data, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      ) : (
                        <div>
                          <p className="text-red-600 font-medium mb-2">✗ Failed</p>
                          {result.error && (
                            <p className="text-red-600 text-sm">{result.error}</p>
                          )}
                          {result.data && (
                            <details className="text-sm mt-2">
                              <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                                View Error Response
                              </summary>
                              <pre className="mt-2 p-2 bg-white rounded border overflow-x-auto text-xs">
                                {JSON.stringify(result.data, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Test Summary */}
        {testResults.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mt-8">
            <h2 className="text-xl font-semibold mb-4">Test Summary</h2>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-800">
                  {testResults.length}
                </div>
                <div className="text-sm text-gray-600">Total Tests</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {testResults.filter(r => r.success).length}
                </div>
                <div className="text-sm text-gray-600">Passed</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {testResults.filter(r => !r.success).length}
                </div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(testResults.reduce((sum, r) => sum + r.duration, 0) / testResults.length)}ms
                </div>
                <div className="text-sm text-gray-600">Avg Response</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
